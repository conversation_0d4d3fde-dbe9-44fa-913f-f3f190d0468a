<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dashboard - Virtual LMS</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/components.css">
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: {"50":"#eff6ff","100":"#dbeafe","200":"#bfdbfe","300":"#93c5fd","400":"#60a5fa","500":"#3b82f6","600":"#2563eb","700":"#1d4ed8","800":"#1e40af","900":"#1e3a8a","950":"#172554"},
              secondary: {"50":"#f0f9ff","100":"#e0f2fe","200":"#bae6fd","300":"#7dd3fc","400":"#38bdf8","500":"#0ea5e9","600":"#0284c7","700":"#0369a1","800":"#075985","900":"#0c4a6e","950":"#082f49"},
              accent: {"50":"#f5f3ff","100":"#ede9fe","200":"#ddd6fe","300":"#c4b5fd","400":"#a78bfa","500":"#8b5cf6","600":"#7c3aed","700":"#6d28d9","800":"#5b21b6","900":"#4c1d95","950":"#2e1065"}
            }
          }
        }
      }
    </script>
     <script type="importmap">
      {
        "imports": {
          "@google/genai": "https://esm.sh/@google/genai@^1.3.0"
        }
      }
    </script>
</head>
<body id="dashboard-page" class="bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-gray-100 flex flex-col min-h-screen">
    <header id="main-header" class="fixed w-full z-50 top-0">
        <!-- Navbar will be injected by js/main.js -->
    </header>

    <main class="flex-grow pt-20 pb-8 container mx-auto px-4 sm:px-6 lg:px-8">
        <div id="dashboard-content" class="space-y-6">
            <!-- Dashboard content will be rendered here by js/dashboard.js -->
            <div class="text-center py-10">
                <p class="text-2xl text-gray-500 dark:text-gray-400">Loading Dashboard...</p>
            </div>
        </div>
    </main>

    <footer id="main-footer-container" class="bg-gray-200 dark:bg-gray-800 text-gray-700 dark:text-gray-300 py-6 text-center shadow-inner">
        <!-- Footer will be injected by js/main.js -->
    </footer>

    <script type="module" src="js/main.js"></script>
    <script type="module" src="js/dashboard.js"></script>
</body>
</html>
