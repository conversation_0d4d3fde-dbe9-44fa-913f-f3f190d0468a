<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dashboard - Virtual LMS</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/style.css">
    <script>
      tailwind.config = { /* Tailwind config from index.html */ } 
    </script>
     <script type="importmap">
      {
        "imports": {
          "@google/genai": "https://esm.sh/@google/genai@^1.3.0"
        }
      }
    </script>
</head>
<body id="dashboard-page" class="bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-gray-100 flex flex-col min-h-screen">
    <header id="main-header" class="fixed w-full z-50 top-0">
        <!-- Navbar will be injected by js/main.js -->
    </header>

    <main class="flex-grow pt-20 pb-8 container mx-auto px-4 sm:px-6 lg:px-8">
        <div id="dashboard-content" class="space-y-6">
            <!-- Dashboard content will be rendered here by js/dashboard.js -->
            <div class="text-center py-10">
                <p class="text-2xl text-gray-500 dark:text-gray-400">Loading Dashboard...</p>
            </div>
        </div>
    </main>

    <footer id="main-footer-container" class="bg-gray-200 dark:bg-gray-800 text-gray-700 dark:text-gray-300 py-6 text-center shadow-inner">
        <!-- Footer will be injected by js/main.js -->
    </footer>

    <script type="module" src="js/main.js"></script>
    <script type="module" src="js/dashboard.js"></script>
</body>
</html>
