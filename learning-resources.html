<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Learning Resources - Physics Virtual Lab</title>
    <meta name="author" content="Dr<PERSON> <PERSON>, SUST-BME">
    <meta name="copyright" content="Dr. <PERSON> Esmail - +************, +************">
    <meta name="institution" content="Sudan University of Science and Technology - Biomedical Engineering">
    <meta name="description" content="Comprehensive learning resources for physics education including formulas, concepts, and study guides">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/components.css">
    <style>
        body {
            font-family: var(--font-family-sans);
            margin: 0;
            padding: 0;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .resources-header {
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
            color: white;
            padding: var(--spacing-3xl) 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .resources-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.1);
            z-index: 1;
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        .resources-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: var(--spacing-md);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .resources-subtitle {
            font-size: 1.25rem;
            opacity: 0.9;
            max-width: 700px;
            margin: 0 auto;
        }

        .main-content {
            flex: 1;
            max-width: 1400px;
            margin: 0 auto;
            padding: var(--spacing-xl);
            width: 100%;
        }

        .resources-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
        }

        .resource-card {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            transition: all var(--transition-normal);
        }

        [data-theme="dark"] .resource-card {
            background: var(--gray-700);
        }

        .resource-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-xl);
        }

        .resource-header {
            padding: var(--spacing-lg);
            background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
            color: white;
            text-align: center;
        }

        .resource-icon {
            font-size: 3rem;
            margin-bottom: var(--spacing-sm);
        }

        .resource-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
        }

        .resource-content {
            padding: var(--spacing-lg);
        }

        .resource-description {
            color: var(--text-secondary);
            margin-bottom: var(--spacing-lg);
            line-height: 1.6;
        }

        .resource-list {
            list-style: none;
            padding: 0;
            margin: 0 0 var(--spacing-lg) 0;
        }

        .resource-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm);
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-sm);
            transition: all var(--transition-fast);
        }

        .resource-item:hover {
            background: var(--accent-50);
        }

        [data-theme="dark"] .resource-item:hover {
            background: var(--accent-900);
        }

        .resource-item-icon {
            font-size: 1.25rem;
            color: var(--primary-600);
        }

        .resource-item-text {
            flex: 1;
            color: var(--text-primary);
        }

        .resource-actions {
            display: flex;
            gap: var(--spacing-sm);
        }

        .action-btn {
            flex: 1;
            padding: var(--spacing-sm) var(--spacing-md);
            border: none;
            border-radius: var(--radius-md);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-fast);
            text-decoration: none;
            text-align: center;
            font-size: 0.875rem;
        }

        .btn-primary {
            background: var(--primary-600);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-700);
        }

        .btn-secondary {
            background: var(--gray-200);
            color: var(--text-primary);
        }

        [data-theme="dark"] .btn-secondary {
            background: var(--gray-600);
        }

        .btn-secondary:hover {
            background: var(--gray-300);
        }

        [data-theme="dark"] .btn-secondary:hover {
            background: var(--gray-500);
        }

        .formula-section {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            padding: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
        }

        [data-theme="dark"] .formula-section {
            background: var(--gray-700);
        }

        .section-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--spacing-lg);
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
        }

        .formula-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-lg);
        }

        .formula-card {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            text-align: center;
        }

        .formula-category {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--primary-600);
            margin-bottom: var(--spacing-md);
        }

        .formula {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
            font-family: 'Times New Roman', serif;
        }

        .formula-description {
            color: var(--text-secondary);
            font-size: 0.875rem;
            margin-bottom: var(--spacing-md);
        }

        .formula-variables {
            background: var(--accent-50);
            border-radius: var(--radius-md);
            padding: var(--spacing-sm);
            font-size: 0.875rem;
        }

        [data-theme="dark"] .formula-variables {
            background: var(--accent-900);
        }

        .quick-links {
            background: var(--primary-50);
            border: 1px solid var(--primary-200);
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            text-align: center;
        }

        [data-theme="dark"] .quick-links {
            background: var(--primary-900);
            border-color: var(--primary-700);
        }

        .quick-links-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--primary-700);
            margin-bottom: var(--spacing-lg);
        }

        [data-theme="dark"] .quick-links-title {
            color: var(--primary-300);
        }

        .quick-links-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
        }

        .quick-link {
            background: white;
            border-radius: var(--radius-lg);
            padding: var(--spacing-md);
            text-decoration: none;
            color: var(--text-primary);
            transition: all var(--transition-fast);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-sm);
        }

        [data-theme="dark"] .quick-link {
            background: var(--gray-800);
        }

        .quick-link:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-md);
        }

        .quick-link-icon {
            font-size: 2rem;
        }

        .quick-link-text {
            font-weight: 500;
        }

        @media (max-width: 768px) {
            .resources-grid {
                grid-template-columns: 1fr;
            }
            
            .formula-grid {
                grid-template-columns: 1fr;
            }
            
            .quick-links-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <header id="main-header"></header>

    <div class="resources-header animate-fade-in">
        <div class="header-content">
            <h1 class="resources-title">📚 Learning Resources</h1>
            <p class="resources-subtitle">
                Comprehensive study materials, formulas, and guides to enhance your physics learning journey
            </p>
        </div>
    </div>

    <div class="main-content">
        <div class="resources-grid animate-slide-up">
            <div class="resource-card">
                <div class="resource-header">
                    <div class="resource-icon">📖</div>
                    <h2 class="resource-title">Study Guides</h2>
                </div>
                <div class="resource-content">
                    <p class="resource-description">
                        Comprehensive study guides covering all major physics topics with clear explanations and examples.
                    </p>
                    <ul class="resource-list">
                        <li class="resource-item">
                            <span class="resource-item-icon">⚡</span>
                            <span class="resource-item-text">Electromagnetism Guide</span>
                        </li>
                        <li class="resource-item">
                            <span class="resource-item-icon">🔬</span>
                            <span class="resource-item-text">Mechanics Fundamentals</span>
                        </li>
                        <li class="resource-item">
                            <span class="resource-item-icon">🌊</span>
                            <span class="resource-item-text">Waves & Oscillations</span>
                        </li>
                        <li class="resource-item">
                            <span class="resource-item-icon">🌡️</span>
                            <span class="resource-item-text">Thermodynamics Basics</span>
                        </li>
                    </ul>
                    <div class="resource-actions">
                        <a href="#study-guides" class="action-btn btn-primary">View Guides</a>
                        <a href="dashboard.html" class="action-btn btn-secondary">Track Progress</a>
                    </div>
                </div>
            </div>

            <div class="resource-card">
                <div class="resource-header">
                    <div class="resource-icon">🧮</div>
                    <h2 class="resource-title">Formula Reference</h2>
                </div>
                <div class="resource-content">
                    <p class="resource-description">
                        Quick reference for essential physics formulas organized by topic with variable definitions.
                    </p>
                    <ul class="resource-list">
                        <li class="resource-item">
                            <span class="resource-item-icon">⚡</span>
                            <span class="resource-item-text">Electrical Formulas</span>
                        </li>
                        <li class="resource-item">
                            <span class="resource-item-icon">🚀</span>
                            <span class="resource-item-text">Motion Equations</span>
                        </li>
                        <li class="resource-item">
                            <span class="resource-item-icon">🌊</span>
                            <span class="resource-item-text">Wave Equations</span>
                        </li>
                        <li class="resource-item">
                            <span class="resource-item-icon">🔥</span>
                            <span class="resource-item-text">Heat Transfer</span>
                        </li>
                    </ul>
                    <div class="resource-actions">
                        <a href="#formulas" class="action-btn btn-primary">View Formulas</a>
                        <a href="assessment.html" class="action-btn btn-secondary">Practice Problems</a>
                    </div>
                </div>
            </div>

            <div class="resource-card">
                <div class="resource-header">
                    <div class="resource-icon">🎬</div>
                    <h2 class="resource-title">Video Tutorials</h2>
                </div>
                <div class="resource-content">
                    <p class="resource-description">
                        Interactive video lessons with animations and real-world applications of physics concepts.
                    </p>
                    <ul class="resource-list">
                        <li class="resource-item">
                            <span class="resource-item-icon">📹</span>
                            <span class="resource-item-text">Concept Explanations</span>
                        </li>
                        <li class="resource-item">
                            <span class="resource-item-icon">🎮</span>
                            <span class="resource-item-text">Interactive Elements</span>
                        </li>
                        <li class="resource-item">
                            <span class="resource-item-icon">📝</span>
                            <span class="resource-item-text">Note-taking Tools</span>
                        </li>
                        <li class="resource-item">
                            <span class="resource-item-icon">🎯</span>
                            <span class="resource-item-text">Targeted Learning</span>
                        </li>
                    </ul>
                    <div class="resource-actions">
                        <a href="video-library.html" class="action-btn btn-primary">Watch Videos</a>
                        <a href="interactive-video.html" class="action-btn btn-secondary">Interactive Player</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="formula-section animate-slide-up animate-stagger-1">
            <h2 class="section-title">🧮 Essential Physics Formulas</h2>
            <div class="formula-grid">
                <div class="formula-card">
                    <div class="formula-category">Electromagnetism</div>
                    <div class="formula">V = IR</div>
                    <div class="formula-description">Ohm's Law</div>
                    <div class="formula-variables">
                        V = Voltage (V)<br>
                        I = Current (A)<br>
                        R = Resistance (Ω)
                    </div>
                </div>

                <div class="formula-card">
                    <div class="formula-category">Mechanics</div>
                    <div class="formula">F = ma</div>
                    <div class="formula-description">Newton's Second Law</div>
                    <div class="formula-variables">
                        F = Force (N)<br>
                        m = Mass (kg)<br>
                        a = Acceleration (m/s²)
                    </div>
                </div>

                <div class="formula-card">
                    <div class="formula-category">Waves</div>
                    <div class="formula">v = fλ</div>
                    <div class="formula-description">Wave Speed</div>
                    <div class="formula-variables">
                        v = Wave speed (m/s)<br>
                        f = Frequency (Hz)<br>
                        λ = Wavelength (m)
                    </div>
                </div>

                <div class="formula-card">
                    <div class="formula-category">Thermodynamics</div>
                    <div class="formula">Q = mcΔT</div>
                    <div class="formula-description">Heat Transfer</div>
                    <div class="formula-variables">
                        Q = Heat energy (J)<br>
                        m = Mass (kg)<br>
                        c = Specific heat (J/kg·K)<br>
                        ΔT = Temperature change (K)
                    </div>
                </div>
            </div>
        </div>

        <div class="quick-links animate-slide-up animate-stagger-2">
            <h2 class="quick-links-title">🚀 Quick Access</h2>
            <div class="quick-links-grid">
                <a href="experiments-hub.html" class="quick-link">
                    <div class="quick-link-icon">🧪</div>
                    <div class="quick-link-text">Experiments</div>
                </a>
                <a href="video-library.html" class="quick-link">
                    <div class="quick-link-icon">🎬</div>
                    <div class="quick-link-text">Videos</div>
                </a>
                <a href="assessment.html" class="quick-link">
                    <div class="quick-link-icon">🎓</div>
                    <div class="quick-link-text">Assessment</div>
                </a>
                <a href="learning-paths.html" class="quick-link">
                    <div class="quick-link-icon">🎯</div>
                    <div class="quick-link-text">Learning Paths</div>
                </a>
                <a href="dashboard.html" class="quick-link">
                    <div class="quick-link-icon">📊</div>
                    <div class="quick-link-text">Dashboard</div>
                </a>
                <a href="sitemap.html" class="quick-link">
                    <div class="quick-link-icon">🗺️</div>
                    <div class="quick-link-text">Site Map</div>
                </a>
            </div>
        </div>
    </div>

    <footer id="main-footer-container"></footer>

    <script type="module" src="js/main.js"></script>
</body>
</html>
