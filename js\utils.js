// Physics Virtual Lab - Utility Functions

export class Utils {
  constructor() {
    this.notificationContainer = null;
    this.loadingOverlay = null;
  }

  // Debounce function for performance optimization
  debounce(func, wait, immediate = false) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        timeout = null;
        if (!immediate) func(...args);
      };
      const callNow = immediate && !timeout;
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
      if (callNow) func(...args);
    };
  }

  // Throttle function for performance optimization
  throttle(func, limit) {
    let inThrottle;
    return function(...args) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  // Format numbers with appropriate units
  formatNumber(num, decimals = 2) {
    if (num === 0) return '0';
    
    const k = 1000;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['', 'K', 'M', 'G', 'T'];
    
    const i = Math.floor(Math.log(Math.abs(num)) / Math.log(k));
    
    if (i === 0) {
      return parseFloat(num.toFixed(dm)).toString();
    }
    
    return parseFloat((num / Math.pow(k, i)).toFixed(dm)) + sizes[i];
  }

  // Format scientific notation
  formatScientific(num, decimals = 2) {
    if (num === 0) return '0';
    return num.toExponential(decimals);
  }

  // Format physics units
  formatPhysicsValue(value, unit, decimals = 2) {
    const formattedValue = this.formatNumber(value, decimals);
    return `${formattedValue} ${unit}`;
  }

  // Generate unique IDs
  generateId(prefix = 'id') {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  // Deep clone objects
  deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj.getTime());
    if (obj instanceof Array) return obj.map(item => this.deepClone(item));
    if (typeof obj === 'object') {
      const clonedObj = {};
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          clonedObj[key] = this.deepClone(obj[key]);
        }
      }
      return clonedObj;
    }
  }

  // Local storage helpers with error handling
  setLocalStorage(key, value) {
    try {
      localStorage.setItem(key, JSON.stringify(value));
      return true;
    } catch (e) {
      console.warn('Failed to save to localStorage:', e);
      return false;
    }
  }

  getLocalStorage(key, defaultValue = null) {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (e) {
      console.warn('Failed to read from localStorage:', e);
      return defaultValue;
    }
  }

  removeLocalStorage(key) {
    try {
      localStorage.removeItem(key);
      return true;
    } catch (e) {
      console.warn('Failed to remove from localStorage:', e);
      return false;
    }
  }

  // URL parameter helpers
  getUrlParameter(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
  }

  setUrlParameter(name, value) {
    const url = new URL(window.location);
    url.searchParams.set(name, value);
    window.history.replaceState({}, '', url);
  }

  removeUrlParameter(name) {
    const url = new URL(window.location);
    url.searchParams.delete(name);
    window.history.replaceState({}, '', url);
  }

  // Date and time utilities
  formatDate(date, format = 'YYYY-MM-DD') {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const seconds = String(d.getSeconds()).padStart(2, '0');

    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds);
  }

  getRelativeTime(date) {
    const now = new Date();
    const diff = now - new Date(date);
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`;
    if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    if (minutes > 0) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    return 'Just now';
  }

  // Animation utilities
  animateValue(start, end, duration, callback, easing = 'easeOutCubic') {
    const startTime = performance.now();
    
    const easingFunctions = {
      linear: t => t,
      easeInCubic: t => t * t * t,
      easeOutCubic: t => 1 - Math.pow(1 - t, 3),
      easeInOutCubic: t => t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2
    };

    const easingFunction = easingFunctions[easing] || easingFunctions.easeOutCubic;

    const animate = (currentTime) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);
      const easedProgress = easingFunction(progress);
      const currentValue = start + (end - start) * easedProgress;

      callback(currentValue);

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };

    requestAnimationFrame(animate);
  }

  // Notification system
  showNotification(message, type = 'info', duration = 5000) {
    if (!this.notificationContainer) {
      this.createNotificationContainer();
    }

    const notification = document.createElement('div');
    notification.className = `notification notification-${type} animate-slide-down`;
    notification.innerHTML = `
      <div class="notification-content">
        <span class="notification-icon">${this.getNotificationIcon(type)}</span>
        <span class="notification-message">${message}</span>
        <button class="notification-close" aria-label="Close notification">&times;</button>
      </div>
    `;

    // Add close functionality
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
      this.removeNotification(notification);
    });

    this.notificationContainer.appendChild(notification);

    // Auto-remove after duration
    if (duration > 0) {
      setTimeout(() => {
        this.removeNotification(notification);
      }, duration);
    }

    return notification;
  }

  createNotificationContainer() {
    this.notificationContainer = document.createElement('div');
    this.notificationContainer.className = 'notification-container';
    document.body.appendChild(this.notificationContainer);
  }

  removeNotification(notification) {
    if (notification && notification.parentNode) {
      notification.classList.add('animate-slide-up');
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }
  }

  getNotificationIcon(type) {
    const icons = {
      success: '✅',
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️'
    };
    return icons[type] || icons.info;
  }

  // Loading overlay
  showLoading(show = true, message = 'Loading...') {
    if (show) {
      if (!this.loadingOverlay) {
        this.createLoadingOverlay();
      }
      this.loadingOverlay.querySelector('.loading-message').textContent = message;
      this.loadingOverlay.style.display = 'flex';
      document.body.style.overflow = 'hidden';
    } else {
      if (this.loadingOverlay) {
        this.loadingOverlay.style.display = 'none';
        document.body.style.overflow = '';
      }
    }
  }

  createLoadingOverlay() {
    this.loadingOverlay = document.createElement('div');
    this.loadingOverlay.className = 'loading-overlay';
    this.loadingOverlay.innerHTML = `
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <div class="loading-message">Loading...</div>
      </div>
    `;
    document.body.appendChild(this.loadingOverlay);
  }

  // Math utilities for physics calculations
  clamp(value, min, max) {
    return Math.min(Math.max(value, min), max);
  }

  lerp(start, end, factor) {
    return start + (end - start) * factor;
  }

  map(value, inMin, inMax, outMin, outMax) {
    return (value - inMin) * (outMax - outMin) / (inMax - inMin) + outMin;
  }

  distance(x1, y1, x2, y2) {
    return Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
  }

  angle(x1, y1, x2, y2) {
    return Math.atan2(y2 - y1, x2 - x1);
  }

  // Physics unit conversions
  convertUnits(value, fromUnit, toUnit) {
    const conversions = {
      // Length
      'm_to_cm': 100,
      'cm_to_m': 0.01,
      'm_to_mm': 1000,
      'mm_to_m': 0.001,
      
      // Time
      's_to_ms': 1000,
      'ms_to_s': 0.001,
      'min_to_s': 60,
      's_to_min': 1/60,
      
      // Frequency
      'Hz_to_kHz': 0.001,
      'kHz_to_Hz': 1000,
      
      // Voltage
      'V_to_mV': 1000,
      'mV_to_V': 0.001,
      'V_to_kV': 0.001,
      'kV_to_V': 1000
    };

    const conversionKey = `${fromUnit}_to_${toUnit}`;
    const factor = conversions[conversionKey];
    
    if (factor !== undefined) {
      return value * factor;
    }
    
    console.warn(`Conversion from ${fromUnit} to ${toUnit} not supported`);
    return value;
  }

  // Validation utilities
  isValidNumber(value) {
    return !isNaN(value) && isFinite(value);
  }

  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Performance monitoring
  measurePerformance(name, fn) {
    const start = performance.now();
    const result = fn();
    const end = performance.now();
    console.log(`${name} took ${end - start} milliseconds`);
    return result;
  }

  // Error handling
  handleError(error, context = 'Unknown') {
    console.error(`Error in ${context}:`, error);
    this.showNotification(`An error occurred: ${error.message}`, 'error');
  }
}
