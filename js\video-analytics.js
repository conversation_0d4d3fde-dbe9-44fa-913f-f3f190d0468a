// Physics Virtual Lab - Video Analytics & Engagement System

class VideoAnalytics {
    constructor() {
        this.sessionData = {
            startTime: Date.now(),
            videoId: null,
            watchTime: 0,
            interactions: [],
            quizAttempts: [],
            notesTaken: 0,
            pauseCount: 0,
            seekCount: 0,
            replayCount: 0,
            engagementScore: 0
        };
        
        this.engagementMetrics = {
            watchTimeWeight: 0.4,
            interactionWeight: 0.3,
            quizWeight: 0.2,
            notesWeight: 0.1
        };
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.startEngagementTracking();
    }

    setupEventListeners() {
        // Track video events
        document.addEventListener('videoPlay', (e) => this.trackVideoPlay(e.detail));
        document.addEventListener('videoPause', (e) => this.trackVideoPause(e.detail));
        document.addEventListener('videoSeek', (e) => this.trackVideoSeek(e.detail));
        document.addEventListener('videoComplete', (e) => this.trackVideoComplete(e.detail));
        
        // Track interaction events
        document.addEventListener('quizAnswer', (e) => this.trackQuizAnswer(e.detail));
        document.addEventListener('notesTaken', (e) => this.trackNotes(e.detail));
        document.addEventListener('conceptClick', (e) => this.trackConceptInteraction(e.detail));
        document.addEventListener('hotspotClick', (e) => this.trackHotspotInteraction(e.detail));
    }

    startEngagementTracking() {
        // Track engagement every 10 seconds
        setInterval(() => {
            this.calculateEngagementScore();
            this.saveAnalytics();
        }, 10000);
    }

    trackVideoPlay(data) {
        this.sessionData.interactions.push({
            type: 'play',
            timestamp: Date.now(),
            videoTime: data.currentTime || 0
        });
    }

    trackVideoPause(data) {
        this.sessionData.pauseCount++;
        this.sessionData.interactions.push({
            type: 'pause',
            timestamp: Date.now(),
            videoTime: data.currentTime || 0
        });
    }

    trackVideoSeek(data) {
        this.sessionData.seekCount++;
        this.sessionData.interactions.push({
            type: 'seek',
            timestamp: Date.now(),
            from: data.from || 0,
            to: data.to || 0
        });
    }

    trackVideoComplete(data) {
        this.sessionData.interactions.push({
            type: 'complete',
            timestamp: Date.now(),
            totalWatchTime: data.watchTime || 0
        });
        
        this.sessionData.watchTime = data.watchTime || 0;
        this.generateCompletionReport();
    }

    trackQuizAnswer(data) {
        this.sessionData.quizAttempts.push({
            questionId: data.questionId,
            answer: data.answer,
            correct: data.correct,
            timestamp: Date.now(),
            timeToAnswer: data.timeToAnswer || 0
        });
    }

    trackNotes(data) {
        this.sessionData.notesTaken++;
        this.sessionData.interactions.push({
            type: 'notes',
            timestamp: Date.now(),
            noteLength: data.length || 0
        });
    }

    trackConceptInteraction(data) {
        this.sessionData.interactions.push({
            type: 'concept_click',
            timestamp: Date.now(),
            conceptId: data.conceptId,
            videoTime: data.videoTime || 0
        });
    }

    trackHotspotInteraction(data) {
        this.sessionData.interactions.push({
            type: 'hotspot_click',
            timestamp: Date.now(),
            hotspotId: data.hotspotId,
            videoTime: data.videoTime || 0
        });
    }

    calculateEngagementScore() {
        const metrics = this.engagementMetrics;
        
        // Watch time score (0-100)
        const watchTimeScore = Math.min(100, (this.sessionData.watchTime / 300) * 100); // 5 minutes = 100%
        
        // Interaction score (0-100)
        const interactionScore = Math.min(100, this.sessionData.interactions.length * 5);
        
        // Quiz score (0-100)
        const correctAnswers = this.sessionData.quizAttempts.filter(q => q.correct).length;
        const totalQuestions = this.sessionData.quizAttempts.length;
        const quizScore = totalQuestions > 0 ? (correctAnswers / totalQuestions) * 100 : 0;
        
        // Notes score (0-100)
        const notesScore = Math.min(100, this.sessionData.notesTaken * 20);
        
        // Calculate weighted engagement score
        this.sessionData.engagementScore = Math.round(
            (watchTimeScore * metrics.watchTimeWeight) +
            (interactionScore * metrics.interactionWeight) +
            (quizScore * metrics.quizWeight) +
            (notesScore * metrics.notesWeight)
        );
        
        return this.sessionData.engagementScore;
    }

    generateEngagementInsights() {
        const insights = [];
        const score = this.sessionData.engagementScore;
        
        if (score >= 80) {
            insights.push({
                type: 'positive',
                message: 'Excellent engagement! You\'re actively learning.',
                icon: '🌟'
            });
        } else if (score >= 60) {
            insights.push({
                type: 'neutral',
                message: 'Good engagement. Try taking more notes or answering quiz questions.',
                icon: '👍'
            });
        } else {
            insights.push({
                type: 'improvement',
                message: 'Consider pausing to take notes or reviewing key concepts.',
                icon: '💡'
            });
        }
        
        // Specific insights based on behavior
        if (this.sessionData.pauseCount > 10) {
            insights.push({
                type: 'neutral',
                message: 'You\'re taking time to process information - great learning strategy!',
                icon: '⏸️'
            });
        }
        
        if (this.sessionData.seekCount > 5) {
            insights.push({
                type: 'neutral',
                message: 'Reviewing sections shows active engagement with the material.',
                icon: '🔄'
            });
        }
        
        if (this.sessionData.notesTaken === 0) {
            insights.push({
                type: 'improvement',
                message: 'Try taking notes to improve retention and understanding.',
                icon: '📝'
            });
        }
        
        return insights;
    }

    generateCompletionReport() {
        const report = {
            sessionId: this.generateSessionId(),
            videoId: this.sessionData.videoId,
            completedAt: new Date().toISOString(),
            duration: Date.now() - this.sessionData.startTime,
            engagementScore: this.calculateEngagementScore(),
            metrics: {
                watchTime: this.sessionData.watchTime,
                interactions: this.sessionData.interactions.length,
                quizScore: this.getQuizScore(),
                notesTaken: this.sessionData.notesTaken,
                pauseCount: this.sessionData.pauseCount,
                seekCount: this.sessionData.seekCount
            },
            insights: this.generateEngagementInsights(),
            recommendations: this.generateRecommendations()
        };
        
        this.saveCompletionReport(report);
        return report;
    }

    getQuizScore() {
        const correct = this.sessionData.quizAttempts.filter(q => q.correct).length;
        const total = this.sessionData.quizAttempts.length;
        return total > 0 ? Math.round((correct / total) * 100) : 0;
    }

    generateRecommendations() {
        const recommendations = [];
        const score = this.sessionData.engagementScore;
        const quizScore = this.getQuizScore();
        
        if (quizScore < 70 && this.sessionData.quizAttempts.length > 0) {
            recommendations.push({
                type: 'review',
                title: 'Review Key Concepts',
                description: 'Consider reviewing the video sections related to quiz questions you missed.',
                action: 'Review Video',
                priority: 'high'
            });
        }
        
        if (this.sessionData.notesTaken === 0) {
            recommendations.push({
                type: 'study_skill',
                title: 'Take Notes',
                description: 'Taking notes while watching can improve retention by up to 40%.',
                action: 'Learn Note-Taking Tips',
                priority: 'medium'
            });
        }
        
        if (score >= 80) {
            recommendations.push({
                type: 'next_step',
                title: 'Try the Related Experiment',
                description: 'You\'ve mastered this concept! Apply your knowledge in the virtual lab.',
                action: 'Start Experiment',
                priority: 'high'
            });
        }
        
        return recommendations;
    }

    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    saveAnalytics() {
        const analyticsData = JSON.parse(localStorage.getItem('video-analytics') || '[]');
        analyticsData.push({
            timestamp: Date.now(),
            sessionData: { ...this.sessionData },
            engagementScore: this.sessionData.engagementScore
        });
        
        // Keep only last 100 entries
        if (analyticsData.length > 100) {
            analyticsData.splice(0, analyticsData.length - 100);
        }
        
        localStorage.setItem('video-analytics', JSON.stringify(analyticsData));
    }

    saveCompletionReport(report) {
        const reports = JSON.parse(localStorage.getItem('video-completion-reports') || '[]');
        reports.push(report);
        
        // Keep only last 50 reports
        if (reports.length > 50) {
            reports.splice(0, reports.length - 50);
        }
        
        localStorage.setItem('video-completion-reports', JSON.stringify(reports));
    }

    getAnalyticsSummary() {
        const analyticsData = JSON.parse(localStorage.getItem('video-analytics') || '[]');
        const reports = JSON.parse(localStorage.getItem('video-completion-reports') || '[]');
        
        if (analyticsData.length === 0) {
            return {
                totalSessions: 0,
                averageEngagement: 0,
                totalWatchTime: 0,
                videosCompleted: 0
            };
        }
        
        const totalEngagement = analyticsData.reduce((sum, entry) => sum + entry.engagementScore, 0);
        const totalWatchTime = reports.reduce((sum, report) => sum + report.metrics.watchTime, 0);
        
        return {
            totalSessions: analyticsData.length,
            averageEngagement: Math.round(totalEngagement / analyticsData.length),
            totalWatchTime: Math.round(totalWatchTime / 60), // Convert to minutes
            videosCompleted: reports.length,
            lastActivity: analyticsData.length > 0 ? new Date(analyticsData[analyticsData.length - 1].timestamp) : null
        };
    }

    displayEngagementFeedback() {
        const insights = this.generateEngagementInsights();
        const score = this.sessionData.engagementScore;
        
        // Create or update engagement display
        let engagementDisplay = document.getElementById('engagement-feedback');
        if (!engagementDisplay) {
            engagementDisplay = document.createElement('div');
            engagementDisplay.id = 'engagement-feedback';
            engagementDisplay.className = 'engagement-feedback';
            document.body.appendChild(engagementDisplay);
        }
        
        engagementDisplay.innerHTML = `
            <div class="engagement-score">
                <div class="score-circle" style="--score: ${score}">
                    <span class="score-value">${score}%</span>
                </div>
                <div class="score-label">Engagement</div>
            </div>
            <div class="engagement-insights">
                ${insights.map(insight => `
                    <div class="insight insight-${insight.type}">
                        <span class="insight-icon">${insight.icon}</span>
                        <span class="insight-message">${insight.message}</span>
                    </div>
                `).join('')}
            </div>
        `;
        
        // Add styles if not already present
        if (!document.getElementById('engagement-styles')) {
            const styles = document.createElement('style');
            styles.id = 'engagement-styles';
            styles.textContent = `
                .engagement-feedback {
                    position: fixed;
                    bottom: 20px;
                    right: 20px;
                    background: white;
                    border-radius: 12px;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                    padding: 1rem;
                    max-width: 300px;
                    z-index: 1000;
                    transform: translateX(100%);
                    transition: transform 0.3s ease;
                }
                
                .engagement-feedback.show {
                    transform: translateX(0);
                }
                
                .engagement-score {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin-bottom: 1rem;
                }
                
                .score-circle {
                    width: 60px;
                    height: 60px;
                    border-radius: 50%;
                    background: conic-gradient(var(--primary-600) 0deg, var(--primary-600) calc(var(--score) * 3.6deg), #e5e7eb calc(var(--score) * 3.6deg));
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    position: relative;
                }
                
                .score-circle::before {
                    content: '';
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    background: white;
                    position: absolute;
                }
                
                .score-value {
                    font-weight: 700;
                    color: var(--primary-600);
                    z-index: 1;
                }
                
                .score-label {
                    font-weight: 600;
                    color: var(--text-primary);
                }
                
                .engagement-insights {
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                }
                
                .insight {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-size: 0.875rem;
                    padding: 0.5rem;
                    border-radius: 8px;
                }
                
                .insight-positive {
                    background: #f0fdf4;
                    color: #166534;
                }
                
                .insight-neutral {
                    background: #f0f9ff;
                    color: #1e40af;
                }
                
                .insight-improvement {
                    background: #fef3c7;
                    color: #92400e;
                }
                
                @media (max-width: 768px) {
                    .engagement-feedback {
                        bottom: 10px;
                        right: 10px;
                        left: 10px;
                        max-width: none;
                    }
                }
            `;
            document.head.appendChild(styles);
        }
        
        // Show the feedback
        setTimeout(() => {
            engagementDisplay.classList.add('show');
        }, 100);
        
        // Auto-hide after 10 seconds
        setTimeout(() => {
            engagementDisplay.classList.remove('show');
        }, 10000);
    }

    // Public API methods
    setVideoId(videoId) {
        this.sessionData.videoId = videoId;
    }

    triggerEvent(eventType, data = {}) {
        const event = new CustomEvent(eventType, { detail: data });
        document.dispatchEvent(event);
    }

    showEngagementSummary() {
        const summary = this.getAnalyticsSummary();
        const report = this.generateCompletionReport();
        
        // Display summary modal or notification
        if (window.app && window.app.showNotification) {
            window.app.showNotification(
                `Session complete! Engagement: ${report.engagementScore}% 🎉`, 
                'success'
            );
        }
        
        return { summary, report };
    }
}

// Global analytics instance
window.videoAnalytics = new VideoAnalytics();

// Export for module use
export default VideoAnalytics;
