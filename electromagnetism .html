<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Circuit Simulator</title>
    <style>
        body { margin: 0; font-family: Arial, sans-serif; display: flex; flex-direction: column; height: 100vh; background-color: #f0f0f0; overflow: hidden; }
        #controls { padding: 10px; background-color: #e0e0e0; display: flex; flex-wrap: wrap; gap: 10px 20px; align-items: center; border-bottom: 1px solid #ccc; user-select: none;}
        #controls label, #controls span, #controls button { margin-right: 5px; font-size: 0.9em; }
        #controls input[type="range"] { vertical-align: middle; }
        #simulationCanvas { flex-grow: 1; display: block; background-color: #ffffff; }
        #infoText { padding: 8px; background-color: #e0e0e0; text-align: center; border-top: 1px solid #ccc; font-size: 0.9em; user-select: none;}
        canvas { cursor: default; }
    </style>
</head>
<body>

    <div id="controls">
        <div>
            <label for="voltageSlider">Voltage: <span id="voltageValue">1.5</span>V</label>
            <input type="range" id="voltageSlider" min="1.5" max="9" step="1.5" value="1.5">
        </div>
        <div>
            <label for="resistanceSlider">Resistance: <span id="resistanceValue">10</span>&Omega;</label>
            <input type="range" id="resistanceSlider" min="1" max="100" step="1" value="10">
        </div>
        <button id="fieldViewToggle">Field View: OFF</button>
    </div>

    <canvas id="simulationCanvas"></canvas>

    <div id="infoText">Voltage: 1.5V, Resistance: 10&Omega;, Current: 0.00A</div>

    <script>
        const canvas = document.getElementById('simulationCanvas');
        const ctx = canvas.getContext('2d');

        const voltageSlider = document.getElementById('voltageSlider');
        const voltageValueSpan = document.getElementById('voltageValue');
        const resistanceSlider = document.getElementById('resistanceSlider');
        const resistanceValueSpan = document.getElementById('resistanceValue');
        const fieldViewToggle = document.getElementById('fieldViewToggle');
        const infoTextDiv = document.getElementById('infoText');

        const COMPONENT_TYPES = { BATTERY: 'battery', RESISTOR: 'resistor', BULB: 'bulb' };
        const BULB_INTERNAL_RESISTANCE = 5; // Ohms
        const WIRE_TERMINAL_RADIUS = 7;
        const CONNECTION_SNAP_RADIUS = 15;
        const MIN_CIRCUIT_RESISTANCE = 0.1; // To prevent division by zero

        let components = [];
        let wires = [];
        let nextComponentId = 0;
        let nextWireId = 0;

        let draggingComponent = null;
        let dragOffsetX, dragOffsetY;
        
        let wiringState = {
            active: false,
            startComponent: null,
            startTerminalIndex: -1,
            startX: 0, // Logical coords
            startY: 0, // Logical coords
            mouseX: 0, // Logical coords
            mouseY: 0  // Logical coords
        };

        let showFieldView = false;
        let logicalWidth = 800;
        let logicalHeight = 600;
        let scaleX = 1, scaleY = 1;

        // --- Component Definitions ---
        function createComponent(type, x, y, value) {
            const baseComponent = {
                id: nextComponentId++,
                type: type,
                x: x, y: y,
                width: 0, height: 0,
                terminals: [] // Each terminal: { id, offsetX, offsetY, type (optional) }
            };

            switch (type) {
                case COMPONENT_TYPES.BATTERY:
                    baseComponent.width = 40; baseComponent.height = 80;
                    baseComponent.voltage = value;
                    baseComponent.terminals = [
                        { id:0, offsetX: baseComponent.width / 2, offsetY: 0, type: 'positive' },
                        { id:1, offsetX: baseComponent.width / 2, offsetY: baseComponent.height, type: 'negative' }
                    ];
                    break;
                case COMPONENT_TYPES.RESISTOR:
                    baseComponent.width = 80; baseComponent.height = 30;
                    baseComponent.resistance = value;
                    baseComponent.terminals = [
                        { id:0, offsetX: 0, offsetY: baseComponent.height / 2 },
                        { id:1, offsetX: baseComponent.width, offsetY: baseComponent.height / 2 }
                    ];
                    break;
                case COMPONENT_TYPES.BULB:
                    baseComponent.width = 50; baseComponent.height = 70;
                    baseComponent.nominalResistance = BULB_INTERNAL_RESISTANCE;
                    baseComponent.brightness = 0;
                    baseComponent.terminals = [
                        { id:0, offsetX: baseComponent.width / 2, offsetY: baseComponent.height }, // Bottom contact
                        { id:1, offsetX: baseComponent.width / 2, offsetY: baseComponent.height - 20 } // Side contact (on base)
                    ];
                    break;
            }
            return baseComponent;
        }

        // --- Initialization ---
        function init() {
            const battery = createComponent(COMPONENT_TYPES.BATTERY, 100, logicalHeight/2 - 40, parseFloat(voltageSlider.value));
            const resistor = createComponent(COMPONENT_TYPES.RESISTOR, 300, logicalHeight/2 - 15, parseInt(resistanceSlider.value));
            const bulb = createComponent(COMPONENT_TYPES.BULB, 500, logicalHeight/2 - 35);
            components.push(battery, resistor, bulb);

            setupEventListeners();
            resizeCanvas(); // Initial sizing and first draw call via gameLoop trigger
            gameLoop(); // Start the animation/update loop
        }

        function setupEventListeners() {
            voltageSlider.addEventListener('input', (e) => {
                const voltage = parseFloat(e.target.value);
                voltageValueSpan.textContent = voltage.toFixed(1);
                const battery = components.find(c => c.type === COMPONENT_TYPES.BATTERY);
                if (battery) battery.voltage = voltage;
                // updateCircuitAndInfo will be called by gameLoop
            });

            resistanceSlider.addEventListener('input', (e) => {
                const resistance = parseInt(e.target.value);
                resistanceValueSpan.textContent = resistance;
                const resistor = components.find(c => c.type === COMPONENT_TYPES.RESISTOR);
                if (resistor) resistor.resistance = resistance;
                // updateCircuitAndInfo will be called by gameLoop
            });

            fieldViewToggle.addEventListener('click', () => {
                showFieldView = !showFieldView;
                fieldViewToggle.textContent = `Field View: ${showFieldView ? 'ON' : 'OFF'}`;
            });

            canvas.addEventListener('mousedown', handleMouseDown);
            canvas.addEventListener('mousemove', handleMouseMove);
            canvas.addEventListener('mouseup', handleMouseUp);
            canvas.addEventListener('mouseleave', handleMouseLeave);

            window.addEventListener('resize', resizeCanvas);
        }
        
        function updateCircuitAndInfo() {
            const circuitResult = analyzeCircuit();
            const bulb = components.find(c => c.type === COMPONENT_TYPES.BULB);
            const battery = components.find(c => c.type === COMPONENT_TYPES.BATTERY); // For V in text
            const resistor = components.find(c => c.type === COMPONENT_TYPES.RESISTOR); // For R in text

            if (bulb && battery) {
                if (circuitResult.isComplete) {
                    const refCurrentForMaxBrightness = battery.voltage / bulb.nominalResistance;
                    const currentRatio = refCurrentForMaxBrightness > 0.01 ? circuitResult.current / refCurrentForMaxBrightness : 0;
                    bulb.brightness = Math.min(1, Math.pow(Math.max(0, currentRatio), 1.5)); // Power 1.5 for better visual ramp
                } else {
                    bulb.brightness = 0;
                }
            }
            if (battery && resistor) {
                 infoTextDiv.textContent = `Voltage: ${battery.voltage.toFixed(1)}V, Resistance: ${resistor.resistance}\u03A9, Current: ${circuitResult.current.toFixed(2)}A`;
            }
        }

        function resizeCanvas() {
            const parentWidth = canvas.clientWidth; // Use clientWidth for actual display size
            const parentHeight = canvas.clientHeight;

            canvas.width = parentWidth; // Set canvas internal bitmap size
            canvas.height = parentHeight;
            
            scaleX = parentWidth / logicalWidth;
            scaleY = parentHeight / logicalHeight;
        }
        
        function getMousePos(event) {
            const rect = canvas.getBoundingClientRect();
            return {
                x: (event.clientX - rect.left) / scaleX,
                y: (event.clientY - rect.top) / scaleY
            };
        }

        function handleMouseDown(event) {
            const mousePos = getMousePos(event);

            for (const comp of components) {
                for (let i = 0; i < comp.terminals.length; i++) {
                    const terminal = comp.terminals[i];
                    const termGlobalX = comp.x + terminal.offsetX;
                    const termGlobalY = comp.y + terminal.offsetY;
                    if (Math.hypot(mousePos.x - termGlobalX, mousePos.y - termGlobalY) <= WIRE_TERMINAL_RADIUS) {
                        wiringState.active = true;
                        wiringState.startComponent = comp;
                        wiringState.startTerminalIndex = i; // Store index, not id
                        wiringState.startX = termGlobalX;
                        wiringState.startY = termGlobalY;
                        wiringState.mouseX = mousePos.x;
                        wiringState.mouseY = mousePos.y;
                        canvas.style.cursor = 'crosshair';
                        return;
                    }
                }
            }

            for (let i = components.length - 1; i >= 0; i--) {
                const comp = components[i];
                if (mousePos.x >= comp.x && mousePos.x <= comp.x + comp.width &&
                    mousePos.y >= comp.y && mousePos.y <= comp.y + comp.height) {
                    draggingComponent = comp;
                    components.splice(i, 1); // Remove and re-add to draw on top
                    components.push(draggingComponent);
                    dragOffsetX = mousePos.x - comp.x;
                    dragOffsetY = mousePos.y - comp.y;
                    canvas.style.cursor = 'grabbing';
                    return;
                }
            }
        }

        function handleMouseMove(event) {
            const mousePos = getMousePos(event);

            if (wiringState.active) {
                wiringState.mouseX = mousePos.x;
                wiringState.mouseY = mousePos.y;
                return;
            }

            if (draggingComponent) {
                draggingComponent.x = mousePos.x - dragOffsetX;
                draggingComponent.y = mousePos.y - dragOffsetY;
                draggingComponent.x = Math.max(0, Math.min(logicalWidth - draggingComponent.width, draggingComponent.x));
                draggingComponent.y = Math.max(0, Math.min(logicalHeight - draggingComponent.height, draggingComponent.y));
                return;
            }
            
            // Cursor hover effects
            let onInteractiveElement = false;
            for (const comp of components) {
                for (const terminal of comp.terminals) {
                    const termGlobalX = comp.x + terminal.offsetX;
                    const termGlobalY = comp.y + terminal.offsetY;
                    if (Math.hypot(mousePos.x - termGlobalX, mousePos.y - termGlobalY) <= WIRE_TERMINAL_RADIUS) {
                        canvas.style.cursor = 'pointer';
                        onInteractiveElement = true;
                        break;
                    }
                }
                if (onInteractiveElement) break;
                 if (mousePos.x >= comp.x && mousePos.x <= comp.x + comp.width &&
                    mousePos.y >= comp.y && mousePos.y <= comp.y + comp.height) {
                    canvas.style.cursor = 'grab';
                    onInteractiveElement = true;
                    break;
                }
            }
            if (!onInteractiveElement) {
                canvas.style.cursor = 'default';
            }
        }

        function handleMouseUp(event) {
            const mousePos = getMousePos(event);

            if (wiringState.active) {
                canvas.style.cursor = 'default';
                let connectionMade = false;
                for (const comp of components) {
                    if (connectionMade) break;
                    for (let i = 0; i < comp.terminals.length; i++) {
                        const terminal = comp.terminals[i];
                        if (comp.id === wiringState.startComponent.id && i === wiringState.startTerminalIndex) continue;

                        const termGlobalX = comp.x + terminal.offsetX;
                        const termGlobalY = comp.y + terminal.offsetY;
                        if (Math.hypot(mousePos.x - termGlobalX, mousePos.y - termGlobalY) <= CONNECTION_SNAP_RADIUS) {
                            const existingWire = wires.find(w =>
                                (w.startComponent.id === wiringState.startComponent.id && w.startTerminalIndex === wiringState.startTerminalIndex && w.endComponent.id === comp.id && w.endTerminalIndex === i) ||
                                (w.startComponent.id === comp.id && w.startTerminalIndex === i && w.endComponent.id === wiringState.startComponent.id && w.endTerminalIndex === wiringState.startTerminalIndex)
                            );
                            if (!existingWire) {
                                wires.push({
                                    id: nextWireId++,
                                    startComponent: wiringState.startComponent,
                                    startTerminalIndex: wiringState.startTerminalIndex,
                                    endComponent: comp,
                                    endTerminalIndex: i
                                });
                            }
                            connectionMade = true;
                            break; 
                        }
                    }
                }
                wiringState.active = false;
            }
            
            if (draggingComponent) {
                canvas.style.cursor = 'grab'; // Or default if not over it anymore
                draggingComponent = null;
            }
             // Reset cursor if nothing else sets it
            if (!wiringState.active && !draggingComponent) {
                 handleMouseMove(event); // Re-evaluate cursor based on new position
            }
        }
        
        function handleMouseLeave(event) {
            if (wiringState.active) wiringState.active = false;
            if (draggingComponent) draggingComponent = null;
            canvas.style.cursor = 'default';
        }

        function analyzeCircuit() {
            const battery = components.find(c => c.type === COMPONENT_TYPES.BATTERY);
            const resistor = components.find(c => c.type === COMPONENT_TYPES.RESISTOR);
            const bulb = components.find(c => c.type === COMPONENT_TYPES.BULB);

            if (!battery || !resistor || !bulb || wires.length === 0) {
                return { isComplete: false, current: 0, totalResistance: Infinity };
            }

            const adj = new Map();
            function addEdge(uNode, vNode) {
                if (!adj.has(uNode)) adj.set(uNode, []);
                if (!adj.has(vNode)) adj.set(vNode, []);
                adj.get(uNode).push(vNode);
                adj.get(vNode).push(uNode);
            }

            for (const wire of wires) {
                const u = `${wire.startComponent.id}_${wire.startComponent.terminals[wire.startTerminalIndex].id}`;
                const v = `${wire.endComponent.id}_${wire.endComponent.terminals[wire.endTerminalIndex].id}`;
                addEdge(u, v);
            }
            
            for (const comp of components) {
                if ((comp.type === COMPONENT_TYPES.RESISTOR || comp.type === COMPONENT_TYPES.BULB) && comp.terminals.length === 2) {
                    const u = `${comp.id}_${comp.terminals[0].id}`;
                    const v = `${comp.id}_${comp.terminals[1].id}`;
                    addEdge(u, v); 
                }
            }
            
            const batteryPosNode = `${battery.id}_${battery.terminals[0].id}`;
            const batteryNegNode = `${battery.id}_${battery.terminals[1].id}`;

            const queue = [[batteryPosNode, [batteryPosNode], new Set([battery.id])]];
            const visited = new Set([batteryPosNode]); // Nodes added to queue

            while (queue.length > 0) {
                const [currentNode, path, pathComponentsSet] = queue.shift();

                if (currentNode === batteryNegNode) {
                    const hasResistor = pathComponentsSet.has(resistor.id);
                    const hasBulb = pathComponentsSet.has(bulb.id);
                    if (hasResistor && hasBulb) {
                        const totalResistance = resistor.resistance + bulb.nominalResistance;
                        const R_eff = Math.max(MIN_CIRCUIT_RESISTANCE, totalResistance);
                        const current = battery.voltage / R_eff;
                        return { isComplete: true, current: current, totalResistance: R_eff };
                    }
                }

                const neighbors = adj.get(currentNode) || [];
                for (const neighborNode of neighbors) {
                    if (!visited.has(neighborNode)) {
                        visited.add(neighborNode);
                        const [compIDStr] = neighborNode.split('_');
                        const neighborComponentID = parseInt(compIDStr);
                        const newPathComponentsSet = new Set(pathComponentsSet);
                        newPathComponentsSet.add(neighborComponentID);
                        if (path.length < (components.length * 2 + wires.length + 5)) { // Path length limit
                           queue.push([neighborNode, [...path, neighborNode], newPathComponentsSet]);
                        }
                    }
                }
            }
            return { isComplete: false, current: 0, totalResistance: Infinity };
        }

        function drawAll() {
            ctx.save();
            ctx.fillStyle = '#FFFFFF';
            ctx.fillRect(0, 0, canvas.width, canvas.height); // Clear physical canvas
            
            ctx.scale(scaleX, scaleY); // Apply scaling for all subsequent draws using logical coords

            wires.forEach(drawWire);
            if (wiringState.active) drawTemporaryWire();
            components.forEach(comp => drawComponent(comp));

            if (showFieldView) {
                const battery = components.find(c => c.type === COMPONENT_TYPES.BATTERY);
                if (battery) drawElectricField(battery);
            }
            
            ctx.restore();
        }

        function drawComponent(comp) {
            ctx.save();
            ctx.translate(comp.x, comp.y);
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;

            if (comp.type === COMPONENT_TYPES.BATTERY) {
                ctx.fillStyle = '#D9A44D'; // Muted orange
                ctx.fillRect(0, 0, comp.width, comp.height);
                ctx.strokeRect(0, 0, comp.width, comp.height);
                ctx.fillStyle = '#AAA';
                ctx.fillRect(comp.width/2 - 10, -5, 20, 5);
                ctx.fillStyle = '#333'; ctx.font = 'bold 12px Arial'; ctx.textAlign = 'center';
                ctx.fillText('+', comp.terminals[0].offsetX, comp.terminals[0].offsetY - 8);
                ctx.fillText('-', comp.terminals[1].offsetX, comp.terminals[1].offsetY + 12);
            } else if (comp.type === COMPONENT_TYPES.RESISTOR) {
                ctx.fillStyle = '#A0C0E0'; // Muted blue
                ctx.fillRect(0, 0, comp.width, comp.height);
                ctx.strokeRect(0, 0, comp.width, comp.height);
                ctx.fillStyle = '#333'; ctx.font = '11px Arial'; ctx.textAlign = 'center';
                ctx.fillText(`${comp.resistance}\u03A9`, comp.width / 2, comp.height / 2 + 4);
            } else if (comp.type === COMPONENT_TYPES.BULB) {
                ctx.fillStyle = '#999'; // Base color
                ctx.fillRect(comp.width/2 - 10, comp.height - 20, 20, 20); // Base
                ctx.strokeRect(comp.width/2 - 10, comp.height - 20, 20, 20);
                
                ctx.beginPath();
                ctx.arc(comp.width / 2, comp.height / 2 - 10, comp.width / 2.5, 0, 2 * Math.PI); // Glass bulb
                if (comp.brightness > 0.01) {
                    const glowAlpha = 0.2 + comp.brightness * 0.6;
                    const glowRadius = (comp.width / 2.5) + comp.brightness * 5;
                    
                    // Outer glow
                    let grad = ctx.createRadialGradient(comp.width / 2, comp.height / 2 -10, glowRadius*0.2, comp.width / 2, comp.height / 2 -10, glowRadius);
                    grad.addColorStop(0, `rgba(255, 255, 180, ${comp.brightness * 0.8})`);
                    grad.addColorStop(1, `rgba(255, 255, 180, 0)`);
                    ctx.fillStyle = grad;
                    ctx.fill();

                    // Bulb color
                    ctx.fillStyle = `rgba(255, 255, ${Math.floor(200 - comp.brightness * 150)}, ${glowAlpha})`;
                } else {
                    ctx.fillStyle = 'rgba(200, 200, 220, 0.2)';
                }
                ctx.fill();
                ctx.strokeStyle = '#777'; ctx.stroke();

                if (comp.brightness <= 0.01) { // Filament
                    ctx.beginPath();
                    ctx.moveTo(comp.width/2 - 5, comp.height/2 - 10); ctx.lineTo(comp.width/2 + 5, comp.height/2 - 10);
                    ctx.strokeStyle = '#555'; ctx.lineWidth = 1.5; ctx.stroke();
                }
            }

            comp.terminals.forEach(terminal => { // Draw terminals
                const termGlobalX = comp.x + terminal.offsetX; // For snap highlight check
                const termGlobalY = comp.y + terminal.offsetY;
                ctx.beginPath();
                ctx.arc(terminal.offsetX, terminal.offsetY, WIRE_TERMINAL_RADIUS, 0, 2 * Math.PI);
                ctx.fillStyle = (terminal.type === 'positive' ? '#E74C3C' : (terminal.type === 'negative' ? '#3498DB' : '#444'));
                ctx.fill();
                // Highlight for snapping during wiring
                if (wiringState.active && Math.hypot(wiringState.mouseX - termGlobalX, wiringState.mouseY - termGlobalY) <= CONNECTION_SNAP_RADIUS &&
                    !(comp.id === wiringState.startComponent.id && terminal.id === wiringState.startComponent.terminals[wiringState.startTerminalIndex].id)) {
                    ctx.strokeStyle = 'rgba(0,200,0,0.8)'; ctx.lineWidth = 3; ctx.stroke();
                }
            });
            ctx.restore();
        }

        function drawWire(wire) {
            ctx.beginPath();
            const startTerm = wire.startComponent.terminals[wire.startTerminalIndex];
            const endTerm = wire.endComponent.terminals[wire.endTerminalIndex];
            ctx.moveTo(wire.startComponent.x + startTerm.offsetX, wire.startComponent.y + startTerm.offsetY);
            ctx.lineTo(wire.endComponent.x + endTerm.offsetX, wire.endComponent.y + endTerm.offsetY);
            ctx.strokeStyle = '#333'; ctx.lineWidth = 4; ctx.stroke();
        }

        function drawTemporaryWire() {
            ctx.beginPath();
            ctx.moveTo(wiringState.startX, wiringState.startY);
            ctx.lineTo(wiringState.mouseX, wiringState.mouseY);
            ctx.strokeStyle = '#007bff'; ctx.lineWidth = 3;
            ctx.setLineDash([4, 4]); ctx.stroke();
            ctx.setLineDash([]);
        }

        function drawElectricField(battery) {
            const numLinePairs = Math.floor(Math.max(1, battery.voltage * 0.6));
            const posTerm = battery.terminals.find(t => t.type === 'positive');
            const negTerm = battery.terminals.find(t => t.type === 'negative');
            if (!posTerm || !negTerm) return;

            const startPt = { x: battery.x + posTerm.offsetX, y: battery.y + posTerm.offsetY };
            const endPt = { x: battery.x + negTerm.offsetX, y: battery.y + negTerm.offsetY };

            ctx.lineWidth = 1 + battery.voltage * 0.1; // Thicker lines for higher voltage
            ctx.strokeStyle = `rgba(0, 100, 255, ${0.2 + Math.min(0.3, battery.voltage/9 * 0.3)})`;

            // Polarity labels are drawn by battery component, ensure they are not occluded
            ctx.fillStyle = 'red'; ctx.font = 'bold 12px Arial';
            ctx.fillText('+', startPt.x - (battery.width/2 + 5), startPt.y + 4);
            ctx.fillStyle = 'blue';
            ctx.fillText('-', endPt.x - (battery.width/2 + 5), endPt.y + 4);

            for (let i = 0; i < numLinePairs; i++) {
                const bulgeFactor = 30 + i * 15 + battery.voltage * 2;
                // Side 1
                ctx.beginPath(); ctx.moveTo(startPt.x, startPt.y);
                ctx.bezierCurveTo(
                    startPt.x - bulgeFactor, startPt.y + (endPt.y - startPt.y) * 0.3,
                    endPt.x - bulgeFactor, endPt.y - (endPt.y - startPt.y) * 0.3,
                    endPt.x, endPt.y);
                ctx.stroke();
                // Side 2
                ctx.beginPath(); ctx.moveTo(startPt.x, startPt.y);
                ctx.bezierCurveTo(
                    startPt.x + bulgeFactor, startPt.y + (endPt.y - startPt.y) * 0.3,
                    endPt.x + bulgeFactor, endPt.y - (endPt.y - startPt.y) * 0.3,
                    endPt.x, endPt.y);
                ctx.stroke();
            }
        }

        function gameLoop() {
            updateCircuitAndInfo();
            drawAll();
            requestAnimationFrame(gameLoop);
        }

        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
