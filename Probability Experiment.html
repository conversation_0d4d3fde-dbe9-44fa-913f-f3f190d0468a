<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Probability Experiment</title>
    <style>
        /* General Styles */
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
            flex-grow: 1;
        }

        h1, h2 {
            color: #2c3e50;
            text-align: center;
        }
        h1 {
            margin-bottom: 10px;
        }
        .instructions {
            text-align: center;
            margin-bottom: 25px;
            color: #555;
            font-size: 0.95em;
            max-width: 700px;
            margin-left: auto;
            margin-right: auto;
        }

        /* App Area Layout */
        .app-area {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: center;
        }

        .card {
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px; /* For when they stack */
            flex: 1 1 320px; /* Grow, shrink, basis */
            min-width: 290px; /* Ensure readability on small screens */
            display: flex;
            flex-direction: column;
        }
        .card h2 {
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        /* Setup Area */
        .setup-area div {
            margin-bottom: 15px;
        }
        .setup-area label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #34495e;
            font-size: 0.9em;
        }
        .setup-area input[type="number"],
        .setup-area input[type="range"] {
            width: 100%; 
            padding: 8px 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .setup-area input[type="range"] {
            padding: 0; /* Range inputs behave differently */
            vertical-align: middle;
        }
        #probValue {
            font-weight: bold;
            color: #2980b9;
            margin-left: 5px;
            font-size: 0.9em;
            display: inline-block; /* Ensures it stays on the same line nicely */
            min-width: 35px; /* Space for "0.00" to "1.00" */
        }
        #runExperimentBtn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s ease;
            width: 100%;
            margin-top: 10px;
        }
        #runExperimentBtn:hover:not(:disabled) {
            background-color: #2980b9;
        }
        #runExperimentBtn:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }

        /* Experiment Area */
        .experiment-area {
            min-height: 180px; 
        }
        #trialProgressContainer {
            margin-bottom: 15px;
            font-size: 0.9em;
            color: #555;
        }
        #trialProgressContainer p {
            margin: 0 0 8px 0;
        }
        #trialProgressBar {
            width: 100%;
            height: 18px;
            border-radius: 9px;
        }
        #trialProgressBar::-webkit-progress-bar {
            background-color: #ecf0f1;
            border-radius: 9px;
        }
        #trialProgressBar::-webkit-progress-value {
            background-color: #2ecc71;
            border-radius: 9px;
            transition: width 0.2s ease-out;
        }
        #trialProgressBar::-moz-progress-bar { /* Firefox */
            background-color: #2ecc71;
            border-radius: 9px;
            transition: width 0.2s ease-out;
        }

        .visual-trials-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 15px;
            align-items: center;
            justify-content: flex-start;
            min-height: 40px; 
        }
        .trial-visual {
            width: 30px;
            height: 30px;
            border-radius: 50%; 
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 1.2em;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .trial-success {
            background-color: #2ecc71; /* Green */
        }
        .trial-failure {
            background-color: #e74c3c; /* Red */
            border-radius: 4px; /* For X, make it square-ish */
        }
        .trial-failure::before {
            content: "✕"; /* X character */
        }

        /* Observation Area */
        #resultsText p {
            margin: 8px 0;
            font-size: 0.95em;
        }
        #expProbSuccess, #numSuccesses, #numFailures {
            font-weight: bold;
        }
        #expProbSuccess { color: #2980b9; }
        #numSuccesses { color: #2ecc71; }
        #numFailures { color: #e74c3c; }

        .bar-graph-container {
            margin-top: 20px;
            width: 100%;
            display: flex;
            justify-content: center; 
        }
        .bar-graph {
            display: flex;
            gap: 25px; 
            align-items: flex-end; 
            height: 150px; 
            width: 180px; 
            border-bottom: 2px solid #bdc3c7; 
            padding-bottom: 2px;
        }
        .bar {
            flex: 1; 
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-end; 
            position: relative; 
            transition: height 0.5s ease-out;
        }
        .success-bar {
            background-color: #2ecc71;
            height: 0; 
        }
        .failure-bar {
            background-color: #e74c3c;
            height: 0; 
        }
        .bar-label {
            font-size: 0.8em;
            color: #333;
            position: absolute;
            bottom: -22px; 
            width: 100%;
            text-align: center;
            font-weight: 500;
        }
        .bar-value {
            font-size: 0.9em;
            color: white;
            font-weight: bold;
            margin-bottom: 5px; 
            opacity: 0; 
            transition: opacity 0.3s ease-in 0.5s; 
            text-shadow: 1px 1px 1px rgba(0,0,0,0.2);
        }
        .bar.has-value .bar-value { 
            opacity: 1;
        }

        /* Footer */
        footer {
            text-align: center;
            margin-top: 30px;
            padding: 15px 0;
            background-color: #e9ecef;
            color: #7f8c8d;
            font-size: 0.85em;
            width: 100%;
            box-sizing: border-box;
        }

        /* Responsive adjustments */
        @media (max-width: 1024px) { /* Adjust breakpoint for 3 cards wide */
             .card {
                flex-basis: calc(50% - 10px); /* Two cards per row */
            }
            .experiment-area { /* Allow experiment area to be wider if it's alone in a row */
                 flex-basis: calc(100% - 20px); /* Full width minus gap, if it's the third item in a row */
            }
             /* If there are three cards, the third one might wrap to a new line and take full width.
               This depends on the sum of flex-basis values. If we want the third one (e.g. Observation)
               to also be 50% with the first one of the next "virtual row", that's default flex-wrap behavior.
               If we want a specific card to take full width when it wraps, that's more complex without JS.
               Current setup: 2 cards, then the 3rd wraps and takes its flex-basis (320px) or grows.
               To make the third card full width when it wraps, we can set a wider flex-basis on it,
               or target it specifically if its order is fixed.
               For simplicity, the current flexbox behavior is usually acceptable.
            */
        }


        @media (max-width: 768px) {
            .app-area {
                flex-direction: column; /* Stack cards vertically */
            }
            .card {
                flex-basis: auto; /* Allow cards to take full width when stacked */
                margin-left: 0;
                margin-right: 0;
            }
             .experiment-area {
                 min-height: 150px;
             }
            .bar-graph {
                width: 80%; 
            }
        }
        @media (max-width: 480px) {
            body {
                padding: 10px;
            }
            h1 {
                font-size: 1.6em;
            }
            .card {
                padding: 15px;
            }
            .card h2 {
                font-size: 1.2em;
            }
            #runExperimentBtn {
                padding: 10px 15px;
                font-size: 0.95em;
            }
            .trial-visual {
                width: 26px;
                height: 26px;
                font-size: 1.1em;
            }
            .bar-graph {
                height: 120px; 
                gap: 20px;
            }
            .bar-label {
                font-size: 0.75em;
                bottom: -20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Probability Experiment</h1>
        <p class="instructions">
            Welcome! This tool helps you understand probability through experimentation.
            Adjust the 'Number of Trials' and the 'Probability of Success' for each trial in the Setup area.
            Then, click 'Run Experiment' to see each trial unfold visually and observe the overall results in the Observation area.
        </p>

        <div class="app-area">
            <!-- Setup Area -->
            <div class="setup-area card">
                <h2>Setup Conditions</h2>
                <div>
                    <label for="numTrials">Number of Trials (1-10):</label>
                    <input type="number" id="numTrials" value="5" min="1" max="10">
                </div>
                <div>
                    <label for="probSuccess">Probability of Success (0-1):</label>
                    <input type="range" id="probSuccess" value="0.5" min="0" max="1" step="0.01">
                    <span id="probValue">0.50</span>
                </div>
                <button id="runExperimentBtn">Run Experiment</button>
            </div>

            <!-- Experiment Area -->
            <div class="experiment-area card">
                <h2>Experiment in Progress</h2>
                <div id="trialProgressContainer">
                    <p>Progress: <span id="currentTrialDisplay">0</span> / <span id="totalTrialsDisplay">0</span> trials</p>
                    <progress id="trialProgressBar" value="0" max="10"></progress>
                </div>
                <div id="visualTrials" class="visual-trials-container">
                    <!-- Trial visuals will appear here -->
                </div>
            </div>

            <!-- Observation Area -->
            <div class="observation-area card">
                <h2>Observation Results</h2>
                <div id="resultsText">
                    <p>Experimental Probability of Success: <span id="expProbSuccess">-</span></p>
                    <p>Total Successes: <span id="numSuccesses">-</span></p>
                    <p>Total Failures: <span id="numFailures">-</span></p>
                </div>
                <div class="bar-graph-container">
                    <div class="bar-graph">
                        <div class="bar success-bar" id="successBar">
                             <span class="bar-value" id="successBarValue">0</span>
                             <span class="bar-label">Successes</span>
                        </div>
                        <div class="bar failure-bar" id="failureBar">
                            <span class="bar-value" id="failureBarValue">0</span>
                            <span class="bar-label">Failures</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer>
        <p>Simulation by AI Language Model</p>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // DOM Elements
            const numTrialsInput = document.getElementById('numTrials');
            const probSuccessSlider = document.getElementById('probSuccess');
            const probValueSpan = document.getElementById('probValue');
            const runExperimentBtn = document.getElementById('runExperimentBtn');

            const currentTrialDisplay = document.getElementById('currentTrialDisplay');
            const totalTrialsDisplay = document.getElementById('totalTrialsDisplay');
            const trialProgressBar = document.getElementById('trialProgressBar');
            const visualTrialsContainer = document.getElementById('visualTrials');

            const expProbSuccessSpan = document.getElementById('expProbSuccess');
            const numSuccessesSpan = document.getElementById('numSuccesses');
            const numFailuresSpan = document.getElementById('numFailures');

            const successBar = document.getElementById('successBar');
            const failureBar = document.getElementById('failureBar');
            const successBarValue = document.getElementById('successBarValue');
            const failureBarValue = document.getElementById('failureBarValue');

            const MAX_BAR_HEIGHT = 150; // Corresponds to .bar-graph height in CSS

            // Initial setup
            function initializeApp() {
                probValueSpan.textContent = parseFloat(probSuccessSlider.value).toFixed(2);
                const initialTrials = parseInt(numTrialsInput.value);
                totalTrialsDisplay.textContent = initialTrials;
                trialProgressBar.max = initialTrials > 0 ? initialTrials : 1;
                trialProgressBar.value = 0;
                currentTrialDisplay.textContent = '0';
                resetObservationAreaDisplay();
            }
            
            function resetObservationAreaDisplay() {
                expProbSuccessSpan.textContent = '-';
                numSuccessesSpan.textContent = '-';
                numFailuresSpan.textContent = '-';
                
                successBar.style.height = '0px';
                failureBar.style.height = '0px';
                successBarValue.textContent = '0';
                failureBarValue.textContent = '0';
                successBar.classList.remove('has-value');
                failureBar.classList.remove('has-value');
            }

            // Event Listeners
            probSuccessSlider.addEventListener('input', () => {
                probValueSpan.textContent = parseFloat(probSuccessSlider.value).toFixed(2);
            });

            numTrialsInput.addEventListener('input', () => {
                let trials = parseInt(numTrialsInput.value);
                if (isNaN(trials) || trials < 1) trials = 1; // Default to 1 if invalid during input
                if (trials > 10) trials = 10; // Cap at 10
                numTrialsInput.value = trials; // Correct the input field if out of bounds

                 totalTrialsDisplay.textContent = trials;
                 trialProgressBar.max = trials;
                 
                 if (!runExperimentBtn.disabled) { // Only reset if not currently running
                    currentTrialDisplay.textContent = '0';
                    trialProgressBar.value = 0;
                    visualTrialsContainer.innerHTML = ''; // Clear visuals if settings change before run
                    resetObservationAreaDisplay(); // Clear old results
                 }
            });

            runExperimentBtn.addEventListener('click', runExperiment);

            function resetUIBeforeExperiment(totalTrialsValue) {
                visualTrialsContainer.innerHTML = ''; 
                
                currentTrialDisplay.textContent = '0';
                totalTrialsDisplay.textContent = totalTrialsValue;
                trialProgressBar.value = 0;
                trialProgressBar.max = totalTrialsValue;

                resetObservationAreaDisplay();
            }

            async function runExperiment() {
                runExperimentBtn.disabled = true;

                const numTrials = parseInt(numTrialsInput.value);
                const probSuccess = parseFloat(probSuccessSlider.value);

                // Validation (already somewhat handled by input type and numTrialsInput listener, but good for direct click)
                if (isNaN(numTrials) || numTrials < 1 || numTrials > 10) {
                    alert("Number of Trials must be a number between 1 and 10.");
                    numTrialsInput.value = Math.max(1, Math.min(10, numTrials || 1)); // Correct value
                    initializeApp(); // Re-initialize related displays
                    runExperimentBtn.disabled = false;
                    return;
                }
                if (isNaN(probSuccess) || probSuccess < 0 || probSuccess > 1) {
                    alert("Probability of Success must be a number between 0 and 1.");
                    probSuccessSlider.value = Math.max(0, Math.min(1, probSuccess || 0.5)); // Correct value
                    probValueSpan.textContent = parseFloat(probSuccessSlider.value).toFixed(2);
                    runExperimentBtn.disabled = false;
                    return;
                }

                resetUIBeforeExperiment(numTrials);

                let successCount = 0;
                let failureCount = 0;
                // Adjust delay: more trials = slightly faster per trial to keep total time reasonable
                const trialDelay = numTrials <= 3 ? 500 : (numTrials <= 7 ? 350 : 250); 

                for (let i = 0; i < numTrials; i++) {
                    await new Promise(resolve => setTimeout(resolve, trialDelay));

                    const isSuccess = Math.random() < probSuccess;
                    
                    const trialDiv = document.createElement('div');
                    trialDiv.classList.add('trial-visual');
                    if (isSuccess) {
                        successCount++;
                        trialDiv.classList.add('trial-success');
                    } else {
                        failureCount++;
                        trialDiv.classList.add('trial-failure');
                    }
                    visualTrialsContainer.appendChild(trialDiv);

                    currentTrialDisplay.textContent = i + 1;
                    trialProgressBar.value = i + 1;
                }

                const experimentalProb = numTrials > 0 ? (successCount / numTrials).toFixed(3) : '0.000';
                expProbSuccessSpan.textContent = experimentalProb;
                numSuccessesSpan.textContent = successCount;
                numFailuresSpan.textContent = failureCount;

                updateBarGraph(successCount, failureCount, numTrials);

                runExperimentBtn.disabled = false;
            }

            function updateBarGraph(successes, failures, totalTrials) {
                const successHeight = totalTrials > 0 ? (successes / totalTrials) * MAX_BAR_HEIGHT : 0;
                const failureHeight = totalTrials > 0 ? (failures / totalTrials) * MAX_BAR_HEIGHT : 0;
                const minHeightForValueDisplay = 20; // Minimum bar height to display value inside

                successBar.style.height = `${successHeight}px`;
                successBarValue.textContent = successes;
                if (successes > 0 && successHeight >= minHeightForValueDisplay) {
                     successBar.classList.add('has-value');
                } else {
                     successBar.classList.remove('has-value');
                }

                failureBar.style.height = `${failureHeight}px`;
                failureBarValue.textContent = failures;
                if (failures > 0 && failureHeight >= minHeightForValueDisplay) {
                    failureBar.classList.add('has-value');
                } else {
                    failureBar.classList.remove('has-value');
                }
            }
            
            initializeApp(); // Call initialization
        });
    </script>
</body>
</html>
