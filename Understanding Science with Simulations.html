<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Science Simulations</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 10px;
            background-color: #f4f7f6;
            color: #333;
        }
        .app-container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-top: 0;
            margin-bottom: 10px;
        }
        .intro-text {
            text-align: center;
            margin-bottom: 20px;
            font-size: 0.9em;
            color: #555;
        }
        .concept-buttons {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 8px;
            margin-bottom: 20px;
        }
        .concept-buttons button {
            padding: 8px 12px;
            font-size: 0.85em;
            cursor: pointer;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }
        .concept-buttons button:hover, .concept-buttons button.active {
            background-color: #2980b9;
        }
        .main-content {
            display: flex;
            gap: 15px;
        }
        .column {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fdfdfd;
            min-width: 0; 
        }
        .column h2 {
            margin-top: 0;
            font-size: 1.2em;
            color: #34495e;
            border-bottom: 1px solid #eee;
            padding-bottom: 8px;
            margin-bottom: 10px;
        }
        #realWorldText {
            width: 100%;
            min-height: 300px;
            box-sizing: border-box;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 0.9em;
        }
        #simulation-area {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        #simulation-placeholder {
            min-height: 340px; 
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #777;
            font-style: italic;
            text-align: center;
            padding: 10px;
            box-sizing: border-box;
        }
        #simulation-controls-container {
            margin-bottom: 10px;
            width: 100%;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .control-group {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 8px;
            font-size: 0.85em;
        }
        .control-group label {
            min-width: 100px;
            flex-shrink: 0;
        }
        .control-group input[type="range"] {
            flex-grow: 1;
            min-width: 120px;
        }
        .control-group output {
            min-width: 40px; /* Increased slightly for units */
            text-align: right;
            font-weight: bold;
        }
        #simulation-canvas {
            border: 1px solid #ccc;
            display: none;
            background-color: #fff;
            max-width: 100%; 
            height: auto; 
        }
        #ohms-law-display {
            font-size: 1em;
            text-align: center;
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 5px;
            background-color: #e9f5ff;
            min-height: 300px;
            width: 100%;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        #ohms-law-display p { margin: 5px 0; }
        #ohms-law-display .formula { margin-top: 20px; font-size: 0.9em; color: #555; }


        footer {
            text-align: center;
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid #eee;
            font-size: 0.8em;
            color: #777;
        }

        @media (max-width: 768px) {
            body { padding: 5px; }
            .app-container { padding: 10px; }
            .main-content {
                flex-direction: column;
            }
            .concept-buttons button {
                flex-grow: 1; 
                min-width: 120px; 
            }
             #realWorldText {
                min-height: 150px;
            }
            #simulation-placeholder, #ohms-law-display {
                min-height: 200px;
            }
        }
        @media (max-width: 480px) {
            .concept-buttons {
                gap: 5px;
            }
            .concept-buttons button {
                width: calc(50% - 5px); /* Two buttons per row */
                font-size: 0.8em;
                padding: 8px 5px; /* Adjust padding for smaller buttons */
            }
            .control-group label {
                min-width: 100%; /* Stack label above slider */
                margin-bottom: -2px; /* Reduce gap slightly */
            }
            .control-group input[type="range"] {
                width: 100%;
            }
            .control-group output {
                width: 100%;
                text-align: left;
                margin-top: -5px;
            }
            h1 { font-size: 1.5em; }
            .column h2 { font-size: 1.1em; }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <h1>Understanding Science with Simulations</h1>
        <p class="intro-text">Use this tool to explore scientific concepts. First, jot down your thoughts, questions, or observations about a concept in the "Real World" column. Then, interact with the simulation to see the concept in action. Compare your initial thoughts with the simulated behavior.</p>

        <div class="concept-buttons">
            <button data-concept="pendulum">Pendulum Motion</button>
            <button data-concept="rolling">Rolling Motion</button>
            <button data-concept="projectile">Projectile Motion</button>
            <button data-concept="shm">Simple Harmonic Motion</button>
            <button data-concept="ohms">Ohm's Law</button>
            <button data-concept="wave">Wave Interference</button>
        </div>

        <div class="main-content">
            <div class="column real-world-column">
                <h2>Real World</h2>
                <p>Your observations, questions, feelings about <strong id="current-concept-rw">the concept</strong>:</p>
                <textarea id="realWorldText" placeholder="Type here..."></textarea>
            </div>
            <div class="column simulation-column">
                <h2>Simulation: <span id="current-concept-sim">Select a concept</span></h2>
                <div id="simulation-area">
                    <p id="simulation-placeholder">Select a concept above to load the simulation.</p>
                    <div id="simulation-controls-container"></div>
                    <canvas id="simulation-canvas"></canvas>
                </div>
            </div>
        </div>
        <footer>
            <p>App to demonstrate the value of simulations in learning.</p>
        </footer>
    </div>

    <script>
        // Global variable to store the current animation request ID
        let currentAnimationId = null;
        let currentSim = null; // To manage simulation state and stop function

        // DOM Elements
        const realWorldText = document.getElementById('realWorldText');
        const conceptButtons = document.querySelectorAll('.concept-buttons button');
        const simulationArea = document.getElementById('simulation-area');
        const simulationControlsContainer = document.getElementById('simulation-controls-container');
        const simulationCanvas = document.getElementById('simulation-canvas');
        const simulationPlaceholder = document.getElementById('simulation-placeholder');
        const currentConceptRW = document.getElementById('current-concept-rw');
        const currentConceptSim = document.getElementById('current-concept-sim');
        const canvasCtx = simulationCanvas.getContext('2d');

        const CANVAS_WIDTH = 400;
        const CANVAS_HEIGHT = 300;
        simulationCanvas.width = CANVAS_WIDTH;
        simulationCanvas.height = CANVAS_HEIGHT;

        // Utility to create controls
        function createSlider(id, label, min, max, step, value, unit = '') {
            const controlGroup = document.createElement('div');
            controlGroup.classList.add('control-group');
            
            const labelEl = document.createElement('label');
            labelEl.setAttribute('for', id);
            labelEl.textContent = label; // Removed colon for better stacking on mobile
            
            const sliderEl = document.createElement('input');
            sliderEl.type = 'range';
            sliderEl.id = id;
            sliderEl.min = min;
            sliderEl.max = max;
            sliderEl.step = step;
            sliderEl.value = value;
            
            const outputEl = document.createElement('output');
            outputEl.textContent = value + unit;
            // The oninput for sliderEl will be set by the specific simulation setup function
            // to include both updating the output and triggering sim changes.
            
            controlGroup.appendChild(labelEl);
            controlGroup.appendChild(sliderEl);
            controlGroup.appendChild(outputEl);
            simulationControlsContainer.appendChild(controlGroup);
            return sliderEl;
        }

        function createButton(id, text, onClick) {
            const buttonEl = document.createElement('button');
            buttonEl.id = id;
            buttonEl.textContent = text;
            buttonEl.onclick = onClick;
            buttonEl.style.padding = '8px 12px'; // Style similar to concept buttons
            buttonEl.style.backgroundColor = '#27ae60';
            buttonEl.style.color = 'white';
            buttonEl.style.border = 'none';
            buttonEl.style.borderRadius = '4px';
            buttonEl.style.cursor = 'pointer';
            simulationControlsContainer.appendChild(buttonEl);
            return buttonEl;
        }
        
        function createSelect(id, label, options, value) {
            const controlGroup = document.createElement('div');
            controlGroup.classList.add('control-group');

            const labelEl = document.createElement('label');
            labelEl.setAttribute('for', id);
            labelEl.textContent = label;

            const selectEl = document.createElement('select');
            selectEl.id = id;
            options.forEach(opt => {
                const optionEl = document.createElement('option');
                optionEl.value = opt.value;
                optionEl.textContent = opt.text;
                if (opt.value === value) {
                    optionEl.selected = true;
                }
                selectEl.appendChild(optionEl);
            });
            
            controlGroup.appendChild(labelEl);
            controlGroup.appendChild(selectEl);
            simulationControlsContainer.appendChild(controlGroup);
            return selectEl;
        }

        function clearSimulation() {
            if (currentAnimationId) {
                cancelAnimationFrame(currentAnimationId);
                currentAnimationId = null;
            }
            if (currentSim && typeof currentSim.stop === 'function') {
                currentSim.stop();
            }
            currentSim = null;
            canvasCtx.clearRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
            simulationControlsContainer.innerHTML = '';
            simulationCanvas.style.display = 'none';
            simulationPlaceholder.style.display = 'flex';
            const ohmsDisplay = document.getElementById('ohms-law-display');
            if (ohmsDisplay) ohmsDisplay.remove();
        }

        conceptButtons.forEach(button => {
            button.addEventListener('click', () => {
                const concept = button.getAttribute('data-concept');
                const conceptName = button.textContent;

                conceptButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');

                realWorldText.value = '';
                currentConceptRW.textContent = conceptName.toLowerCase();
                currentConceptSim.textContent = conceptName;
                
                clearSimulation();
                loadSimulation(concept);
            });
        });

        function loadSimulation(concept) {
            simulationPlaceholder.style.display = 'none';
            switch (concept) {
                case 'pendulum': setupPendulumSimulation(); break;
                case 'rolling': setupRollingMotionSimulation(); break;
                case 'projectile': setupProjectileMotionSimulation(); break;
                case 'shm': setupSHMSimulation(); break;
                case 'ohms': setupOhmsLawSimulation(); break;
                case 'wave': setupWaveInterferenceSimulation(); break;
                default:
                    simulationPlaceholder.style.display = 'flex';
                    simulationPlaceholder.textContent = 'Simulation not found.';
            }
        }

        // --- Simulation Implementations ---

        function setupPendulumSimulation() {
            simulationCanvas.style.display = 'block';
            const lengthSlider = createSlider('pendulumLength', 'Length (L)', 0.5, 2, 0.1, 1, ' m');
            const angleSlider = createSlider('pendulumAngle', 'Initial Angle (θ₀)', -45, 45, 1, 15, '°');
            const massSlider = createSlider('pendulumMass', 'Mass (m)', 0.1, 2, 0.1, 0.5, ' kg');
            
            let L, theta_rad, mass_val;
            let angleVelocity = 0;
            const g = 9.81; 
            const pivotX = CANVAS_WIDTH / 2;
            const pivotY = 50;
            const timeStep = 0.016; 

            function drawPendulum() {
                canvasCtx.clearRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
                
                const L_meters = L / 100; // L is in pixels for drawing convenience
                let angleAcceleration = -(g / L_meters) * Math.sin(theta_rad);
                angleVelocity += angleAcceleration * timeStep;
                angleVelocity *= 0.995; // Damping
                theta_rad += angleVelocity * timeStep;

                const bobX = pivotX + L * Math.sin(theta_rad);
                const bobY = pivotY + L * Math.cos(theta_rad);

                canvasCtx.beginPath();
                canvasCtx.moveTo(pivotX, pivotY);
                canvasCtx.lineTo(bobX, bobY);
                canvasCtx.strokeStyle = '#333';
                canvasCtx.lineWidth = 2;
                canvasCtx.stroke();

                canvasCtx.beginPath();
                canvasCtx.arc(pivotX, pivotY, 5, 0, 2 * Math.PI);
                canvasCtx.fillStyle = '#555';
                canvasCtx.fill();

                canvasCtx.beginPath();
                canvasCtx.arc(bobX, bobY, 8 + mass_val * 4, 0, 2 * Math.PI);
                canvasCtx.fillStyle = '#3498db';
                canvasCtx.fill();
                
                currentAnimationId = requestAnimationFrame(drawPendulum);
            }

            function resetAndRun() {
                if (currentAnimationId) cancelAnimationFrame(currentAnimationId);
                L = parseFloat(lengthSlider.value) * 100; // scale factor: 1m = 100px
                theta_rad = parseFloat(angleSlider.value) * Math.PI / 180;
                mass_val = parseFloat(massSlider.value);
                angleVelocity = 0;
                drawPendulum();
            }
            
            lengthSlider.oninput = () => { lengthSlider.nextElementSibling.textContent = lengthSlider.value + ' m'; resetAndRun(); };
            angleSlider.oninput = () => { angleSlider.nextElementSibling.textContent = angleSlider.value + '°'; resetAndRun(); };
            massSlider.oninput = () => { massSlider.nextElementSibling.textContent = massSlider.value + ' kg'; resetAndRun(); };
            
            currentSim = { stop: () => { if (currentAnimationId) cancelAnimationFrame(currentAnimationId); } };
            resetAndRun();
        }

        function setupProjectileMotionSimulation() {
            simulationCanvas.style.display = 'block';
            const velocitySlider = createSlider('projVelocity', 'Initial Velocity (v₀)', 5, 50, 1, 25, ' m/s');
            const angleSlider = createSlider('projAngle', 'Launch Angle (α)', 0, 90, 1, 45, '°');
            
            let particles = [];
            const g = 9.81; 
            const scale = 5; // 1 meter = 5 pixels
            const groundY = CANVAS_HEIGHT - 20;

            function launchProjectile() {
                let v0 = parseFloat(velocitySlider.value);
                let angleRad = parseFloat(angleSlider.value) * Math.PI / 180;
                
                const x0 = 20;
                const y0 = groundY;
                
                const v0x = v0 * Math.cos(angleRad);
                const v0y = v0 * Math.sin(angleRad);

                particles.push({ x0, y0, v0x, v0y, t: 0, path: [{x: x0, y: y0}], landed: false });
                if (particles.length > 5) particles.shift();
            }

            createButton('launchProjectileBtn', 'Launch', launchProjectile);

            function drawProjectile() {
                canvasCtx.clearRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);

                canvasCtx.beginPath();
                canvasCtx.moveTo(0, groundY);
                canvasCtx.lineTo(CANVAS_WIDTH, groundY);
                canvasCtx.strokeStyle = 'green';
                canvasCtx.lineWidth = 2;
                canvasCtx.stroke();

                particles.forEach((p, particleIndex) => {
                    if (!p.landed) {
                        p.t += 0.05; // time step, smaller for smoother trajectory
                        
                        let currentX = p.x0 + p.v0x * p.t * scale;
                        let currentY = p.y0 - (p.v0y * p.t - 0.5 * g * p.t * p.t) * scale;

                        if (currentY >= groundY) {
                            currentY = groundY;
                            p.landed = true; 
                        }
                        if (currentX > CANVAS_WIDTH || currentX < 0) {
                            p.landed = true; // Landed if off-screen
                        }
                        p.path.push({x: currentX, y: currentY});
                    }
                    
                    canvasCtx.beginPath();
                    canvasCtx.moveTo(p.path[0].x, p.path[0].y);
                    for (let i = 1; i < p.path.length; i++) {
                        canvasCtx.lineTo(p.path[i].x, p.path[i].y);
                    }
                    canvasCtx.strokeStyle = `rgba(52, 152, 219, ${1 - (particles.length - 1 - particleIndex) * 0.2})`;
                    canvasCtx.lineWidth = 2;
                    canvasCtx.stroke();

                    const lastPoint = p.path[p.path.length - 1];
                    if (lastPoint.y <= groundY && lastPoint.x < CANVAS_WIDTH && lastPoint.x > 0) {
                        canvasCtx.beginPath();
                        canvasCtx.arc(lastPoint.x, lastPoint.y, 5, 0, 2 * Math.PI);
                        canvasCtx.fillStyle = '#e74c3c';
                        canvasCtx.fill();
                    }
                });
                
                currentAnimationId = requestAnimationFrame(drawProjectile);
            }
            
            velocitySlider.oninput = () => { velocitySlider.nextElementSibling.textContent = velocitySlider.value + ' m/s'; };
            angleSlider.oninput = () => { angleSlider.nextElementSibling.textContent = angleSlider.value + '°'; };

            currentSim = { stop: () => { if (currentAnimationId) cancelAnimationFrame(currentAnimationId); particles = []; } };
            drawProjectile(); 
        }

        function setupOhmsLawSimulation() {
            simulationCanvas.style.display = 'none';
            
            const displayDiv = document.createElement('div');
            displayDiv.id = 'ohms-law-display';
            simulationArea.insertBefore(displayDiv, simulationControlsContainer.nextSibling);

            const voltageSlider = createSlider('ohmsVoltage', 'Voltage (V)', 1, 24, 0.5, 12, ' V');
            const resistanceSlider = createSlider('ohmsResistance', 'Resistance (R)', 1, 100, 1, 50, ' Ω');
            
            displayDiv.innerHTML = `
                <p>Voltage (V) = <span id="vVal">12.0</span> V</p>
                <p>Resistance (R) = <span id="rVal">50</span> Ω</p>
                <p style="font-weight: bold; font-size: 1.4em; margin-top: 15px;">Current (I) = <span id="iVal">0.24</span> A</p>
                <p class="formula">Ohm's Law: V = I × R</p>
            `;

            function updateOhmDisplay() {
                let V = parseFloat(voltageSlider.value);
                let R = parseFloat(resistanceSlider.value);
                let I = V / R;
                
                document.getElementById('vVal').textContent = V.toFixed(1);
                document.getElementById('rVal').textContent = R.toFixed(0);
                document.getElementById('iVal').textContent = I.toFixed(2);

                voltageSlider.nextElementSibling.textContent = V.toFixed(1) + ' V';
                resistanceSlider.nextElementSibling.textContent = R.toFixed(0) + ' Ω';
            }
            
            voltageSlider.oninput = updateOhmDisplay;
            resistanceSlider.oninput = updateOhmDisplay;
            
            currentSim = { stop: () => {} };
            updateOhmDisplay();
        }

        function setupSHMSimulation() {
            simulationCanvas.style.display = 'block';
            const massSlider = createSlider('shmMass', 'Mass (m)', 0.1, 2, 0.1, 0.5, ' kg');
            const springKSlider = createSlider('shmK', 'Spring Constant (k)', 1, 20, 0.5, 10, ' N/m');
            const displacementSlider = createSlider('shmDisp', 'Initial Displacement (A)', -50, 50, 5, 30, ' px');

            let m, k_spring, A_disp;
            let y_pos, velocity;
            const equilibriumY = CANVAS_HEIGHT / 2;
            const springTop = 50;
            const timeStep = 0.02;

            function resetSHM() {
                if (currentAnimationId) cancelAnimationFrame(currentAnimationId);
                m = parseFloat(massSlider.value);
                k_spring = parseFloat(springKSlider.value);
                A_disp = parseFloat(displacementSlider.value);
                
                y_pos = A_disp; 
                velocity = 0;
                animateSHM();
            }

            function animateSHM() {
                const acceleration = -(k_spring / m) * y_pos;
                velocity += acceleration * timeStep;
                velocity *= 0.99; // Damping
                y_pos += velocity * timeStep;

                canvasCtx.clearRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);

                const springBottomY = equilibriumY + y_pos;
                const numSegments = 15;
                const springWidth = 15;
                
                canvasCtx.beginPath();
                canvasCtx.moveTo(CANVAS_WIDTH / 2, springTop);
                for (let i = 0; i < numSegments; i++) {
                    const segmentY = springTop + (i + 0.5) * ((springBottomY - springTop) / numSegments);
                    const xOffset = (i % 2 === 0) ? springWidth : -springWidth;
                    canvasCtx.lineTo(CANVAS_WIDTH / 2 + xOffset, segmentY);
                }
                canvasCtx.lineTo(CANVAS_WIDTH / 2, springBottomY);
                canvasCtx.strokeStyle = '#555';
                canvasCtx.lineWidth = 1.5;
                canvasCtx.stroke();

                const massSize = 12 + m * 5;
                canvasCtx.beginPath();
                canvasCtx.rect(CANVAS_WIDTH / 2 - massSize / 2, springBottomY, massSize, massSize);
                canvasCtx.fillStyle = '#2ecc71';
                canvasCtx.fill();
                
                canvasCtx.beginPath();
                canvasCtx.moveTo(CANVAS_WIDTH/2 - 30, equilibriumY);
                canvasCtx.lineTo(CANVAS_WIDTH/2 + 30, equilibriumY);
                canvasCtx.strokeStyle = 'rgba(0,0,0,0.3)';
                canvasCtx.setLineDash([5, 5]);
                canvasCtx.stroke();
                canvasCtx.setLineDash([]);

                currentAnimationId = requestAnimationFrame(animateSHM);
            }

            massSlider.oninput = () => { massSlider.nextElementSibling.textContent = massSlider.value + ' kg'; resetSHM(); };
            springKSlider.oninput = () => { springKSlider.nextElementSibling.textContent = springKSlider.value + ' N/m'; resetSHM(); };
            displacementSlider.oninput = () => { displacementSlider.nextElementSibling.textContent = displacementSlider.value + ' px'; resetSHM(); };
            
            currentSim = { stop: () => { if (currentAnimationId) cancelAnimationFrame(currentAnimationId); } };
            resetSHM();
        }

        function setupRollingMotionSimulation() {
            simulationCanvas.style.display = 'block';
            const angleSlider = createSlider('rollAngle', 'Incline Angle (θ)', 5, 45, 1, 20, '°');
            const objectTypeSelect = createSelect('rollObjectType', 'Object Type', [
                { value: '0.4', text: 'Solid Sphere' }, // I_factor = I/(mr^2)
                { value: '0.5', text: 'Solid Cylinder' },
                { value: '1.0', text: 'Hoop' }
            ], '0.4');

            let angle_rad, I_factor;
            let s, v_mps, a_mps2; // s: distance (px), v: velocity (m/s), a: accel (m/s^2)
            const g = 9.81; 
            const physScale = 20; // 20 pixels = 1 meter for physics calcs if needed, but mostly for v->s conversion.
            const objectRadiusPx = 15; 
            
            let rampPathStartX, rampPathStartY, rampPathEndX, rampPathEndY, rampTravelLengthPx;

            function resetRolling() {
                if (currentAnimationId) cancelAnimationFrame(currentAnimationId);
                
                angle_rad = parseFloat(angleSlider.value) * Math.PI / 180;
                I_factor = parseFloat(objectTypeSelect.value);

                rampPathStartX = 50 + objectRadiusPx * Math.sin(angle_rad); 
                rampPathStartY = 50 + objectRadiusPx * Math.cos(angle_rad);

                let maxHorizDist = CANVAS_WIDTH - rampPathStartX - 30 - objectRadiusPx * Math.sin(angle_rad);
                let maxVertDist = CANVAS_HEIGHT - rampPathStartY - 20 - objectRadiusPx - objectRadiusPx * Math.cos(angle_rad);
                
                let travelLengthByX = (angle_rad < Math.PI/2 - 0.01) ? maxHorizDist / Math.cos(angle_rad) : Infinity;
                let travelLengthByY = (angle_rad > 0.01) ? maxVertDist / Math.sin(angle_rad) : Infinity;
                
                rampTravelLengthPx = Math.min(travelLengthByX, travelLengthByY, CANVAS_WIDTH * 0.7);
                if (angle_rad < 0.01) rampTravelLengthPx = maxHorizDist; // Horizontal case

                rampPathEndX = rampPathStartX + rampTravelLengthPx * Math.cos(angle_rad);
                rampPathEndY = rampPathStartY + rampTravelLengthPx * Math.sin(angle_rad);

                s = 0; 
                v_mps = 0; 
                a_mps2 = (g * Math.sin(angle_rad)) / (1 + I_factor);
                
                animateRolling();
            }

            function animateRolling() {
                const simTimeStep = 0.02; 
                v_mps += a_mps2 * simTimeStep;
                s += v_mps * simTimeStep * physScale; // Convert m/s to px/s for distance

                canvasCtx.clearRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);

                let surfaceP1X = rampPathStartX - objectRadiusPx * Math.sin(angle_rad);
                let surfaceP1Y = rampPathStartY + objectRadiusPx * Math.cos(angle_rad);
                let surfaceP2X = rampPathEndX - objectRadiusPx * Math.sin(angle_rad);
                let surfaceP2Y = rampPathEndY + objectRadiusPx * Math.cos(angle_rad);
                
                canvasCtx.beginPath();
                canvasCtx.moveTo(surfaceP1X, surfaceP1Y); 
                canvasCtx.lineTo(surfaceP2X, surfaceP2Y);
                const groundLevel = CANVAS_HEIGHT - 20;
                if (surfaceP2Y < groundLevel) canvasCtx.lineTo(surfaceP2X, groundLevel);
                if (surfaceP1Y < groundLevel) canvasCtx.lineTo(surfaceP1X, groundLevel);
                canvasCtx.closePath();
                canvasCtx.fillStyle = '#ccc';
                canvasCtx.fill();
                canvasCtx.strokeStyle = '#333';
                canvasCtx.stroke();

                let objCenterX = rampPathStartX + s * Math.cos(angle_rad);
                let objCenterY = rampPathStartY + s * Math.sin(angle_rad);

                canvasCtx.beginPath();
                canvasCtx.arc(objCenterX, objCenterY, objectRadiusPx, 0, 2 * Math.PI);
                canvasCtx.fillStyle = '#e67e22';
                canvasCtx.fill();
                
                let rotationAngleRad = s / objectRadiusPx; 
                canvasCtx.beginPath();
                canvasCtx.moveTo(objCenterX, objCenterY);
                canvasCtx.lineTo(objCenterX + objectRadiusPx * Math.cos(rotationAngleRad), objCenterY + objectRadiusPx * Math.sin(rotationAngleRad));
                canvasCtx.strokeStyle = '#000';
                canvasCtx.lineWidth = 2;
                canvasCtx.stroke();

                if (s < rampTravelLengthPx - 1) { // -1 to avoid overshooting slightly
                    currentAnimationId = requestAnimationFrame(animateRolling);
                }
            }

            angleSlider.oninput = () => { angleSlider.nextElementSibling.textContent = angleSlider.value + '°'; resetRolling(); };
            objectTypeSelect.onchange = resetRolling;
            createButton('resetRollingBtn', 'Reset/Run', resetRolling);

            currentSim = { stop: () => { if (currentAnimationId) cancelAnimationFrame(currentAnimationId); } };
            resetRolling();
        }

        function setupWaveInterferenceSimulation() {
            simulationCanvas.style.display = 'block';
            const amp1S = createSlider('waveAmp1', 'Amplitude 1', 0.1, 1.0, 0.1, 0.5, '');
            const wl1S = createSlider('waveWl1', 'Wavelength 1 (λ₁)', 50, 200, 10, 100, 'px');
            const amp2S = createSlider('waveAmp2', 'Amplitude 2', 0.1, 1.0, 0.1, 0.5, '');
            const wl2S = createSlider('waveWl2', 'Wavelength 2 (λ₂)', 50, 200, 10, 120, 'px');
            const phase2S = createSlider('wavePhase2', 'Phase 2 (φ₂)', 0, 360, 15, 0, '°');

            let A1, k1, A2, k2, phi2_rad;
            let animTime = 0;

            function drawWaves() {
                canvasCtx.clearRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
                A1 = parseFloat(amp1S.value) * 40; // Scale amplitude
                k1 = 2 * Math.PI / parseFloat(wl1S.value);
                A2 = parseFloat(amp2S.value) * 40;
                k2 = 2 * Math.PI / parseFloat(wl2S.value);
                phi2_rad = parseFloat(phase2S.value) * Math.PI / 180;

                const centerY = CANVAS_HEIGHT / 2;
                const omega = 0.05; 

                // Wave 1
                canvasCtx.beginPath(); canvasCtx.strokeStyle = 'blue'; canvasCtx.lineWidth = 1.5;
                for (let x = 0; x < CANVAS_WIDTH; x++) {
                    const y = centerY - A1 * Math.sin(k1 * x - omega * animTime);
                    if (x === 0) canvasCtx.moveTo(x, y); else canvasCtx.lineTo(x, y);
                }
                canvasCtx.stroke();

                // Wave 2
                canvasCtx.beginPath(); canvasCtx.strokeStyle = 'red'; canvasCtx.lineWidth = 1.5;
                for (let x = 0; x < CANVAS_WIDTH; x++) {
                    const y = centerY - A2 * Math.sin(k2 * x - omega * animTime + phi2_rad);
                    if (x === 0) canvasCtx.moveTo(x, y); else canvasCtx.lineTo(x, y);
                }
                canvasCtx.stroke();

                // Superposition
                canvasCtx.beginPath(); canvasCtx.strokeStyle = 'purple'; canvasCtx.lineWidth = 2.5;
                for (let x = 0; x < CANVAS_WIDTH; x++) {
                    const y_sum = centerY - (A1 * Math.sin(k1 * x - omega * animTime) + A2 * Math.sin(k2 * x - omega * animTime + phi2_rad));
                    if (x === 0) canvasCtx.moveTo(x, y_sum); else canvasCtx.lineTo(x, y_sum);
                }
                canvasCtx.stroke();
                
                canvasCtx.beginPath(); canvasCtx.moveTo(0, centerY); canvasCtx.lineTo(CANVAS_WIDTH, centerY);
                canvasCtx.strokeStyle = '#aaa'; canvasCtx.lineWidth = 1; canvasCtx.stroke();

                animTime++;
                currentAnimationId = requestAnimationFrame(drawWaves);
            }
            
            function updateAndRedrawWaves() {
                if (currentAnimationId) cancelAnimationFrame(currentAnimationId);
                amp1S.nextElementSibling.textContent = amp1S.value;
                wl1S.nextElementSibling.textContent = wl1S.value + 'px';
                amp2S.nextElementSibling.textContent = amp2S.value;
                wl2S.nextElementSibling.textContent = wl2S.value + 'px';
                phase2S.nextElementSibling.textContent = phase2S.value + '°';
                animTime = 0; 
                drawWaves();
            }

            [amp1S, wl1S, amp2S, wl2S, phase2S].forEach(s => s.oninput = updateAndRedrawWaves);
            currentSim = { stop: () => { if (currentAnimationId) cancelAnimationFrame(currentAnimationId); } };
            updateAndRedrawWaves();
        }

    </script>
</body>
</html>
