<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Physics Assessment Center - Physics Virtual Lab</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/components.css">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: var(--font-family-sans);
            margin: 0;
            padding: 0;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .assessment-header {
            background: linear-gradient(135deg, var(--assessment-color), var(--primary-600));
            color: white;
            padding: var(--spacing-xl) 0;
            text-align: center;
        }

        .assessment-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: var(--spacing-sm);
        }

        .assessment-subtitle {
            font-size: 1.125rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .main-content {
            flex: 1;
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--spacing-xl);
            width: 100%;
        }

        .assessment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: var(--spacing-xl);
            margin-top: var(--spacing-xl);
        }

        .assessment-card {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            transition: all var(--transition-normal);
        }

        [data-theme="dark"] .assessment-card {
            background: var(--gray-700);
        }

        .assessment-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .card-header {
            padding: var(--spacing-lg);
            background: var(--assessment-color);
            color: white;
            text-align: center;
        }

        .card-icon {
            font-size: 3rem;
            margin-bottom: var(--spacing-sm);
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
        }

        .card-body {
            padding: var(--spacing-lg);
        }

        .card-description {
            color: var(--text-secondary);
            margin-bottom: var(--spacing-lg);
            line-height: 1.6;
        }

        .assessment-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .stat-item {
            text-align: center;
            padding: var(--spacing-sm);
            background: var(--bg-secondary);
            border-radius: var(--radius-md);
        }

        .stat-value {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary-600);
        }

        .stat-label {
            font-size: 0.75rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .difficulty-indicator {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-size: 0.75rem;
            font-weight: 500;
            margin-bottom: var(--spacing-md);
        }

        .difficulty-beginner {
            background: var(--beginner-color);
            color: white;
        }

        .difficulty-intermediate {
            background: var(--intermediate-color);
            color: white;
        }

        .difficulty-advanced {
            background: var(--advanced-color);
            color: white;
        }

        .start-assessment-btn {
            width: 100%;
            background: var(--assessment-color);
            color: white;
            border: none;
            padding: var(--spacing-md);
            border-radius: var(--radius-lg);
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .start-assessment-btn:hover {
            background: var(--accent-700);
            transform: translateY(-2px);
        }

        .start-assessment-btn:disabled {
            background: var(--gray-400);
            cursor: not-allowed;
            transform: none;
        }

        .progress-section {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            padding: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
        }

        [data-theme="dark"] .progress-section {
            background: var(--gray-700);
        }

        .progress-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-lg);
            text-align: center;
        }

        .overall-progress {
            display: flex;
            align-items: center;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }

        .progress-circle {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: conic-gradient(var(--assessment-color) 0deg, var(--assessment-color) var(--progress-angle, 0deg), var(--gray-200) var(--progress-angle, 0deg));
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .progress-circle::before {
            content: '';
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: var(--bg-primary);
            position: absolute;
        }

        .progress-percentage {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--assessment-color);
            z-index: 1;
        }

        .progress-details {
            flex: 1;
        }

        .progress-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-sm);
        }

        .progress-bar {
            width: 200px;
            height: 8px;
            background: var(--gray-200);
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: var(--assessment-color);
            border-radius: 4px;
            transition: width var(--transition-normal);
        }

        @media (max-width: 768px) {
            .assessment-grid {
                grid-template-columns: 1fr;
            }
            
            .overall-progress {
                flex-direction: column;
                text-align: center;
            }
            
            .progress-bar {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <header id="main-header"></header>

    <div class="assessment-header animate-fade-in">
        <h1 class="assessment-title">🎓 Physics Assessment Center</h1>
        <p class="assessment-subtitle">
            Test your understanding with interactive quizzes and practical assessments
        </p>
    </div>

    <div class="main-content">
        <div class="progress-section animate-slide-up">
            <h2 class="progress-title">Your Assessment Progress</h2>
            <div class="overall-progress">
                <div class="progress-circle" style="--progress-angle: 108deg;">
                    <span class="progress-percentage">30%</span>
                </div>
                <div class="progress-details">
                    <div class="progress-item">
                        <span>Mechanics Fundamentals</span>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 75%"></div>
                        </div>
                        <span>75%</span>
                    </div>
                    <div class="progress-item">
                        <span>Electromagnetism</span>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 40%"></div>
                        </div>
                        <span>40%</span>
                    </div>
                    <div class="progress-item">
                        <span>Waves & Oscillations</span>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 15%"></div>
                        </div>
                        <span>15%</span>
                    </div>
                    <div class="progress-item">
                        <span>Modern Physics</span>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                        <span>0%</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="assessment-grid">
            <div class="assessment-card animate-slide-up animate-stagger-1">
                <div class="card-header">
                    <div class="card-icon">⚖️</div>
                    <h3 class="card-title">Mechanics Fundamentals</h3>
                </div>
                <div class="card-body">
                    <div class="difficulty-indicator difficulty-beginner">
                        <span>●</span> Beginner Level
                    </div>
                    <p class="card-description">
                        Test your understanding of basic mechanics concepts including motion, forces, energy, and momentum.
                    </p>
                    <div class="assessment-stats">
                        <div class="stat-item">
                            <div class="stat-value">15</div>
                            <div class="stat-label">Questions</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">20</div>
                            <div class="stat-label">Minutes</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">75%</div>
                            <div class="stat-label">Best Score</div>
                        </div>
                    </div>
                    <button class="start-assessment-btn" onclick="startAssessment('mechanics')">
                        Start Assessment
                    </button>
                </div>
            </div>

            <div class="assessment-card animate-slide-up animate-stagger-2">
                <div class="card-header">
                    <div class="card-icon">⚡</div>
                    <h3 class="card-title">Electromagnetism</h3>
                </div>
                <div class="card-body">
                    <div class="difficulty-indicator difficulty-intermediate">
                        <span>●</span> Intermediate Level
                    </div>
                    <p class="card-description">
                        Evaluate your knowledge of electric circuits, magnetic fields, and electromagnetic phenomena.
                    </p>
                    <div class="assessment-stats">
                        <div class="stat-item">
                            <div class="stat-value">20</div>
                            <div class="stat-label">Questions</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">25</div>
                            <div class="stat-label">Minutes</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">40%</div>
                            <div class="stat-label">Best Score</div>
                        </div>
                    </div>
                    <button class="start-assessment-btn" onclick="startAssessment('electromagnetism')">
                        Start Assessment
                    </button>
                </div>
            </div>

            <div class="assessment-card animate-slide-up animate-stagger-3">
                <div class="card-header">
                    <div class="card-icon">🌊</div>
                    <h3 class="card-title">Waves & Oscillations</h3>
                </div>
                <div class="card-body">
                    <div class="difficulty-indicator difficulty-intermediate">
                        <span>●</span> Intermediate Level
                    </div>
                    <p class="card-description">
                        Assess your understanding of wave properties, sound, light, and oscillatory motion.
                    </p>
                    <div class="assessment-stats">
                        <div class="stat-item">
                            <div class="stat-value">18</div>
                            <div class="stat-label">Questions</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">22</div>
                            <div class="stat-label">Minutes</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">15%</div>
                            <div class="stat-label">Best Score</div>
                        </div>
                    </div>
                    <button class="start-assessment-btn" onclick="startAssessment('waves')">
                        Start Assessment
                    </button>
                </div>
            </div>

            <div class="assessment-card animate-slide-up animate-stagger-4">
                <div class="card-header">
                    <div class="card-icon">⚛️</div>
                    <h3 class="card-title">Modern Physics</h3>
                </div>
                <div class="card-body">
                    <div class="difficulty-indicator difficulty-advanced">
                        <span>●</span> Advanced Level
                    </div>
                    <p class="card-description">
                        Challenge yourself with quantum mechanics, relativity, and atomic physics concepts.
                    </p>
                    <div class="assessment-stats">
                        <div class="stat-item">
                            <div class="stat-value">25</div>
                            <div class="stat-label">Questions</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">35</div>
                            <div class="stat-label">Minutes</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">0%</div>
                            <div class="stat-label">Best Score</div>
                        </div>
                    </div>
                    <button class="start-assessment-btn" onclick="startAssessment('modern')" disabled>
                        Complete Prerequisites First
                    </button>
                </div>
            </div>

            <div class="assessment-card animate-slide-up animate-stagger-5">
                <div class="card-header">
                    <div class="card-icon">🧪</div>
                    <h3 class="card-title">Practical Lab Skills</h3>
                </div>
                <div class="card-body">
                    <div class="difficulty-indicator difficulty-beginner">
                        <span>●</span> Beginner Level
                    </div>
                    <p class="card-description">
                        Demonstrate your ability to conduct virtual experiments and analyze data effectively.
                    </p>
                    <div class="assessment-stats">
                        <div class="stat-item">
                            <div class="stat-value">10</div>
                            <div class="stat-label">Tasks</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">30</div>
                            <div class="stat-label">Minutes</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">--</div>
                            <div class="stat-label">Best Score</div>
                        </div>
                    </div>
                    <button class="start-assessment-btn" onclick="startAssessment('practical')">
                        Start Practical
                    </button>
                </div>
            </div>

            <div class="assessment-card animate-slide-up animate-stagger-6">
                <div class="card-header">
                    <div class="card-icon">🏆</div>
                    <h3 class="card-title">Comprehensive Exam</h3>
                </div>
                <div class="card-body">
                    <div class="difficulty-indicator difficulty-advanced">
                        <span>●</span> Advanced Level
                    </div>
                    <p class="card-description">
                        Final comprehensive assessment covering all physics topics with mixed question types.
                    </p>
                    <div class="assessment-stats">
                        <div class="stat-item">
                            <div class="stat-value">50</div>
                            <div class="stat-label">Questions</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">90</div>
                            <div class="stat-label">Minutes</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">--</div>
                            <div class="stat-label">Best Score</div>
                        </div>
                    </div>
                    <button class="start-assessment-btn" onclick="startAssessment('comprehensive')" disabled>
                        Complete All Topics First
                    </button>
                </div>
            </div>
        </div>
    </div>

    <footer id="main-footer-container"></footer>

    <script type="module" src="js/main.js"></script>
    <script>
        function startAssessment(type) {
            // Store assessment type and redirect to quiz page
            localStorage.setItem('currentAssessment', type);
            window.location.href = `quiz.html?type=${type}`;
        }

        // Update progress based on stored data
        document.addEventListener('DOMContentLoaded', () => {
            updateProgressDisplay();
        });

        function updateProgressDisplay() {
            // This would typically fetch data from localStorage or a server
            const assessmentData = JSON.parse(localStorage.getItem('assessmentProgress') || '{}');
            
            // Update progress bars and percentages based on stored data
            // This is a simplified version - in a real app, you'd calculate based on actual scores
        }
    </script>
</body>
</html>
