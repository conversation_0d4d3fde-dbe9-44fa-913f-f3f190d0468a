<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Science Simulations: A Playground for the Mind</title>
    <style>
        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f7f6;
            color: #333;
            line-height: 1.6;
        }

        header {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 20px 0;
            margin-bottom: 30px;
        }

        .main-heading {
            text-align: center;
            margin: 0;
            font-size: 2.2em; /* Adjusted for prominence */
            font-weight: 600;
        }

        main {
            padding: 0 15px; /* Add some padding for smaller screens */
        }

        .grid-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); /* Min width 280px for better mobile fit */
            gap: 25px; /* Increased gap */
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .info-box {
            background-color: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 10px; /* Slightly more rounded */
            padding: 25px; /* Increased padding */
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08); /* Softer shadow */
            display: flex;
            flex-direction: column;
            align-items: stretch; /* Stretch items to fill width */
            text-align: center;
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }

        .info-box:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
        }

        .info-box h2 {
            color: #3498db;
            margin-top: 0;
            margin-bottom: 20px; /* Increased margin */
            font-size: 1.6em;
        }

        .info-box input[type="text"] {
            width: 100%; /* Full width within padding */
            padding: 12px; /* Increased padding */
            margin-bottom: 20px; /* Increased margin */
            border: 1px solid #ccc;
            border-radius: 5px; /* Slightly rounded */
            font-size: 1em;
        }

        .info-box button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 20px; /* Increased padding */
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 1.05em; /* Slightly larger font */
            font-weight: 500;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.1s ease;
            width: 100%; /* Full width button */
        }

        .info-box button:hover {
            background-color: #2980b9;
        }
        .info-box button:active {
            transform: scale(0.98);
        }

        footer {
            text-align: center;
            padding: 25px; /* Increased padding */
            margin-top: 40px;
            background-color: #2c3e50;
            color: #ecf0f1;
            font-size: 0.9em;
        }

        footer p {
            margin: 0;
        }

        footer a {
            color: #3498db;
            text-decoration: none;
            font-weight: 500;
        }

        footer a:hover {
            text-decoration: underline;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .main-heading {
                font-size: 1.8em;
            }
            .info-box h2 {
                font-size: 1.4em;
            }
            .grid-container {
                gap: 20px;
                padding: 15px;
            }
        }
        @media (max-width: 480px) {
            .main-heading {
                font-size: 1.6em;
                padding: 0 10px; /* Ensure heading doesn't touch edges */
            }
            .info-box {
                padding: 20px;
            }
            .info-box h2 {
                font-size: 1.3em;
                margin-bottom: 15px;
            }
            .info-box input[type="text"], .info-box button {
                font-size: 0.95em;
                padding: 10px;
            }
            .grid-container {
                padding: 10px; /* Reduce padding for very small screens */
            }
        }
    </style>
</head>
<body>
    <header>
        <h1 class="main-heading">Science Simulations: A Playground for the Mind</h1>
    </header>
    
    <main>
        <div class="grid-container">
            <!-- Box 1: Simulate! -->
            <div class="info-box">
                <h2>Simulate!</h2>
                <input type="text" id="conceptInput" placeholder="Enter any scientific concept here">
                <button id="simulateButton">Simulate</button>
            </div>

            <!-- Box 2: Interact! -->
            <div class="info-box">
                <h2>Interact!</h2>
                <input type="text" id="interactionInput" placeholder="How could I interact with it?">
                <button id="interactButton">Interact</button>
            </div>

            <!-- Box 3: Visualise! -->
            <div class="info-box">
                <h2>Visualise!</h2>
                <input type="text" id="visualiseInput" placeholder="What if I could see the unseeable?">
                <button id="visualiseButton">Visualise</button>
            </div>

            <!-- Box 4: Learn! -->
            <div class="info-box">
                <h2>Learn!</h2>
                <input type="text" id="learnInput" placeholder="What conclusions could I draw?">
                <button id="learnButton">Learn</button>
            </div>

            <!-- Box 5: Share! -->
            <div class="info-box">
                <h2>Share!</h2>
                <input type="text" id="shareInput" placeholder="How could I use this in class?">
                <button id="shareButton">Share</button>
            </div>

            <!-- Box 6: Study! -->
            <div class="info-box">
                <h2>Study!</h2>
                <input type="text" id="studyInput" placeholder="How can I teach myself?">
                <button id="studyButton">Study</button>
            </div>
        </div>
    </main>

    <footer>
        <p>&copy; <span id="currentYear"></span> Science Simulations App. All rights reserved. | <a href="#">Acknowledgements</a></p>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Box 1: Simulate!
            const simulateButton = document.getElementById('simulateButton');
            if (simulateButton) {
                simulateButton.addEventListener('click', function() {
                    alert('The concept was entered.');
                });
            }

            // Box 2: Interact!
            const interactButton = document.getElementById('interactButton');
            if (interactButton) {
                interactButton.addEventListener('click', function() {
                    alert('The interaction was entered.');
                });
            }

            // Box 3: Visualise!
            const visualiseButton = document.getElementById('visualiseButton');
            if (visualiseButton) {
                visualiseButton.addEventListener('click', function() {
                    alert('The unseeable was seen.');
                });
            }

            // Box 4: Learn!
            const learnButton = document.getElementById('learnButton');
            if (learnButton) {
                learnButton.addEventListener('click', function() {
                    alert('The conclusion was entered.');
                });
            }

            // Box 5: Share!
            const shareButton = document.getElementById('shareButton');
            if (shareButton) {
                shareButton.addEventListener('click', function() {
                    alert('The method of use was entered.');
                });
            }

            // Box 6: Study!
            const studyButton = document.getElementById('studyButton');
            if (studyButton) {
                studyButton.addEventListener('click', function() {
                    alert('The method of self-teaching was entered.');
                });
            }

            // Update copyright year
            const yearSpan = document.getElementById('currentYear');
            if (yearSpan) {
                yearSpan.textContent = new Date().getFullYear();
            }
        });
    </script>
</body>
</html>
