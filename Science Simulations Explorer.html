<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Science Simulations Explorer</title>
    <style>
        :root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --danger-color: #dc3545;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }

        body {
            font-family: var(--font-family);
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: var(--light-color);
            color: var(--dark-color);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        header {
            background-color: var(--primary-color);
            color: white;
            padding: 1rem;
            text-align: center;
        }

        header h1 {
            margin: 0;
            font-size: 1.8rem;
        }

        nav {
            background-color: var(--dark-color);
            padding: 0.5rem;
            text-align: center;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
        }

        nav button {
            background-color: var(--secondary-color);
            color: white;
            border: none;
            padding: 0.75rem 1rem;
            margin: 0.25rem;
            cursor: pointer;
            border-radius: 4px;
            font-size: 0.9rem;
            transition: background-color 0.3s ease;
        }

        nav button:hover, nav button.active-nav-button {
            background-color: var(--primary-color);
        }

        main {
            flex-grow: 1;
            padding: 1rem;
            max-width: 1000px;
            margin: 0 auto;
            width: 100%;
            box-sizing: border-box;
        }

        main section {
            display: none;
            padding: 1rem;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        main section.active {
            display: block;
        }

        h2 {
            color: var(--primary-color);
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 0.5rem;
            margin-top: 0;
        }
        h3 {
            color: var(--secondary-color);
        }

        button, input[type="button"], input[type="submit"], select {
            padding: 0.5rem 1rem;
            margin: 0.25rem;
            border-radius: 4px;
            border: 1px solid var(--secondary-color);
            background-color: var(--secondary-color);
            color: white;
            cursor: pointer;
            font-size: 1rem;
        }
        button:hover, input[type="button"]:hover, input[type="submit"]:hover {
            opacity: 0.9;
        }
        button.primary, input.primary { background-color: var(--primary-color); border-color: var(--primary-color); }
        button.success, input.success { background-color: var(--success-color); border-color: var(--success-color); }
        button.danger, input.danger { background-color: var(--danger-color); border-color: var(--danger-color); }
        button.warning, input.warning { background-color: var(--warning-color); border-color: var(--warning-color); color: var(--dark-color);}


        input[type="range"] {
            width: 100%;
            max-width: 300px;
        }
        label {
            display: block;
            margin: 0.5rem 0 0.2rem;
        }

        #sandbox-container {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        #simulationCanvas {
            border: 1px solid var(--secondary-color);
            width: 100%;
            max-width: 700px; /* Max width for canvas */
            height: auto;
            aspect-ratio: 3 / 2; /* Maintain aspect ratio */
            background-color: #f0f0f0;
            display: block;
            margin: 0 auto; /* Center canvas */
        }
        
        #sandbox-controls, #sandbox-actions, #simulation-parameters {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            align-items: center;
            padding: 0.5rem;
            background-color: #e9ecef;
            border-radius: 4px;
        }

        #simulation-parameters div {
            display: flex;
            flex-direction: column;
            margin-right: 1rem;
        }
        #simulation-parameters span {
            font-size: 0.9em;
            color: var(--secondary-color);
        }

        .resource-list li {
            margin-bottom: 0.5rem;
        }
        .resource-list a {
            color: var(--primary-color);
            text-decoration: none;
        }
        .resource-list a:hover {
            text-decoration: underline;
        }

        .saved-sim-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem;
            border: 1px solid #ddd;
            margin-bottom: 0.5rem;
            border-radius: 4px;
        }
        .saved-sim-item button {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }

        #shareLinkOutput {
            width: 100%;
            padding: 0.5rem;
            margin-top: 0.5rem;
            box-sizing: border-box;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
        }

        footer {
            text-align: center;
            padding: 1rem;
            background-color: var(--dark-color);
            color: var(--light-color);
            font-size: 0.9rem;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            header h1 { font-size: 1.5rem; }
            nav button { font-size: 0.8rem; padding: 0.5rem 0.75rem; }
            main { padding: 0.5rem; }
            main section { padding: 0.75rem; }
            #sandbox-controls, #sandbox-actions, #simulation-parameters { flex-direction: column; align-items: stretch; }
            #simulation-parameters div { margin-right: 0; margin-bottom: 0.5rem; }
            input[type="range"] { max-width: none; }
        }
    </style>
</head>
<body>
    <header>
        <h1>Science Simulations Explorer</h1>
    </header>
    <nav>
        <button data-target="intro" class="active-nav-button">Instructions</button>
        <button data-target="what-are-sims">What are Simulations?</button>
        <button data-target="sandbox">Sandbox</button>
        <button data-target="premade">Pre-made Examples</button>
        <button data-target="teaching">Simulations in Teaching</button>
        <button data-target="resources">External Resources</button>
        <button data-target="download-app">Download App</button>
    </nav>

    <main>
        <section id="intro" class="active">
            <h2>Welcome to the Science Simulations Explorer!</h2>
            <p>This interactive web app is designed to help you understand the concept of science simulations. Here's how to use it effectively:</p>
            <ul>
                <li><strong>Navigation:</strong> Use the buttons in the top navigation bar to switch between different sections of the app.</li>
                <li><strong>What are Simulations?:</strong> Learn about the definition and purpose of science simulations.</li>
                <li><strong>Sandbox:</strong> This is where the magic happens!
                    <ul>
                        <li>Add different types of particles (Red, Blue, Green) to the canvas by selecting a type and clicking on the canvas.</li>
                        <li>Use controls to start, pause, or reset the simulation.</li>
                        <li>Adjust parameters like interaction strength and range to see how they affect particle behavior.</li>
                        <li>Save your creations to your browser, load them later, or download them as a file.</li>
                        <li>Share your simulations with others using a special link!</li>
                    </ul>
                </li>
                <li><strong>Pre-made Examples:</strong> Explore some pre-configured simulations to see different interaction patterns. You can load these into the sandbox and modify them.</li>
                <li><strong>Simulations in Teaching:</strong> Discover tips and best practices for using simulations in an educational setting.</li>
                <li><strong>External Resources:</strong> Find links to other websites offering a wide variety of science simulations.</li>
                <li><strong>Download App:</strong> Download this entire web application as a single HTML file to use it offline for self-study.</li>
            </ul>
            <p>Experiment, explore, and have fun learning about the power of simulations!</p>
        </section>

        <section id="what-are-sims">
            <h2>What are Science Simulations?</h2>
            <p>A science simulation is a computer program that models a natural phenomenon or a scientific concept. It allows users to observe, interact with, and manipulate a virtual representation of a system that might be too complex, too fast, too slow, too small, too large, or too dangerous to study directly in the real world.</p>
            <h3>Purpose of Simulations:</h3>
            <ul>
                <li><strong>Understanding Complex Systems:</strong> Simulations can break down intricate processes into understandable parts, showing how different variables interact.</li>
                <li><strong>Experimentation:</strong> They provide a safe and cost-effective way to conduct experiments, change variables, and observe outcomes without real-world consequences or resource limitations. For example, simulating planetary motion, chemical reactions, or ecological changes.</li>
                <li><strong>Visualization:</strong> Abstract concepts (like atomic structures or electromagnetic fields) can be made visible and more intuitive.</li>
                <li><strong>Prediction:</strong> Simulations can be used to predict future behavior of systems based on current data and established scientific laws (e.g., weather forecasting, climate change models).</li>
                <li><strong>Training:</strong> They offer practice environments for complex tasks (e.g., flight simulators, surgical training).</li>
                <li><strong>Education:</strong> Interactive simulations make learning more engaging and can help students develop a deeper conceptual understanding by actively exploring cause-and-effect relationships.</li>
            </ul>
            <p>In essence, simulations are powerful tools for scientific inquiry, learning, and problem-solving, bridging the gap between theoretical knowledge and practical understanding.</p>
        </section>

        <section id="sandbox">
            <h2>Simulation Sandbox</h2>
            <div id="sandbox-container">
                <div id="sandbox-controls">
                    <span>Add Particle Type:</span>
                    <select id="particleTypeSelect">
                        <option value="A" selected>Red (Attracts Red, Repels Blue)</option>
                        <option value="B">Blue (Attracts Blue, Repels Red)</option>
                        <option value="C">Green (Neutral, Affected by A & B)</option>
                    </select>
                    <span style="font-size:0.8em; margin-left:10px;">(Click on canvas to add)</span>
                </div>

                <canvas id="simulationCanvas"></canvas>
                <p style="text-align:center; font-size:0.9em;">Particle Count: <span id="particleCount">0</span></p>

                <div id="simulation-parameters">
                    <div>
                        <label for="interactionStrength">Interaction Strength: <span id="interactionStrengthValue">0.5</span></label>
                        <input type="range" id="interactionStrength" min="0.05" max="2" step="0.05" value="0.5">
                    </div>
                    <div>
                        <label for="interactionRange">Interaction Range: <span id="interactionRangeValue">70</span>px</label>
                        <input type="range" id="interactionRange" min="10" max="200" step="5" value="70">
                    </div>
                    <div>
                        <label for="maxSpeed">Max Speed: <span id="maxSpeedValue">2</span></label>
                        <input type="range" id="maxSpeed" min="0.5" max="10" step="0.1" value="2">
                    </div>
                </div>

                <div id="sandbox-actions">
                    <button id="startPauseButton" class="primary">Start</button>
                    <button id="resetButton" class="warning">Reset Parameters</button>
                    <button id="clearButton" class="danger">Clear Canvas</button>
                    <button id="randomizeButton">Randomize Positions</button>
                </div>

                <h3>Manage Your Simulations:</h3>
                <div>
                    <input type="text" id="simulationName" placeholder="Simulation Name (for saving)">
                    <button id="saveSimulationButton" class="success">Save to Browser</button>
                </div>
                <div id="savedSimulationsList">
                    <h4>Saved in Browser:</h4>
                    <!-- Saved simulations will be listed here -->
                </div>
                
                <div>
                    <button id="downloadStateButton">Download Configuration</button>
                    <label for="uploadStateFile" class="button-like-label">
                        Upload Configuration File
                        <input type="file" id="uploadStateFile" accept=".simjson" style="display:none;">
                    </label>
                </div>
                <div>
                    <button id="generateShareLinkButton">Generate Share Link</button>
                    <input type="text" id="shareLinkOutput" readonly placeholder="Shareable link will appear here">
                </div>
            </div>
        </section>

        <section id="premade">
            <h2>Pre-made Simulation Examples</h2>
            <p>Click on an example to load it into the sandbox. You can then run it, modify parameters, or add more particles.</p>
            <div id="premadeSimulationsList">
                <!-- Pre-made simulations will be listed here -->
            </div>
        </section>

        <section id="teaching">
            <h2>Using Simulations in Teaching</h2>
            <p>Simulations can be incredibly effective teaching tools. Here are some important points to consider when incorporating them into your lessons:</p>
            
            <h3>1. License and Attributions</h3>
            <p>Always check the license of any simulation you plan to use. Many educational simulations are free for non-commercial use, but some may have restrictions or require attribution. If you modify or adapt a simulation, understand the terms of its license regarding derivative works. For this app, it's provided as an educational example; for external simulations, due diligence is key.</p>

            <h3>2. Set Clear Learning Outcomes</h3>
            <p>Before introducing a simulation, define what you want students to learn. Are they exploring a concept, testing a hypothesis, or understanding a complex process? Clear outcomes will guide student interaction and your assessment. For example: "Students will be able to describe how predator-prey populations affect each other by manipulating variables in the simulation."</p>

            <h3>3. Make a Lesson Plan</h3>
            <p>Integrate the simulation into a broader lesson plan. This plan should include:</p>
            <ul>
                <li><strong>Introduction:</strong> Background information, relevant vocabulary, and the learning objectives.</li>
                <li><strong>Simulation Activity:</strong> Specific tasks or questions for students to address while using the simulation. This could be guided inquiry or open exploration.</li>
                <li><strong>Discussion/Reflection:</strong> Post-simulation questions to help students consolidate their learning, connect it to theory, and discuss their findings.</li>
                <li><strong>Assessment:</strong> How will you gauge if students met the learning outcomes? (e.g., observation, worksheet, discussion, quiz).</li>
            </ul>

            <h3>4. Introduce the Simulation to Students</h3>
            <p>Don't just throw students into a complex simulation. Briefly explain:</p>
            <ul>
                <li>The purpose of the simulation and how it relates to the topic.</li>
                <li>The basic controls and interface elements.</li>
                <li>Any key variables they can manipulate and what they represent.</li>
                <li>The specific task or questions they need to focus on.</li>
            </ul>
            <p>A short demonstration can be very helpful.</p>

            <h3>5. Let Them Play and Explore</h3>
            <p>Allow students some unstructured time to "play" with the simulation. This helps them get comfortable with the interface and discover some behaviors on their own. Curiosity-driven exploration can lead to deeper engagement and unexpected insights. However, balance this with guided tasks to ensure they address the learning objectives.</p>

            <h3>6. Ask Questions from Various Angles</h3>
            <p>Use questioning techniques to deepen understanding and critical thinking:</p>
            <ul>
                <li><strong>Predictive Questions:</strong> "What do you think will happen if you increase/decrease [variable X]?"</li>
                <li><strong>Observational Questions:</strong> "What did you observe when you changed [variable Y]?"</li>
                <li><strong>Explanatory Questions:</strong> "Why do you think [observed phenomenon Z] happened?"</li>
                <li><strong>Application Questions:</strong> "How does this relate to [real-world example]?" or "Can you design an experiment using this simulation to test [hypothesis]?"</li>
                <li><strong>"What if" Scenarios:</strong> Encourage students to explore extreme values or unusual combinations of parameters.</li>
            </ul>
            <p>Encourage collaboration and discussion among students as they use the simulation.</p>
        </section>

        <section id="resources">
            <h2>External Resources for More Simulations</h2>
            <p>Here are some excellent websites where you can find a wide variety of science simulations for different subjects and grade levels:</p>
            <ul class="resource-list">
                <li><a href="https://phet.colorado.edu/" target="_blank" rel="noopener noreferrer">PhET Interactive Simulations (University of Colorado Boulder)</a> - Extensive collection of free, research-based math and science simulations.</li>
                <li><a href="https://concord.org/learn/projects/" target="_blank" rel="noopener noreferrer">Concord Consortium</a> - Interactive STEM activities, models, and simulations.</li>
                <li><a href="https://www.explorelearning.com/" target="_blank" rel="noopener noreferrer">ExploreLearning Gizmos</a> - Large library of interactive online simulations for math and science (subscription-based, but some free trials).</li>
                <li><a href="https://learn.genetics.utah.edu/" target="_blank" rel="noopener noreferrer">Learn.Genetics (University of Utah)</a> - Engaging resources and simulations focused on genetics, bioscience, and health.</li>
                <li><a href="https://www.olabs.edu.in/" target="_blank" rel="noopener noreferrer">OLabs (Online Labs for School Lab Experiments)</a> - Developed by CDAC India, provides simulations for science experiments.</li>
                <li><a href="https://www.physicsclassroom.com/Physics-Interactives" target="_blank" rel="noopener noreferrer">The Physics Classroom - Interactives</a> - A collection of interactive simulations and exercises for physics.</li>
                 <li><a href="https://www.falstad.com/mathphysics.html" target="_blank" rel="noopener noreferrer">Paul Falstad's simulations</a> - A wide range of Java and JavaScript based physics and math simulations.</li>
            </ul>
            <p><em>Note: Please review the terms of use and licensing for each external resource before using them, especially in educational settings.</em></p>
        </section>

        <section id="download-app">
            <h2>Download This App</h2>
            <p>You can download this entire web application as a single HTML file. This allows you to use it offline on your computer or mobile device for self-study or demonstration purposes.</p>
            <p>Click the button below to download the `ScienceSimulationsExplorer.html` file.</p>
            <button id="downloadAppButton" class="primary">Download App (HTML file)</button>
            <p><small><strong>Note:</strong> If you have saved any simulations in your browser's local storage, those saved states will not be included in the downloaded file. You can use the "Download Configuration" feature in the Sandbox to save specific simulation setups to a file.</small></p>
        </section>
    </main>

    <footer>
        <p>&copy; 2023 AI-Generated Science Simulations Explorer. For educational purposes.</p>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // --- Navigation ---
            const navButtons = document.querySelectorAll('nav button');
            const sections = document.querySelectorAll('main section');

            function showSection(targetId) {
                sections.forEach(section => {
                    section.classList.remove('active');
                    if (section.id === targetId) {
                        section.classList.add('active');
                    }
                });
                navButtons.forEach(button => {
                    button.classList.toggle('active-nav-button', button.dataset.target === targetId);
                });
                window.scrollTo(0,0); // Scroll to top of page
            }

            navButtons.forEach(button => {
                button.addEventListener('click', () => {
                    showSection(button.dataset.target);
                });
            });
            
            // --- Simulation Globals & Setup ---
            const canvas = document.getElementById('simulationCanvas');
            const ctx = canvas.getContext('2d');
            
            // Set internal resolution for canvas
            const canvasInternalWidth = 600;
            const canvasInternalHeight = 400;
            canvas.width = canvasInternalWidth;
            canvas.height = canvasInternalHeight;

            let particles = [];
            let simulationRunning = false;
            let animationFrameId;

            // Default parameters
            let simParams = {
                interactionStrength: 0.5,
                interactionRange: 70,
                maxSpeed: 2,
                damping: 0.98, // Slight energy loss
                particleRadius: 5,
            };
            
            const particleColors = {
                A: 'rgba(255, 0, 0, 0.8)', // Red
                B: 'rgba(0, 0, 255, 0.8)', // Blue
                C: 'rgba(0, 128, 0, 0.8)'  // Green
            };

            // --- UI Elements ---
            const particleTypeSelect = document.getElementById('particleTypeSelect');
            const particleCountDisplay = document.getElementById('particleCount');
            
            const interactionStrengthSlider = document.getElementById('interactionStrength');
            const interactionStrengthValue = document.getElementById('interactionStrengthValue');
            const interactionRangeSlider = document.getElementById('interactionRange');
            const interactionRangeValue = document.getElementById('interactionRangeValue');
            const maxSpeedSlider = document.getElementById('maxSpeed');
            const maxSpeedValue = document.getElementById('maxSpeedValue');

            const startPauseButton = document.getElementById('startPauseButton');
            const resetButton = document.getElementById('resetButton');
            const clearButton = document.getElementById('clearButton');
            const randomizeButton = document.getElementById('randomizeButton');

            const simulationNameInput = document.getElementById('simulationName');
            const saveSimulationButton = document.getElementById('saveSimulationButton');
            const savedSimulationsListDiv = document.getElementById('savedSimulationsList');
            const downloadStateButton = document.getElementById('downloadStateButton');
            const uploadStateFileInput = document.getElementById('uploadStateFile');
            const generateShareLinkButton = document.getElementById('generateShareLinkButton');
            const shareLinkOutput = document.getElementById('shareLinkOutput');
            const premadeSimulationsListDiv = document.getElementById('premadeSimulationsList');

            // --- Parameter UI Update ---
            function updateParameterUI() {
                interactionStrengthSlider.value = simParams.interactionStrength;
                interactionStrengthValue.textContent = simParams.interactionStrength.toFixed(2);
                interactionRangeSlider.value = simParams.interactionRange;
                interactionRangeValue.textContent = simParams.interactionRange;
                maxSpeedSlider.value = simParams.maxSpeed;
                maxSpeedValue.textContent = simParams.maxSpeed.toFixed(1);
            }
            updateParameterUI(); // Initial call

            interactionStrengthSlider.addEventListener('input', (e) => {
                simParams.interactionStrength = parseFloat(e.target.value);
                interactionStrengthValue.textContent = simParams.interactionStrength.toFixed(2);
            });
            interactionRangeSlider.addEventListener('input', (e) => {
                simParams.interactionRange = parseInt(e.target.value);
                interactionRangeValue.textContent = simParams.interactionRange;
            });
            maxSpeedSlider.addEventListener('input', (e) => {
                simParams.maxSpeed = parseFloat(e.target.value);
                maxSpeedValue.textContent = simParams.maxSpeed.toFixed(1);
            });

            // --- Simulation Logic ---
            function addParticle(x, y, type) {
                if (particles.length >= 300) { // Max particle limit
                    alert("Maximum particle limit (300) reached. Please clear some particles to add more.");
                    return;
                }
                particles.push({
                    x, y,
                    vx: (Math.random() - 0.5) * 0.5, // Small initial random velocity
                    vy: (Math.random() - 0.5) * 0.5,
                    type: type,
                    radius: simParams.particleRadius,
                    color: particleColors[type]
                });
                updateParticleCount();
            }

            canvas.addEventListener('click', (e) => {
                if (document.getElementById('sandbox').classList.contains('active')) {
                    const rect = canvas.getBoundingClientRect();
                    // Scale click coordinates to canvas internal resolution
                    const scaleX = canvas.width / rect.width;
                    const scaleY = canvas.height / rect.height;
                    const x = (e.clientX - rect.left) * scaleX;
                    const y = (e.clientY - rect.top) * scaleY;
                    const selectedType = particleTypeSelect.value;
                    addParticle(x, y, selectedType);
                    if (!simulationRunning) draw(); // Draw if paused to show new particle
                }
            });

            function updateParticles() {
                particles.forEach(p1 => {
                    let fx = 0;
                    let fy = 0;

                    particles.forEach(p2 => {
                        if (p1 === p2) return;

                        const dx = p2.x - p1.x;
                        const dy = p2.y - p1.y;
                        const distSq = dx * dx + dy * dy;
                        
                        if (distSq === 0 || distSq > simParams.interactionRange * simParams.interactionRange) return;
                        
                        const dist = Math.sqrt(distSq);
                        
                        let force = 0;
                        // Interaction rules:
                        // A (Red): Attracts A, Repels B
                        // B (Blue): Attracts B, Repels A
                        // C (Green): Neutral base, but affected by A (attract) and B (repel)
                        
                        if (p1.type === 'A') {
                            if (p2.type === 'A') force = simParams.interactionStrength; // A attracts A
                            else if (p2.type === 'B') force = -simParams.interactionStrength; // A repels B
                            else if (p2.type === 'C') force = simParams.interactionStrength * 0.5; // A attracts C (weaker)
                        } else if (p1.type === 'B') {
                            if (p2.type === 'B') force = simParams.interactionStrength; // B attracts B
                            else if (p2.type === 'A') force = -simParams.interactionStrength; // B repels A
                            else if (p2.type === 'C') force = -simParams.interactionStrength * 0.5; // B repels C (weaker)
                        } else if (p1.type === 'C') {
                            if (p2.type === 'A') force = simParams.interactionStrength * 0.5; // C attracted by A
                            else if (p2.type === 'B') force = -simParams.interactionStrength * 0.5; // C repelled by B
                        }
                        
                        // Force proportional to (1 - dist/range), stronger when closer
                        const normalizedForce = force * (1 - dist / simParams.interactionRange);

                        fx += normalizedForce * (dx / dist);
                        fy += normalizedForce * (dy / dist);
                    });

                    // Update velocity (mass is assumed to be 1)
                    p1.vx += fx;
                    p1.vy += fy;

                    // Apply damping
                    p1.vx *= simParams.damping;
                    p1.vy *= simParams.damping;

                    // Limit speed
                    const speed = Math.sqrt(p1.vx * p1.vx + p1.vy * p1.vy);
                    if (speed > simParams.maxSpeed) {
                        p1.vx = (p1.vx / speed) * simParams.maxSpeed;
                        p1.vy = (p1.vy / speed) * simParams.maxSpeed;
                    }

                    // Update position
                    p1.x += p1.vx;
                    p1.y += p1.vy;

                    // Boundary conditions (bounce)
                    if (p1.x - p1.radius < 0) { p1.x = p1.radius; p1.vx *= -1; }
                    if (p1.x + p1.radius > canvas.width) { p1.x = canvas.width - p1.radius; p1.vx *= -1; }
                    if (p1.y - p1.radius < 0) { p1.y = p1.radius; p1.vy *= -1; }
                    if (p1.y + p1.radius > canvas.height) { p1.y = canvas.height - p1.radius; p1.vy *= -1; }
                });
            }

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                particles.forEach(p => {
                    ctx.beginPath();
                    ctx.arc(p.x, p.y, p.radius, 0, Math.PI * 2);
                    ctx.fillStyle = p.color;
                    ctx.fill();
                    // For debugging: draw velocity vectors
                    // ctx.beginPath();
                    // ctx.moveTo(p.x, p.y);
                    // ctx.lineTo(p.x + p.vx * 10, p.y + p.vy * 10);
                    // ctx.strokeStyle = 'black';
                    // ctx.stroke();
                });
            }

            function simulationLoop() {
                if (!simulationRunning) return;
                updateParticles();
                draw();
                animationFrameId = requestAnimationFrame(simulationLoop);
            }

            function updateParticleCount() {
                particleCountDisplay.textContent = particles.length;
            }

            // --- Simulation Controls ---
            startPauseButton.addEventListener('click', () => {
                simulationRunning = !simulationRunning;
                if (simulationRunning) {
                    startPauseButton.textContent = 'Pause';
                    startPauseButton.classList.remove('primary');
                    startPauseButton.classList.add('warning');
                    simulationLoop();
                } else {
                    startPauseButton.textContent = 'Start';
                    startPauseButton.classList.remove('warning');
                    startPauseButton.classList.add('primary');
                    cancelAnimationFrame(animationFrameId);
                }
            });
            
            function resetDefaultParameters() {
                simParams = {
                    interactionStrength: 0.5,
                    interactionRange: 70,
                    maxSpeed: 2,
                    damping: 0.98,
                    particleRadius: 5,
                };
                updateParameterUI();
            }

            resetButton.addEventListener('click', () => {
                resetDefaultParameters();
                if (!simulationRunning) draw(); // Redraw if paused
            });

            clearButton.addEventListener('click', () => {
                particles = [];
                updateParticleCount();
                if (!simulationRunning) draw(); // Redraw if paused
            });

            randomizeButton.addEventListener('click', () => {
                particles.forEach(p => {
                    p.x = Math.random() * canvas.width;
                    p.y = Math.random() * canvas.height;
                    p.vx = (Math.random() - 0.5) * 0.5;
                    p.vy = (Math.random() - 0.5) * 0.5;
                });
                if (!simulationRunning) draw();
            });

            // --- Save/Load/Share ---
            function getSimulationState() {
                return {
                    particles: particles.map(p => ({ x: p.x, y: p.y, vx: p.vx, vy: p.vy, type: p.type })), // Don't save radius/color, derive from type
                    params: { ...simParams }
                };
            }

            function loadSimulationState(state) {
                try {
                    particles = state.particles.map(p_data => ({
                        ...p_data,
                        radius: state.params.particleRadius || simParams.particleRadius, // Use loaded or default radius
                        color: particleColors[p_data.type]
                    }));
                    simParams = { ...state.params };
                    
                    updateParameterUI();
                    updateParticleCount();
                    draw();
                    if (simulationRunning) { // If it was running, pause and let user restart
                        simulationRunning = false;
                        startPauseButton.textContent = 'Start';
                        startPauseButton.classList.remove('warning');
                        startPauseButton.classList.add('primary');
                        cancelAnimationFrame(animationFrameId);
                    }
                } catch (error) {
                    console.error("Error loading state:", error);
                    alert("Failed to load simulation state. The data might be corrupted.");
                }
            }

            // LocalStorage Save/Load
            saveSimulationButton.addEventListener('click', () => {
                const name = simulationNameInput.value.trim();
                if (!name) {
                    alert("Please enter a name for your simulation.");
                    return;
                }
                const state = getSimulationState();
                try {
                    let savedSims = JSON.parse(localStorage.getItem('scienceSims')) || [];
                    // Check if name exists, if so, update it. Otherwise, add new.
                    const existingIndex = savedSims.findIndex(s => s.name === name);
                    if (existingIndex > -1) {
                        savedSims[existingIndex].state = state;
                    } else {
                        savedSims.push({ name, state });
                    }
                    localStorage.setItem('scienceSims', JSON.stringify(savedSims));
                    alert(`Simulation "${name}" saved!`);
                    simulationNameInput.value = '';
                    renderSavedSimulations();
                } catch (e) {
                    alert("Error saving simulation. LocalStorage might be full or disabled.");
                    console.error("LocalStorage save error:", e);
                }
            });

            function renderSavedSimulations() {
                savedSimulationsListDiv.innerHTML = '<h4>Saved in Browser:</h4>';
                let savedSims = [];
                try {
                    savedSims = JSON.parse(localStorage.getItem('scienceSims')) || [];
                } catch (e) {
                    console.error("Error reading saved simulations from LocalStorage:", e);
                    localStorage.removeItem('scienceSims'); // Clear corrupted data
                }

                if (savedSims.length === 0) {
                    savedSimulationsListDiv.innerHTML += '<p>No simulations saved yet.</p>';
                    return;
                }
                const ul = document.createElement('ul');
                ul.style.listStyleType = 'none';
                ul.style.paddingLeft = '0';

                savedSims.forEach((simData, index) => {
                    const li = document.createElement('li');
                    li.classList.add('saved-sim-item');
                    li.textContent = simData.name;
                    
                    const buttonsDiv = document.createElement('div');
                    const loadBtn = document.createElement('button');
                    loadBtn.textContent = 'Load';
                    loadBtn.classList.add('success');
                    loadBtn.onclick = () => {
                        loadSimulationState(simData.state);
                        simulationNameInput.value = simData.name; // Populate name for potential re-save
                        showSection('sandbox');
                    };
                    buttonsDiv.appendChild(loadBtn);

                    const deleteBtn = document.createElement('button');
                    deleteBtn.textContent = 'Delete';
                    deleteBtn.classList.add('danger');
                    deleteBtn.style.marginLeft = '5px';
                    deleteBtn.onclick = () => {
                        if (confirm(`Are you sure you want to delete "${simData.name}"?`)) {
                            savedSims.splice(index, 1);
                            localStorage.setItem('scienceSims', JSON.stringify(savedSims));
                            renderSavedSimulations();
                        }
                    };
                    buttonsDiv.appendChild(deleteBtn);
                    li.appendChild(buttonsDiv);
                    ul.appendChild(li);
                });
                savedSimulationsListDiv.appendChild(ul);
            }

            // File Download/Upload
            downloadStateButton.addEventListener('click', () => {
                const state = getSimulationState();
                const jsonState = JSON.stringify(state, null, 2);
                const blob = new Blob([jsonState], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                const name = simulationNameInput.value.trim() || 'simulation_state';
                a.href = url;
                a.download = `${name}.simjson`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            });

            uploadStateFileInput.addEventListener('change', (event) => {
                const file = event.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        try {
                            const state = JSON.parse(e.target.result);
                            loadSimulationState(state);
                            const fileName = file.name.replace('.simjson', '');
                            simulationNameInput.value = fileName;
                            alert(`Simulation "${fileName}" loaded from file.`);
                        } catch (err) {
                            alert("Error reading or parsing simulation file.");
                            console.error("File load error:", err);
                        }
                    };
                    reader.readAsText(file);
                    uploadStateFileInput.value = null; // Reset file input
                }
            });

            // Share Link
            generateShareLinkButton.addEventListener('click', () => {
                const state = getSimulationState();
                try {
                    const jsonState = JSON.stringify(state);
                    const base64State = btoa(jsonState); // Encode to Base64
                    const shareUrl = `${window.location.href.split('#')[0]}#sim=${encodeURIComponent(base64State)}`;
                    shareLinkOutput.value = shareUrl;
                    shareLinkOutput.select();
                    // For older browsers or specific security settings, execCommand might not work.
                    // Modern approach is navigator.clipboard.writeText(shareUrl) but that requires HTTPS.
                    // For simplicity in a single file HTTP context, just selecting is fine.
                    // User can then manually copy.
                    alert("Share link generated and selected. Press Ctrl+C (or Cmd+C) to copy.");
                } catch(e) {
                    alert("Error generating share link. Simulation might be too large.");
                    console.error("Share link error:", e);
                }
            });

            function loadFromShareLink() {
                if (window.location.hash && window.location.hash.startsWith('#sim=')) {
                    try {
                        const base64State = decodeURIComponent(window.location.hash.substring(5)); // Remove #sim=
                        const jsonState = atob(base64State); // Decode from Base64
                        const state = JSON.parse(jsonState);
                        loadSimulationState(state);
                        showSection('sandbox'); // Switch to sandbox view
                        alert('Shared simulation loaded!');
                         // Clear the hash to prevent reloading if user refreshes, and to make new share links cleaner
                        history.pushState("", document.title, window.location.pathname + window.location.search);
                    } catch (e) {
                        console.error("Error loading shared simulation:", e);
                        alert('Could not load shared simulation. The link might be corrupted.');
                    }
                }
            }

            // --- Pre-made Simulations ---
            const premadeSims = [
                {
                    name: "Red Cluster",
                    description: "Multiple red particles that attract each other, forming a cluster.",
                    config: () => {
                        resetDefaultParameters();
                        simParams.interactionStrength = 0.8;
                        simParams.interactionRange = 100;
                        particles = [];
                        for (let i = 0; i < 30; i++) {
                            addParticle(Math.random() * canvas.width, Math.random() * canvas.height, 'A');
                        }
                    }
                },
                {
                    name: "Blue Dispersion",
                    description: "Blue particles repelling each other, leading to dispersion.",
                    config: () => {
                        resetDefaultParameters();
                        simParams.interactionStrength = 0.6; // Repulsion strength for B-B is based on this
                        simParams.interactionRange = 80;
                        particles = [];
                        for (let i = 0; i < 30; i++) {
                            addParticle(Math.random() * canvas.width, Math.random() * canvas.height, 'B');
                        }
                    }
                },
                {
                    name: "Red & Blue Mix",
                    description: "Red and Blue particles interacting. Reds attract Reds, Blues attract Blues, Reds and Blues repel.",
                    config: () => {
                        resetDefaultParameters();
                        simParams.interactionStrength = 0.7;
                        simParams.interactionRange = 90;
                        particles = [];
                        for (let i = 0; i < 20; i++) {
                            addParticle(Math.random() * canvas.width/2, Math.random() * canvas.height, 'A');
                            addParticle(canvas.width/2 + Math.random() * canvas.width/2, Math.random() * canvas.height, 'B');
                        }
                    }
                },
                {
                    name: "Green Tracers",
                    description: "Green particles interacting with a few Red and Blue particles.",
                    config: () => {
                        resetDefaultParameters();
                        simParams.interactionStrength = 1.0;
                        simParams.interactionRange = 120;
                        particles = [];
                        // Add a few A and B particles
                        addParticle(canvas.width * 0.25, canvas.height * 0.5, 'A');
                        addParticle(canvas.width * 0.75, canvas.height * 0.5, 'B');
                        // Add many C particles
                        for (let i = 0; i < 40; i++) {
                            addParticle(Math.random() * canvas.width, Math.random() * canvas.height, 'C');
                        }
                    }
                }
            ];

            function renderPremadeSimulations() {
                premadeSims.forEach(sim => {
                    const div = document.createElement('div');
                    div.style.marginBottom = '1rem';
                    div.style.paddingBottom = '0.5rem';
                    div.style.borderBottom = '1px solid #eee';

                    const h3 = document.createElement('h3');
                    h3.textContent = sim.name;
                    div.appendChild(h3);

                    const p = document.createElement('p');
                    p.textContent = sim.description;
                    p.style.fontSize = '0.9em';
                    div.appendChild(p);

                    const button = document.createElement('button');
                    button.textContent = `Load "${sim.name}"`;
                    button.classList.add('primary');
                    button.onclick = () => {
                        sim.config(); // Sets up particles and params
                        updateParameterUI();
                        updateParticleCount();
                        draw();
                        if (simulationRunning) { // Pause if running
                            simulationRunning = false;
                            startPauseButton.textContent = 'Start';
                            startPauseButton.classList.remove('warning');
                            startPauseButton.classList.add('primary');
                            cancelAnimationFrame(animationFrameId);
                        }
                        showSection('sandbox');
                        alert(`"${sim.name}" loaded into Sandbox.`);
                    };
                    div.appendChild(button);
                    premadeSimulationsListDiv.appendChild(div);
                });
            }
            
            // --- Download App ---
            const downloadAppButton = document.getElementById('downloadAppButton');
            downloadAppButton.addEventListener('click', () => {
                try {
                    const fullHtml = document.documentElement.outerHTML;
                    const blob = new Blob([fullHtml], { type: 'text/html' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'ScienceSimulationsExplorer.html';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                } catch (e) {
                    alert("Error creating download link for the app.");
                    console.error("App download error:", e);
                }
            });


            // --- Initializations ---
            showSection('intro'); // Show the intro section by default
            renderSavedSimulations(); // Load any simulations saved in localStorage
            renderPremadeSimulations(); // Populate the pre-made simulations list
            loadFromShareLink(); // Check if there's a simulation in the URL hash
            draw(); // Initial draw of empty canvas
            updateParticleCount(); // Initial particle count

        }); // End DOMContentLoaded
    </script>
</body>
</html>
