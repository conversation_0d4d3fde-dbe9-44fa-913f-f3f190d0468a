<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Heat Transfer Laboratory - Physics Virtual Lab</title>
    <meta name="author" content="Dr<PERSON> <PERSON>, SUST-BME">
    <meta name="copyright" content="Dr. <PERSON>il - +249912867327, +966538076790">
    <meta name="institution" content="Sudan University of Science and Technology - Biomedical Engineering">
    <meta name="description" content="Interactive heat transfer simulation for studying conduction, convection, and radiation">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/animations.css">
    <link rel="stylesheet" href="../css/components.css">
    <style>
        body {
            font-family: var(--font-family-sans);
            margin: 0;
            padding: 0;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .experiment-header {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
            padding: var(--spacing-lg);
            text-align: center;
        }

        .experiment-title {
            font-size: 2rem;
            font-weight: 700;
            margin: 0 0 var(--spacing-sm) 0;
        }

        .experiment-subtitle {
            font-size: 1.125rem;
            opacity: 0.9;
            margin: 0;
        }

        .header-actions {
            display: flex;
            justify-content: center;
            gap: var(--spacing-md);
            margin-top: var(--spacing-lg);
        }

        .nav-button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-md);
            text-decoration: none;
            font-weight: 500;
            transition: all var(--transition-fast);
        }

        .nav-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .main-content {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 350px;
            gap: var(--spacing-lg);
            padding: var(--spacing-lg);
            max-width: 1400px;
            margin: 0 auto;
            width: 100%;
        }

        .simulation-panel {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
        }

        .controls-panel {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
        }

        .panel-section {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            padding: var(--spacing-lg);
        }

        [data-theme="dark"] .panel-section {
            background: var(--gray-700);
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .heat-canvas {
            width: 100%;
            height: 400px;
            border: 2px solid var(--border-color);
            border-radius: var(--radius-lg);
            background: var(--bg-primary);
        }

        .control-group {
            margin-bottom: var(--spacing-md);
        }

        .control-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 500;
            color: var(--text-primary);
        }

        .control-slider {
            width: 100%;
            height: 8px;
            border-radius: 4px;
            background: var(--gray-200);
            outline: none;
            cursor: pointer;
            margin-bottom: var(--spacing-sm);
            accent-color: var(--primary-600);
        }

        [data-theme="dark"] .control-slider {
            background: var(--gray-600);
        }

        .control-value {
            font-weight: 600;
            color: var(--primary-600);
            text-align: center;
            padding: var(--spacing-sm);
            background: var(--bg-secondary);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
        }

        .transfer-mode-selector {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
        }

        .mode-btn {
            padding: var(--spacing-sm);
            border: 2px solid var(--border-color);
            border-radius: var(--radius-md);
            background: var(--bg-secondary);
            color: var(--text-primary);
            cursor: pointer;
            transition: all var(--transition-fast);
            text-align: center;
            font-weight: 500;
        }

        .mode-btn.active {
            border-color: var(--primary-600);
            background: var(--primary-100);
            color: var(--primary-700);
        }

        [data-theme="dark"] .mode-btn.active {
            background: var(--primary-900);
            color: var(--primary-300);
        }

        .mode-btn:hover {
            border-color: var(--primary-500);
        }

        .control-buttons {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--spacing-md);
        }

        .btn {
            flex: 1;
            padding: var(--spacing-sm) var(--spacing-md);
            border: none;
            border-radius: var(--radius-md);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .btn-primary {
            background: var(--primary-600);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-700);
        }

        .btn-secondary {
            background: var(--gray-200);
            color: var(--text-primary);
        }

        [data-theme="dark"] .btn-secondary {
            background: var(--gray-600);
        }

        .btn-secondary:hover {
            background: var(--gray-300);
        }

        [data-theme="dark"] .btn-secondary:hover {
            background: var(--gray-500);
        }

        .temperature-display {
            background: var(--accent-50);
            border: 1px solid var(--accent-200);
            border-radius: var(--radius-lg);
            padding: var(--spacing-md);
        }

        [data-theme="dark"] .temperature-display {
            background: var(--accent-900);
            border-color: var(--accent-700);
        }

        .temp-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: var(--spacing-sm);
        }

        .temp-label {
            font-weight: 500;
            color: var(--text-primary);
        }

        .temp-value {
            font-weight: 600;
            color: var(--accent-600);
        }

        [data-theme="dark"] .temp-value {
            color: var(--accent-400);
        }

        .heat-equation {
            background: var(--primary-50);
            border: 1px solid var(--primary-200);
            border-radius: var(--radius-lg);
            padding: var(--spacing-md);
            margin-top: var(--spacing-md);
        }

        [data-theme="dark"] .heat-equation {
            background: var(--primary-900);
            border-color: var(--primary-700);
        }

        .equation {
            font-size: 1.125rem;
            font-weight: 700;
            color: var(--primary-700);
            text-align: center;
            margin-bottom: var(--spacing-sm);
        }

        [data-theme="dark"] .equation {
            color: var(--primary-300);
        }

        .equation-description {
            font-size: 0.875rem;
            color: var(--text-secondary);
            text-align: center;
        }

        .legend {
            display: flex;
            justify-content: space-around;
            margin-top: var(--spacing-md);
            padding: var(--spacing-sm);
            background: var(--bg-secondary);
            border-radius: var(--radius-md);
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-size: 0.875rem;
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 2px;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                padding: var(--spacing-md);
            }
            
            .header-actions {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <header id="main-header"></header>
    
    <div class="experiment-header animate-fade-in">
        <h1 class="experiment-title">🌡️ Heat Transfer Laboratory</h1>
        <p class="experiment-subtitle">Explore conduction, convection, and radiation heat transfer</p>
        <div class="header-actions">
            <a href="../experiments-hub.html" class="nav-button">← Experiments Hub</a>
            <a href="../video-library.html?topic=thermodynamics" class="nav-button">📹 Watch Video</a>
            <a href="../assessment.html?topic=thermodynamics" class="nav-button">🎓 Take Quiz</a>
            <a href="../dashboard.html" class="nav-button">📊 Dashboard</a>
        </div>
    </div>

    <div class="main-content">
        <div class="simulation-panel">
            <div class="panel-section animate-slide-up">
                <h2 class="section-title">🔥 Heat Transfer Simulation</h2>
                <canvas id="heatCanvas" class="heat-canvas"></canvas>
                
                <div class="legend">
                    <div class="legend-item">
                        <div class="legend-color" style="background: #ef4444;"></div>
                        <span>Hot (>80°C)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #f59e0b;"></div>
                        <span>Warm (40-80°C)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #10b981;"></div>
                        <span>Cool (20-40°C)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #3b82f6;"></div>
                        <span>Cold (<20°C)</span>
                    </div>
                </div>
                
                <div class="control-buttons">
                    <button type="button" id="startBtn" class="btn btn-primary">Start Simulation</button>
                    <button type="button" id="pauseBtn" class="btn btn-secondary">Pause</button>
                    <button type="button" id="resetBtn" class="btn btn-secondary">Reset</button>
                </div>
            </div>
        </div>

        <div class="controls-panel">
            <div class="panel-section animate-slide-left">
                <h2 class="section-title">⚙️ Heat Transfer Controls</h2>
                
                <div class="transfer-mode-selector">
                    <button type="button" class="mode-btn active" data-mode="conduction">🧱 Conduction</button>
                    <button type="button" class="mode-btn" data-mode="convection">🌪️ Convection</button>
                    <button type="button" class="mode-btn" data-mode="radiation">☀️ Radiation</button>
                </div>

                <div class="control-group">
                    <label for="initialTempSlider" class="control-label">Initial Temperature:</label>
                    <input type="range" id="initialTempSlider" class="control-slider" min="0" max="100" value="80">
                    <div class="control-value">
                        T₀ = <span id="initialTempValue">80</span>°C
                    </div>
                </div>

                <div class="control-group">
                    <label for="ambientTempSlider" class="control-label">Ambient Temperature:</label>
                    <input type="range" id="ambientTempSlider" class="control-slider" min="0" max="50" value="20">
                    <div class="control-value">
                        T∞ = <span id="ambientTempValue">20</span>°C
                    </div>
                </div>

                <div class="control-group">
                    <label for="thermalConductivitySlider" class="control-label">Thermal Conductivity:</label>
                    <input type="range" id="thermalConductivitySlider" class="control-slider" min="1" max="10" value="5">
                    <div class="control-value">
                        k = <span id="thermalConductivityValue">5</span> W/m·K
                    </div>
                </div>
            </div>

            <div class="panel-section animate-slide-left animate-stagger-1">
                <h2 class="section-title">📊 Temperature Readings</h2>
                <div class="temperature-display">
                    <div class="temp-item">
                        <span class="temp-label">Center Temperature:</span>
                        <span class="temp-value" id="centerTemp">80°C</span>
                    </div>
                    <div class="temp-item">
                        <span class="temp-label">Surface Temperature:</span>
                        <span class="temp-value" id="surfaceTemp">75°C</span>
                    </div>
                    <div class="temp-item">
                        <span class="temp-label">Heat Flow Rate:</span>
                        <span class="temp-value" id="heatFlowRate">0 W</span>
                    </div>
                    <div class="temp-item">
                        <span class="temp-label">Time Elapsed:</span>
                        <span class="temp-value" id="timeElapsed">0 s</span>
                    </div>
                </div>

                <div class="heat-equation">
                    <div class="equation" id="currentEquation">q = -kA(dT/dx)</div>
                    <div class="equation-description" id="equationDescription">
                        Fourier's Law of Heat Conduction
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer id="main-footer-container"></footer>

    <script type="module" src="../js/main.js"></script>
    <script>
        // Heat Transfer Laboratory JavaScript
        document.addEventListener('DOMContentLoaded', () => {
            // DOM Elements
            const canvas = document.getElementById('heatCanvas');
            const ctx = canvas.getContext('2d');
            const startBtn = document.getElementById('startBtn');
            const pauseBtn = document.getElementById('pauseBtn');
            const resetBtn = document.getElementById('resetBtn');
            
            // Sliders
            const initialTempSlider = document.getElementById('initialTempSlider');
            const ambientTempSlider = document.getElementById('ambientTempSlider');
            const thermalConductivitySlider = document.getElementById('thermalConductivitySlider');
            
            // Value displays
            const initialTempValue = document.getElementById('initialTempValue');
            const ambientTempValue = document.getElementById('ambientTempValue');
            const thermalConductivityValue = document.getElementById('thermalConductivityValue');
            const centerTemp = document.getElementById('centerTemp');
            const surfaceTemp = document.getElementById('surfaceTemp');
            const heatFlowRate = document.getElementById('heatFlowRate');
            const timeElapsed = document.getElementById('timeElapsed');
            const currentEquation = document.getElementById('currentEquation');
            const equationDescription = document.getElementById('equationDescription');

            // Mode buttons
            const modeButtons = document.querySelectorAll('.mode-btn');

            // Simulation state
            let isRunning = false;
            let animationId = null;
            let time = 0;
            let currentMode = 'conduction';
            let temperatureGrid = [];
            const gridSize = 50;

            // Heat transfer equations and descriptions
            const equations = {
                conduction: {
                    equation: 'q = -kA(dT/dx)',
                    description: "Fourier's Law of Heat Conduction"
                },
                convection: {
                    equation: 'q = hA(Ts - T∞)',
                    description: "Newton's Law of Cooling"
                },
                radiation: {
                    equation: 'q = εσA(T₁⁴ - T₂⁴)',
                    description: "Stefan-Boltzmann Law"
                }
            };

            // Initialize canvas
            function initCanvas() {
                canvas.width = canvas.offsetWidth;
                canvas.height = canvas.offsetHeight;
                initializeTemperatureGrid();
            }

            // Initialize temperature grid
            function initializeTemperatureGrid() {
                temperatureGrid = [];
                const rows = Math.floor(canvas.height / gridSize);
                const cols = Math.floor(canvas.width / gridSize);
                
                for (let i = 0; i < rows; i++) {
                    temperatureGrid[i] = [];
                    for (let j = 0; j < cols; j++) {
                        // Initialize with hot center and cool edges
                        const centerX = cols / 2;
                        const centerY = rows / 2;
                        const distance = Math.sqrt((j - centerX) ** 2 + (i - centerY) ** 2);
                        const maxDistance = Math.sqrt(centerX ** 2 + centerY ** 2);
                        const normalizedDistance = distance / maxDistance;
                        
                        const initialTemp = parseInt(initialTempSlider.value);
                        const ambientTemp = parseInt(ambientTempSlider.value);
                        temperatureGrid[i][j] = initialTemp - (initialTemp - ambientTemp) * normalizedDistance;
                    }
                }
            }

            // Get temperature color
            function getTemperatureColor(temp) {
                if (temp > 80) return '#ef4444'; // Red
                if (temp > 60) return '#f59e0b'; // Orange
                if (temp > 40) return '#eab308'; // Yellow
                if (temp > 20) return '#10b981'; // Green
                return '#3b82f6'; // Blue
            }

            // Update temperature grid based on heat transfer mode
            function updateTemperatureGrid() {
                const k = parseInt(thermalConductivitySlider.value) / 10;
                const ambientTemp = parseInt(ambientTempSlider.value);
                const dt = 0.1;
                
                const newGrid = temperatureGrid.map(row => [...row]);
                
                for (let i = 1; i < temperatureGrid.length - 1; i++) {
                    for (let j = 1; j < temperatureGrid[i].length - 1; j++) {
                        const currentTemp = temperatureGrid[i][j];
                        
                        switch (currentMode) {
                            case 'conduction':
                                // 2D heat equation
                                const d2Tdx2 = temperatureGrid[i][j-1] - 2*currentTemp + temperatureGrid[i][j+1];
                                const d2Tdy2 = temperatureGrid[i-1][j] - 2*currentTemp + temperatureGrid[i+1][j];
                                newGrid[i][j] = currentTemp + k * dt * (d2Tdx2 + d2Tdy2);
                                break;
                                
                            case 'convection':
                                // Convective cooling
                                const h = k * 2; // Convection coefficient
                                newGrid[i][j] = currentTemp - h * dt * (currentTemp - ambientTemp) / 100;
                                break;
                                
                            case 'radiation':
                                // Radiative cooling (simplified)
                                const sigma = 5.67e-8; // Stefan-Boltzmann constant (scaled)
                                const epsilon = 0.8; // Emissivity
                                const T1 = currentTemp + 273.15; // Convert to Kelvin
                                const T2 = ambientTemp + 273.15;
                                const radiativeHeat = epsilon * sigma * (T1**4 - T2**4) / 1e12; // Scaled
                                newGrid[i][j] = currentTemp - radiativeHeat * dt;
                                break;
                        }
                        
                        // Ensure temperature doesn't go below ambient
                        newGrid[i][j] = Math.max(newGrid[i][j], ambientTemp);
                    }
                }
                
                temperatureGrid = newGrid;
            }

            // Draw heat map
            function drawHeatMap() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                const rows = temperatureGrid.length;
                const cols = temperatureGrid[0].length;
                const cellWidth = canvas.width / cols;
                const cellHeight = canvas.height / rows;
                
                for (let i = 0; i < rows; i++) {
                    for (let j = 0; j < cols; j++) {
                        const temp = temperatureGrid[i][j];
                        ctx.fillStyle = getTemperatureColor(temp);
                        ctx.fillRect(j * cellWidth, i * cellHeight, cellWidth, cellHeight);
                    }
                }
                
                // Update temperature readings
                const centerRow = Math.floor(rows / 2);
                const centerCol = Math.floor(cols / 2);
                const centerTemperature = temperatureGrid[centerRow][centerCol];
                const surfaceTemperature = temperatureGrid[0][centerCol];
                
                centerTemp.textContent = centerTemperature.toFixed(1) + '°C';
                surfaceTemp.textContent = surfaceTemperature.toFixed(1) + '°C';
                
                // Calculate heat flow rate (simplified)
                const tempDiff = centerTemperature - parseInt(ambientTempSlider.value);
                const k = parseInt(thermalConductivitySlider.value);
                const heatFlow = k * tempDiff / 10; // Simplified calculation
                heatFlowRate.textContent = heatFlow.toFixed(1) + ' W';
            }

            // Animation loop
            function animate() {
                if (!isRunning) return;
                
                time += 0.1;
                timeElapsed.textContent = time.toFixed(1) + ' s';
                
                updateTemperatureGrid();
                drawHeatMap();
                
                animationId = requestAnimationFrame(animate);
            }

            // Update displays
            function updateDisplays() {
                initialTempValue.textContent = initialTempSlider.value;
                ambientTempValue.textContent = ambientTempSlider.value;
                thermalConductivityValue.textContent = thermalConductivitySlider.value;
            }

            // Update equation display
            function updateEquation() {
                const eq = equations[currentMode];
                currentEquation.textContent = eq.equation;
                equationDescription.textContent = eq.description;
            }

            // Event listeners
            startBtn.addEventListener('click', () => {
                if (!isRunning) {
                    isRunning = true;
                    startBtn.textContent = 'Running...';
                    startBtn.disabled = true;
                    animate();
                }
            });

            pauseBtn.addEventListener('click', () => {
                isRunning = false;
                cancelAnimationFrame(animationId);
                startBtn.textContent = 'Resume';
                startBtn.disabled = false;
            });

            resetBtn.addEventListener('click', () => {
                isRunning = false;
                cancelAnimationFrame(animationId);
                time = 0;
                timeElapsed.textContent = '0 s';
                startBtn.textContent = 'Start Simulation';
                startBtn.disabled = false;
                initializeTemperatureGrid();
                drawHeatMap();
            });

            // Slider event listeners
            [initialTempSlider, ambientTempSlider, thermalConductivitySlider].forEach(slider => {
                slider.addEventListener('input', () => {
                    updateDisplays();
                    if (!isRunning) {
                        initializeTemperatureGrid();
                        drawHeatMap();
                    }
                });
            });

            // Mode selection
            modeButtons.forEach(btn => {
                btn.addEventListener('click', () => {
                    modeButtons.forEach(b => b.classList.remove('active'));
                    btn.classList.add('active');
                    currentMode = btn.dataset.mode;
                    updateEquation();
                    
                    // Reset simulation for new mode
                    isRunning = false;
                    cancelAnimationFrame(animationId);
                    time = 0;
                    timeElapsed.textContent = '0 s';
                    startBtn.textContent = 'Start Simulation';
                    startBtn.disabled = false;
                    initializeTemperatureGrid();
                    drawHeatMap();
                });
            });

            // Initialize
            initCanvas();
            updateDisplays();
            updateEquation();
            drawHeatMap();

            // Handle window resize
            window.addEventListener('resize', () => {
                initCanvas();
                drawHeatMap();
            });
        });
    </script>
</body>
</html>
