<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>'s Law Simulation</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: flex-start; /* Align to top for scroll on small screens */
            min-height: 100vh;
            box-sizing: border-box;
            line-height: 1.6;
        }
        #app-container {
            width: 100%;
            max-width: 700px; /* Max width of the content area */
            padding: 20px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px; /* Space between elements */
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-top: 0;
            margin-bottom: 10px;
        }
        #simulation-area {
            width: 100%; /* Take full width of app-container */
            height: 300px; /* Fixed height for the simulation box */
            border: 2px solid #3498db;
            background-color: #ffffff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            position: relative; /* For canvas positioning if needed */
        }
        #gas-canvas {
            display: block; /* Remove extra space below canvas */
            width: 100%;
            height: 100%;
        }
        #controls-area {
            width: 100%;
            padding: 20px;
            background-color: #ecf0f1;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 18px;
            box-sizing: border-box;
        }
        .control-group {
            display: flex;
            flex-direction: column; 
            align-items: center;
            width: 100%;
            max-width: 400px; /* Limit width of slider controls */
        }
        .control-group label {
            font-weight: bold;
            margin-bottom: 8px;
            color: #2980b9;
            display: flex; /* To align label text and value display nicely */
            justify-content: center;
            align-items: center;
            gap: 8px;
        }
        .control-group input[type="range"] {
            width: 100%; /* Slider takes full width of its group */
            cursor: pointer;
            accent-color: #3498db; /* Modern way to color sliders */
        }
        .value-display {
            font-size: 1.1em;
            color: #333;
            background-color: #fff;
            padding: 5px 12px;
            border-radius: 4px;
            border: 1px solid #bdc3c7;
            min-width: 50px; 
            text-align: center;
            font-weight: normal; /* Keep value display normal weight */
        }
        #reset-button {
            padding: 10px 20px;
            font-size: 1em;
            color: white;
            background-color: #e74c3c;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        #reset-button:hover, #reset-button:focus {
            background-color: #c0392b;
            outline: none;
        }
        .info-text {
            font-size: 0.95em;
            color: #555;
            text-align: center;
            max-width: 600px;
            margin-bottom: 0;
        }

        /* Responsive adjustments */
        @media (max-width: 600px) {
            h1 {
                font-size: 1.6em;
            }
            #simulation-area {
                height: 250px; /* Slightly smaller height on mobile */
            }
            .control-group {
                max-width: 100%; /* Allow controls to use more width */
            }
            #controls-area {
                padding: 15px;
                gap: 15px;
            }
            .info-text {
                font-size: 0.9em;
            }
        }
    </style>
</head>
<body>
    <div id="app-container">
        <h1>Boyle's Law Simulation</h1>
        <p class="info-text">Change the volume of the gas container using the slider and observe the change in pressure. Temperature and number of gas molecules are kept constant (P₁V₁ = P₂V₂).</p>

        <div id="simulation-area">
            <canvas id="gas-canvas"></canvas>
        </div>

        <div id="controls-area">
            <div class="control-group">
                <label for="volume-slider">Volume: <span id="volume-value" class="value-display">50</span> units</label>
                <input type="range" id="volume-slider" min="10" max="100" value="50" aria-label="Volume control">
            </div>
            
            <div class="control-group">
                <label>Pressure: <span id="pressure-value" class="value-display">2.00</span> units</label>
            </div>

            <button id="reset-button">Reset Simulation</button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // DOM Elements
            const canvas = document.getElementById('gas-canvas');
            const ctx = canvas.getContext('2d');
            const volumeSlider = document.getElementById('volume-slider');
            const volumeValueDisplay = document.getElementById('volume-value');
            const pressureValueDisplay = document.getElementById('pressure-value');
            const resetButton = document.getElementById('reset-button');
            const simulationArea = document.getElementById('simulation-area');

            // Simulation Constants
            const NUM_MOLECULES = 60;
            const MOLECULE_RADIUS = 4; 
            const MAX_SPEED = 1.2; // Pixels per frame, adjusted for smoother visuals

            const INITIAL_SLIDER_VOLUME = 50;
            const MIN_SLIDER_VOLUME = parseFloat(volumeSlider.min);
            const MAX_SLIDER_VOLUME = parseFloat(volumeSlider.max);
            
            // Boyle's Law: P * V = k. We define k based on an initial P at initial V.
            // Let initial pressure be 2.00 units when volume is INITIAL_SLIDER_VOLUME (50 units).
            const K_BOYLE = 2.00 * INITIAL_SLIDER_VOLUME; // k = 100

            let molecules = [];
            let currentVolume = INITIAL_SLIDER_VOLUME; 
            let animationFrameId;

            function getRandomColor() {
                // Generate aesthetically pleasing, somewhat desaturated colors
                const hue = Math.random() * 360;
                return `hsl(${hue}, 60%, 65%)`;
            }

            function resizeCanvas() {
                // Ensure canvas drawing buffer matches its display size for crisp graphics
                canvas.width = simulationArea.clientWidth;
                canvas.height = simulationArea.clientHeight;
            }

            function initMolecules(targetVolume) {
                molecules = [];
                // Calculate the width of the container rectangle based on the target volume
                const currentRectWidth = (targetVolume / MAX_SLIDER_VOLUME) * canvas.width;
                
                for (let i = 0; i < NUM_MOLECULES; i++) {
                    // Ensure dx and dy are not too close to zero for visible movement
                    let dx, dy;
                    do {
                        dx = (Math.random() - 0.5) * 2 * MAX_SPEED;
                    } while (Math.abs(dx) < 0.2); // Minimum speed component
                    do {
                        dy = (Math.random() - 0.5) * 2 * MAX_SPEED;
                    } while (Math.abs(dy) < 0.2);

                    molecules.push({
                        x: Math.random() * (currentRectWidth - 2 * MOLECULE_RADIUS) + MOLECULE_RADIUS,
                        y: Math.random() * (canvas.height - 2 * MOLECULE_RADIUS) + MOLECULE_RADIUS,
                        dx: dx,
                        dy: dy,
                        color: getRandomColor()
                    });
                }
            }

            function updateVolumeDisplays(volume) {
                currentVolume = volume; // Update global currentVolume state
                volumeValueDisplay.textContent = volume.toFixed(0);
                
                const pressure = K_BOYLE / volume;
                pressureValueDisplay.textContent = pressure.toFixed(2);
            }
            
            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                const currentRectWidth = (currentVolume / MAX_SLIDER_VOLUME) * canvas.width;

                // Draw container boundary
                ctx.strokeStyle = '#3498db';
                ctx.lineWidth = 2; // Line width for the border
                // Adjust rect drawing to keep border within canvas and intended width
                ctx.strokeRect(
                    ctx.lineWidth / 2, 
                    ctx.lineWidth / 2, 
                    currentRectWidth - ctx.lineWidth, 
                    canvas.height - ctx.lineWidth
                );

                // Draw molecules
                molecules.forEach(m => {
                    ctx.beginPath();
                    ctx.arc(m.x, m.y, MOLECULE_RADIUS, 0, Math.PI * 2);
                    ctx.fillStyle = m.color;
                    ctx.fill();
                });
            }

            function updateMolecules() {
                const currentRectWidth = (currentVolume / MAX_SLIDER_VOLUME) * canvas.width;
                const effectiveCanvasHeight = canvas.height; // Full height of canvas

                molecules.forEach(m => {
                    m.x += m.dx;
                    m.y += m.dy;

                    // Wall collisions
                    // Right wall
                    if (m.x + MOLECULE_RADIUS > currentRectWidth - (ctx.lineWidth / 2)) {
                        m.x = currentRectWidth - MOLECULE_RADIUS - (ctx.lineWidth / 2);
                        m.dx *= -1;
                    } 
                    // Left wall
                    else if (m.x - MOLECULE_RADIUS < (ctx.lineWidth / 2)) {
                        m.x = MOLECULE_RADIUS + (ctx.lineWidth / 2);
                        m.dx *= -1;
                    }

                    // Bottom wall
                    if (m.y + MOLECULE_RADIUS > effectiveCanvasHeight - (ctx.lineWidth / 2)) {
                        m.y = effectiveCanvasHeight - MOLECULE_RADIUS - (ctx.lineWidth / 2);
                        m.dy *= -1;
                    } 
                    // Top wall
                    else if (m.y - MOLECULE_RADIUS < (ctx.lineWidth / 2)) {
                        m.y = MOLECULE_RADIUS + (ctx.lineWidth / 2);
                        m.dy *= -1;
                    }
                });
            }

            function animate() {
                updateMolecules();
                draw();
                animationFrameId = requestAnimationFrame(animate);
            }

            function resetSimulation(keepSliderValue = false) {
                if (animationFrameId) {
                    cancelAnimationFrame(animationFrameId);
                }
                
                resizeCanvas(); // Always ensure canvas is sized correctly based on its CSS dimensions

                let volumeToSet;
                if (keepSliderValue) {
                    volumeToSet = parseFloat(volumeSlider.value);
                } else {
                    volumeToSet = INITIAL_SLIDER_VOLUME;
                    volumeSlider.value = INITIAL_SLIDER_VOLUME; // Reset slider position
                }
                
                updateVolumeDisplays(volumeToSet); // Update text displays and currentVolume
                initMolecules(volumeToSet); // Re-initialize molecules based on new dimensions/volume
                animate(); // Restart animation
            }

            // Event Listeners
            volumeSlider.addEventListener('input', (e) => {
                const newVolume = parseFloat(e.target.value);
                updateVolumeDisplays(newVolume);
                // Molecules will adapt to the new boundary in updateMolecules()
            });

            resetButton.addEventListener('click', () => resetSimulation(false));
            
            // Handle window resize: re-initialize simulation keeping current volume setting
            window.addEventListener('resize', () => resetSimulation(true));

            // Initial Setup
            resetSimulation(false); // Start with default values
        });
    </script>
</body>
</html>
