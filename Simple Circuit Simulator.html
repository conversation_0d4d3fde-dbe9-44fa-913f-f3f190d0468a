<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Circuit Simulator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .container {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 700px; /* Max width for content containers */
            margin-bottom: 20px;
        }

        h1 {
            text-align: center;
            color: #333;
        }

        .controls {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 20px;
            justify-content: space-around;
        }

        .control-group {
            flex: 1;
            min-width: 250px; /* Ensure controls don't get too squished */
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }

        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .control-group input[type="range"] {
            width: 100%;
            cursor: pointer;
        }

        .output-display {
            text-align: center;
            font-size: 1.5em;
            margin-bottom: 20px;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 4px;
            font-weight: bold;
        }
        
        #circuit-svg-container {
            width: 100%;
            max-width: 500px; /* Max width of SVG */
            margin: 20px auto; /* Center the SVG container */
            border: 1px solid #ccc;
            background-color: #f0f8ff; /* Light alice blue background for circuit area */
            border-radius: 4px;
            overflow: hidden; /* Ensures SVG corners are rounded if SVG itself has rounded corners */
        }

        #circuit-svg {
            display: block; /* Remove extra space below SVG if it were inline */
            width: 100%;
            height: auto; /* Maintain aspect ratio */
        }

        .info-section {
            margin-top: 20px;
            padding: 15px;
            background-color: #eef;
            border-radius: 4px;
        }
        .info-section h2 {
            margin-top: 0;
            color: #2c3e50;
        }

        /* Responsive adjustments */
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            .container {
                padding: 15px;
            }
            .controls {
                flex-direction: column;
            }
            .control-group {
                min-width: calc(100% - 20px); /* Full width minus padding */
            }
            h1 {
                font-size: 1.8em;
            }
            .output-display {
                font-size: 1.2em;
            }
        }

    </style>
</head>
<body>

    <h1>Simple Circuit Simulator</h1>

    <div class="container">
        <div class="controls">
            <div class="control-group">
                <label for="voltage">Voltage (V): <span id="voltageValue">1.5</span> V</label>
                <input type="range" id="voltage" min="0.1" max="12" step="0.1" value="1.5" aria-label="Voltage">
            </div>
            <div class="control-group">
                <label for="resistance">Resistance (R): <span id="resistanceValue">10.0</span> Ω</label>
                <input type="range" id="resistance" min="0.1" max="100" step="0.1" value="10" aria-label="Resistance">
            </div>
        </div>

        <div class="output-display">
            Current (I): <span id="currentDisplay">0.000</span> A
        </div>

        <div id="circuit-svg-container">
            <svg id="circuit-svg" viewBox="0 0 400 200" preserveAspectRatio="xMidYMid meet">
                <!-- Path for charge flow animation (conventional current: + to -) -->
                <!-- Path: Battery positive -> Top Wire -> Through Lightbulb -> Bottom Wire -> Battery negative (conceptually) -->
                <!-- Coordinates adjusted for a 400x200 viewBox -->
                <path id="charge-flow-path"
                      d="M 70,60 L 330,60 L 330,75 L 330,95 L 330,110 L 70,110 L 70,60 Z"
                      fill="none" stroke="transparent" stroke-width="0"/>

                <!-- Visible Wires -->
                <line x1="70" y1="60" x2="330" y2="60" stroke="dimgray" stroke-width="4"/> <!-- Top wire -->
                <line x1="330" y1="60" x2="330" y2="75" stroke="dimgray" stroke-width="4"/> <!-- Wire to bulb top connector -->
                
                <line x1="330" y1="95" x2="330" y2="110" stroke="dimgray" stroke-width="4"/> <!-- Wire from bulb bottom connector -->
                <line x1="330" y1="110" x2="70" y2="110" stroke="dimgray" stroke-width="4"/> <!-- Bottom wire -->

                <!-- Battery -->
                <rect x="45" y="60" width="25" height="50" fill="#E0E0E0" stroke="black" stroke-width="1"/>
                <rect x="53" y="52" width="10" height="8" fill="darkgray" stroke="black" stroke-width="1"/> <!-- Positive terminal nub -->
                <text x="30" y="72" font-size="14px" font-weight="bold" fill="#333">+</text>
                <text x="30" y="102" font-size="16px" font-weight="bold" fill="#333">-</text>

                <!-- Lightbulb -->
                <!-- Glow must be drawn first to be behind other elements -->
                <circle id="lightbulb-glow" cx="330" cy="85" r="25" fill="yellow" opacity="0"/>
                <!-- Glass part of the bulb -->
                <circle id="lightbulb-glass" cx="330" cy="85" r="15" fill="rgba(220, 220, 180, 0.1)" stroke="#555" stroke-width="1.5"/>
                <!-- Metal base of the bulb -->
                <rect x="324" y="73" width="12" height="4" fill="silver" stroke="#444" stroke-width="0.5"/> <!-- Top connector part of base -->
                <rect x="324" y="93" width="12" height="4" fill="silver" stroke="#444" stroke-width="0.5"/> <!-- Bottom connector part of base -->
                <!-- Leads inside the glass -->
                <line x1="330" y1="75" x2="330" y2="80" stroke="darkgoldenrod" stroke-width="1.5"/> <!-- top lead -->
                <line x1="330" y1="90" x2="330" y2="95" stroke="darkgoldenrod" stroke-width="1.5"/> <!-- bottom lead -->
                <!-- Filament -->
                <path id="lightbulb-filament" d="M 326 82 Q 330 78 334 82 T 326 88 Q 330 92 334 88"
                      stroke="orange" stroke-width="2" fill="none" />
                
                <!-- Particles will be appended here by JS -->
            </svg>
        </div>
    </div>

    <div class="container info-section">
        <h2>Understanding Ohm's Law</h2>
        <p>Ohm's Law describes the relationship between voltage (V), current (I), and resistance (R) in an electrical circuit. It is stated as:</p>
        <p style="text-align:center; font-weight:bold; font-size:1.2em;">V = I × R</p>
        <ul>
            <li><strong>Voltage (V)</strong>: The electrical potential difference between two points, measured in Volts (V). Think of it as the "pressure" that pushes electric charge.</li>
            <li><strong>Current (I)</strong>: The rate of flow of electric charge, measured in Amperes (A). Think of it as the "amount" of electricity flowing.</li>
            <li><strong>Resistance (R)</strong>: The opposition to the flow of current, measured in Ohms (Ω). Think of it as "friction" in the circuit.</li>
        </ul>
        <p>From this formula, we can also derive: I = V / R (used in this simulation to calculate current) and R = V / I.</p>
        <p>The power (P) dissipated by a resistor (like the lightbulb) is given by P = I<sup>2</sup> × R, or P = V × I, measured in Watts (W). This power is what makes the lightbulb glow.</p>
    </div>

    <div class="container info-section" style="background-color: #ffeded;">
        <h2>Disclaimer</h2>
        <p>This is a simplified simulation for educational purposes. It does not account for real-world factors such as:</p>
        <ul>
            <li>Internal resistance of the battery.</li>
            <li>Temperature dependence of resistance (e.g., a real bulb's resistance changes as it heats up).</li>
            <li>Wire resistance (assumed to be zero).</li>
            <li>Complexities of AC circuits or non-ohmic components.</li>
        </ul>
    </div>

    <script>
        let voltage = 1.5;
        let resistance = 10.0;
        let current = 0;
        let power = 0;

        // DOM Elements
        const voltageSlider = document.getElementById('voltage');
        const resistanceSlider = document.getElementById('resistance');
        const voltageValueDisplay = document.getElementById('voltageValue');
        const resistanceValueDisplay = document.getElementById('resistanceValue');
        const currentDisplay = document.getElementById('currentDisplay');

        // SVG Elements
        const lightbulbGlass = document.getElementById('lightbulb-glass');
        const lightbulbGlow = document.getElementById('lightbulb-glow');
        const lightbulbFilament = document.getElementById('lightbulb-filament');
        const chargeFlowPath = document.getElementById('charge-flow-path');
        const svgElement = document.getElementById('circuit-svg');

        const particles = [];
        const NUM_PARTICLES = 15; // Number of charge particles
        const PARTICLE_RADIUS = 2.0; // Adjusted for new viewBox
        const PARTICLE_COLOR = "dodgerblue";

        function init() {
            voltageSlider.addEventListener('input', handleVoltageChange);
            resistanceSlider.addEventListener('input', handleResistanceChange);

            createParticles();
            updateCircuit(); // Initial calculation and rendering
            requestAnimationFrame(animationLoop); // Start animation loop
        }

        function handleVoltageChange(event) {
            voltage = parseFloat(event.target.value);
            voltageValueDisplay.textContent = voltage.toFixed(1);
            updateCircuit();
        }

        function handleResistanceChange(event) {
            resistance = parseFloat(event.target.value);
            resistance = Math.max(0.1, resistance); 
            resistanceValueDisplay.textContent = resistance.toFixed(1);
            updateCircuit();
        }

        function createParticles() {
            const svgNS = "http://www.w3.org/2000/svg";
            const pathLength = chargeFlowPath.getTotalLength();

            for (let i = 0; i < NUM_PARTICLES; i++) {
                const particleElement = document.createElementNS(svgNS, "circle");
                particleElement.setAttribute("r", String(PARTICLE_RADIUS));
                particleElement.setAttribute("fill", PARTICLE_COLOR);
                svgElement.appendChild(particleElement);

                particles.push({
                    element: particleElement,
                    offset: (pathLength / NUM_PARTICLES) * i,
                    isVisible: false // Track visibility
                });
            }
        }
        
        function updateCircuit() {
            voltage = parseFloat(voltageSlider.value);
            resistance = parseFloat(resistanceSlider.value);
            resistance = Math.max(0.1, resistance);

            current = voltage / resistance;
            power = current * current * resistance;

            currentDisplay.textContent = current.toFixed(3);
            updateLightbulbVisuals();
        }

        function updateLightbulbVisuals() {
            const maxPowerForVisuals = 50; 
            let brightnessFactor = Math.min(power / maxPowerForVisuals, 1.0);
            // Apply a non-linear curve to make lower brightness more perceptible
            brightnessFactor = Math.sqrt(brightnessFactor); 

            lightbulbGlass.style.fill = `rgba(255, 255, ${180 + Math.floor(brightnessFactor * 75)}, ${0.1 + brightnessFactor * 0.6})`;
            lightbulbGlow.style.opacity = brightnessFactor;
            
            if (power < 0.05) { 
                lightbulbFilament.style.stroke = "#402000"; 
                lightbulbFilament.style.strokeWidth = "1.5";
            } else if (power < 0.5) { 
                lightbulbFilament.style.stroke = "darkred";
                lightbulbFilament.style.strokeWidth = String(1.5 + brightnessFactor * 1);
            } else if (power < 5) { 
                lightbulbFilament.style.stroke = "orange";
                lightbulbFilament.style.strokeWidth = String(1.5 + brightnessFactor * 1.5);
            } else { 
                lightbulbFilament.style.stroke = `rgb(255, ${255 - Math.floor(brightnessFactor * 100)}, 0)`;
                lightbulbFilament.style.strokeWidth = String(1.5 + brightnessFactor * 2);
            }
        }

        let lastTimestamp = 0;
        function animationLoop(timestamp) {
            if (!chargeFlowPath || typeof chargeFlowPath.getTotalLength !== 'function') {
                requestAnimationFrame(animationLoop);
                return;
            }
            const pathLength = chargeFlowPath.getTotalLength();
            if (pathLength === 0) { 
                 requestAnimationFrame(animationLoop);
                 return;
            }

            if (!lastTimestamp) {
                lastTimestamp = timestamp;
            }
            const deltaTime = (timestamp - lastTimestamp) / 1000; 
            lastTimestamp = timestamp;

            const currentForSpeed = Math.min(current, 5.0); 
            const speed = (currentForSpeed / 5.0) * (pathLength / 1.5); // At 5A, 1.5s per full path traversal
            const distanceToMove = speed * deltaTime;

            const shouldBeVisible = current > 0.001 && distanceToMove > 0.01;

            particles.forEach(p => {
                if (shouldBeVisible) {
                    if (!p.isVisible) {
                        p.element.style.display = '';
                        p.isVisible = true;
                    }
                    p.offset = (p.offset + distanceToMove) % pathLength;
                    const point = chargeFlowPath.getPointAtLength(p.offset);
                    p.element.setAttribute("cx", String(point.x));
                    p.element.setAttribute("cy", String(point.y));
                } else {
                    if (p.isVisible) {
                        p.element.style.display = 'none';
                        p.isVisible = false;
                    }
                }
            });
            requestAnimationFrame(animationLoop);
        }

        document.addEventListener('DOMContentLoaded', init);
    </script>

</body>
</html>
