<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Science Simulations Explorer</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f0f2f5;
            color: #333;
        }
        header, section {
            padding: 20px;
            margin: 15px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1877f2; /* Facebook blue - modern tech feel */
            text-align: center;
        }
        h2 {
            color: #333;
            border-bottom: 2px solid #1877f2;
            padding-bottom: 10px;
            margin-top: 0;
        }
        h3 {
            color: #555;
        }
        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .control-group {
            display: flex;
            flex-direction: column;
        }
        label {
            margin-bottom: 8px;
            font-weight: 600;
            color: #444;
        }
        input[type="range"] {
            width: 100%;
            cursor: pointer;
        }
        button {
            background-color: #1877f2;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.2s ease-in-out;
        }
        button:hover {
            background-color: #166fe5;
        }
        button:active {
            background-color: #1365d1;
        }
        canvas {
            border: 1px solid #ddd;
            display: block;
            margin: 20px auto;
            max-width: 100%;
            background-color: #f9f9f9;
        }
        .results p {
            font-weight: 600;
            background-color: #e7f3ff;
            padding: 8px;
            border-radius: 4px;
            border-left: 4px solid #1877f2;
        }
        .results span {
            font-weight: normal;
            color: #000;
        }

        #attribution p, #disclaimer p {
            font-size: 0.9em;
            color: #555;
        }

        /* Responsive adjustments */
        @media (max-width: 600px) {
            .controls {
                grid-template-columns: 1fr; /* Single column on small screens */
            }
            h1 { font-size: 1.8em; }
            h2 { font-size: 1.5em; }
        }
    </style>
</head>
<body>
    <header>
        <h1>Understanding Science Through Simulations</h1>
        <p><strong>What is a Simulation?</strong> A simulation is an artificial environment created by a computer program to model real-world phenomena. It allows us to experiment with different conditions and observe potential outcomes, which can be difficult, dangerous, or impossible to do in reality. Simulations are powerful tools in science for learning, prediction, and design, helping us understand complex systems by breaking them down into manageable, interactive models.</p>
    </header>

    <main>
        <section id="physics-area">
            <h2>Physics: Projectile Motion</h2>
            <p>Explore how different factors affect the path of a projectile. Adjust the parameters below and observe the trajectory.</p>
            <div class="controls">
                <div class="control-group">
                    <label for="initialVelocity">Initial Velocity (<span id="initialVelocityVal">50</span> m/s):</label>
                    <input type="range" id="initialVelocity" min="1" max="100" value="50" oninput="updateSliderDisplay('initialVelocityVal', this.value)">
                </div>
                <div class="control-group">
                    <label for="launchAngle">Launch Angle (<span id="launchAngleVal">45</span> degrees):</label>
                    <input type="range" id="launchAngle" min="0" max="90" value="45" oninput="updateSliderDisplay('launchAngleVal', this.value)">
                </div>
                <div class="control-group">
                    <label for="mass">Mass (<span id="massVal">1</span> kg):</label>
                    <input type="range" id="mass" min="0.1" max="10" value="1" step="0.1" oninput="updateSliderDisplay('massVal', this.value)">
                </div>
                <div class="control-group">
                    <label for="dragCoefficient">Air Resistance (Drag Coeff: <span id="dragCoefficientVal">0.1</span>):</label>
                    <input type="range" id="dragCoefficient" min="0" max="1" value="0.1" step="0.01" oninput="updateSliderDisplay('dragCoefficientVal', this.value)">
                </div>
            </div>
            <button onclick="runProjectileSimulation()">Simulate Projectile</button>
            <canvas id="projectileCanvas"></canvas>
            <div class="results">
                <p>Range: <span id="rangeDisplay">-</span> m</p>
                <p>Maximum Height: <span id="maxHeightDisplay">-</span> m</p>
            </div>
        </section>

        <section id="chemistry-area">
            <h2>Chemistry: States of Matter</h2>
            <p>Observe how atoms or molecules behave in different states (solid, liquid, gas) by adjusting temperature and pressure.</p>
            <div class="controls">
                <div class="control-group">
                    <label for="temperature">Temperature (<span id="temperatureVal">50</span> / 100):</label>
                    <input type="range" id="temperature" min="0" max="100" value="50" oninput="updateSliderDisplay('temperatureVal', this.value)">
                </div>
                <div class="control-group">
                    <label for="pressure">Pressure (<span id="pressureVal">50</span> / 100):</label>
                    <input type="range" id="pressure" min="0" max="100" value="50" oninput="updateSliderDisplay('pressureVal', this.value)">
                </div>
            </div>
            <button onclick="runMatterSimulation()">Simulate State</button>
            <canvas id="matterCanvas"></canvas>
            <div class="results">
                <p>Current State: <span id="stateDisplay">-</span></p>
            </div>
        </section>
    </main>

    <section id="disclaimer">
        <h3>Disclaimer</h3>
        <p>These simulations are simplified models designed for educational purposes. They use approximations and do not account for all variables or complexities present in real-world phenomena. For instance, the projectile motion simulation assumes a constant drag coefficient and air density, and the states of matter simulation uses a highly simplified model of inter-particle interactions. We encourage you to explore the limitations of these simulations, conduct further research, and compare their results with real-world observations and more comprehensive scientific models.</p>
    </section>

    <section id="attribution">
        <h3>Attribution & Resources</h3>
        <p>This interactive learning tool was inspired by concepts from open educational resources. The physics and chemistry simulations are based on fundamental principles taught in introductory science courses. We encourage further exploration through resources like:</p>
        <ul>
            <li>PhET Interactive Simulations (University of Colorado Boulder)</li>
            <li>Khan Academy (Physics and Chemistry sections)</li>
            <li>OpenStax (Freely available textbooks)</li>
        </ul>
        <p>No external libraries were used in this self-contained demonstration.</p>
    </section>

    <script>
        // --- Generic Utility ---
        function updateSliderDisplay(spanId, value) {
            document.getElementById(spanId).textContent = value;
        }

        // Initialize slider display values on load
        document.addEventListener('DOMContentLoaded', () => {
            updateSliderDisplay('initialVelocityVal', document.getElementById('initialVelocity').value);
            updateSliderDisplay('launchAngleVal', document.getElementById('launchAngle').value);
            updateSliderDisplay('massVal', document.getElementById('mass').value);
            updateSliderDisplay('dragCoefficientVal', document.getElementById('dragCoefficient').value);
            updateSliderDisplay('temperatureVal', document.getElementById('temperature').value);
            updateSliderDisplay('pressureVal', document.getElementById('pressure').value);
            
            setupCanvases();
        });
        
        window.addEventListener('resize', () => {
            setupCanvases();
            // Optional: could re-run simulations if they were active, but for simplicity, user can re-click.
            // For now, resizing will clear canvases. User needs to re-simulate.
            clearPhysicsCanvas();
            clearChemistryCanvasAndStopAnimation();
        });

        function setupCanvases() {
            const physicsCanvas = document.getElementById('projectileCanvas');
            const chemistryCanvas = document.getElementById('matterCanvas');
            
            const physicsContainerWidth = physicsCanvas.parentElement.clientWidth * 0.95;
            physicsCanvas.width = Math.max(300, physicsContainerWidth);
            physicsCanvas.height = physicsCanvas.width * 0.6;

            const chemistryContainerWidth = chemistryCanvas.parentElement.clientWidth * 0.9;
            chemistryCanvas.width = Math.min(400, Math.max(250, chemistryContainerWidth));
            chemistryCanvas.height = chemistryCanvas.width; // Keep it square
        }

        function clearPhysicsCanvas() {
            const canvas = document.getElementById('projectileCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            document.getElementById('rangeDisplay').textContent = '-';
            document.getElementById('maxHeightDisplay').textContent = '-';
        }

        function clearChemistryCanvasAndStopAnimation() {
            const canvas = document.getElementById('matterCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            document.getElementById('stateDisplay').textContent = '-';
            if (chemistryAnimationId) {
                cancelAnimationFrame(chemistryAnimationId);
                chemistryAnimationId = null;
            }
        }

        // --- Physics Simulation: Projectile Motion ---
        const G = 9.81; // m/s^2
        const RHO_AIR = 1.225; // kg/m^3 (sea level standard air density)
        const PROJECTILE_AREA = 0.01; // m^2, assumed cross-sectional area

        function runProjectileSimulation() {
            const v0 = parseFloat(document.getElementById('initialVelocity').value);
            const angleDeg = parseFloat(document.getElementById('launchAngle').value);
            const mass = parseFloat(document.getElementById('mass').value);
            const Cd = parseFloat(document.getElementById('dragCoefficient').value);

            const angleRad = angleDeg * Math.PI / 180;

            let x = 0, y = 0;
            let vx = v0 * Math.cos(angleRad);
            let vy = v0 * Math.sin(angleRad);
            const dt = 0.01; // time step in seconds
            let t = 0;

            let maxHeight = 0;
            const trajectoryPoints = [{x: 0, y: 0}];

            while (y >= 0 || t < 0.1) { // t < 0.1 condition to ensure it launches a bit
                if (y > maxHeight) {
                    maxHeight = y;
                }

                let F_drag_x = 0, F_drag_y = 0;
                if (Cd > 0) {
                    const v_mag_sq = vx*vx + vy*vy;
                    const v_mag = Math.sqrt(v_mag_sq);
                    if (v_mag > 1e-6) { // Avoid division by zero if velocity is near zero
                        const F_drag_mag = 0.5 * RHO_AIR * v_mag_sq * Cd * PROJECTILE_AREA;
                        F_drag_x = -F_drag_mag * (vx / v_mag);
                        F_drag_y = -F_drag_mag * (vy / v_mag);
                    }
                }
                
                const ax = F_drag_x / mass;
                const ay = -G + (F_drag_y / mass);

                vx += ax * dt;
                vy += ay * dt;
                x += vx * dt;
                y += vy * dt;
                t += dt;

                if (y < 0 && t > 0.1) break; // Ground hit
                trajectoryPoints.push({x: x, y: y});
                if (t > 300) break; // Safety break for extreme parameters
            }
            
            const range = x;
            document.getElementById('rangeDisplay').textContent = range.toFixed(2);
            document.getElementById('maxHeightDisplay').textContent = maxHeight.toFixed(2);

            drawProjectileTrajectory(trajectoryPoints, range, maxHeight);
        }

        function drawProjectileTrajectory(points, totalRange, peakHeight) {
            const canvas = document.getElementById('projectileCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            if (points.length === 0) return;

            const padding = 20;
            const plotWidth = canvas.width - 2 * padding;
            const plotHeight = canvas.height - 2 * padding;

            // Determine scale factors to fit the trajectory
            // Ensure peakHeight and totalRange are not zero to avoid division by zero
            const effectivePeakHeight = Math.max(peakHeight, 1); // min 1m to avoid issues with flat trajectories
            const effectiveTotalRange = Math.max(totalRange, 1); // min 1m

            const scaleX = plotWidth / effectiveTotalRange;
            const scaleY = plotHeight / effectivePeakHeight;
            
            // Use a common scale factor to maintain aspect ratio, or independent for fill
            // Let's use independent scaling to fill the canvas area better for varying trajectories
            
            ctx.beginPath();
            ctx.moveTo(padding, canvas.height - padding); // Start at origin (bottom-left of plot area)

            points.forEach(p => {
                const canvasX = padding + p.x * scaleX;
                const canvasY = canvas.height - padding - p.y * scaleY;
                ctx.lineTo(canvasX, canvasY);
            });

            ctx.strokeStyle = '#1877f2';
            ctx.lineWidth = 2;
            ctx.stroke();

            // Draw simple axes
            ctx.beginPath();
            // Y-axis
            ctx.moveTo(padding, padding);
            ctx.lineTo(padding, canvas.height - padding);
            // X-axis
            ctx.lineTo(canvas.width - padding, canvas.height - padding);
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 1;
            ctx.stroke();

            // Labels for axes (simple)
            ctx.fillStyle = '#333';
            ctx.fillText('Height', padding + 5, padding - 5);
            ctx.fillText('Range', canvas.width - padding - 30, canvas.height - padding - 5);
            ctx.fillText('0', padding - 10, canvas.height - padding + 10);
        }

        // --- Chemistry Simulation: States of Matter ---
        const NUM_PARTICLES = 40;
        let particles = [];
        let chemistryAnimationId = null;
        const PARTICLE_RADIUS = 4;
        let currentMatterState = '';

        function getMatterState(tempSliderVal, pressSliderVal) { // Slider values 0-100
            // Simplified state determination logic
            if (tempSliderVal < 25) { // Low temperature
                return 'Solid';
            } else if (tempSliderVal < 65) { // Medium temperature
                if (pressSliderVal > 60) return 'Solid'; // High pressure can solidify
                return 'Liquid';
            } else { // High temperature
                if (pressSliderVal > 85) return 'Liquid'; // Very high pressure might condense
                return 'Gas';
            }
        }

        function runMatterSimulation() {
            if (chemistryAnimationId) {
                cancelAnimationFrame(chemistryAnimationId);
            }

            const temp = parseFloat(document.getElementById('temperature').value);
            const press = parseFloat(document.getElementById('pressure').value);
            
            currentMatterState = getMatterState(temp, press);
            document.getElementById('stateDisplay').textContent = currentMatterState;

            const canvas = document.getElementById('matterCanvas');
            initializeParticles(canvas, currentMatterState, temp, press);
            
            animateParticles();
        }
        
        function initializeParticles(canvas, state, temp, press) {
            particles = [];
            const particleSpeedFactor = temp / 50 + 0.5; // Base speed related to temp

            for (let i = 0; i < NUM_PARTICLES; i++) {
                let p = {
                    x: 0, y: 0,
                    vx: (Math.random() - 0.5) * particleSpeedFactor,
                    vy: (Math.random() - 0.5) * particleSpeedFactor,
                    radius: PARTICLE_RADIUS,
                    color: `hsl(${Math.random() * 360}, 70%, 60%)` // Random color for visual appeal
                };

                if (state === 'Solid') {
                    // Arrange in a grid-like structure
                    const particlesPerRow = Math.ceil(Math.sqrt(NUM_PARTICLES));
                    const spacing = canvas.width / (particlesPerRow + 1);
                    p.gridX = ((i % particlesPerRow) + 1) * spacing;
                    p.gridY = (Math.floor(i / particlesPerRow) + 1) * spacing;
                    p.x = p.gridX;
                    p.y = p.gridY;
                    p.vx = (Math.random() - 0.5) * 0.2 * particleSpeedFactor; // Small vibrations
                    p.vy = (Math.random() - 0.5) * 0.2 * particleSpeedFactor;
                } else if (state === 'Liquid') {
                    // Randomly in lower half, simulating gravity/containment
                    p.x = Math.random() * (canvas.width - 2 * p.radius) + p.radius;
                    p.y = Math.random() * (canvas.height / 2 - 2 * p.radius) + canvas.height / 2 + p.radius;
                    // Pressure might make it more compact, temp affects speed
                    p.vx *= (1 - press/200); // Higher pressure slightly dampens initial random spread
                    p.vy *= (1 - press/200);
                } else { // Gas
                    p.x = Math.random() * (canvas.width - 2 * p.radius) + p.radius;
                    p.y = Math.random() * (canvas.height - 2 * p.radius) + p.radius;
                    p.vx *= 1.5; // Gases generally faster
                    p.vy *= 1.5; 
                }
                particles.push(p);
            }
        }

        function animateParticles() {
            const canvas = document.getElementById('matterCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const temp = parseFloat(document.getElementById('temperature').value); // For dynamic speed adjustment
            const press = parseFloat(document.getElementById('pressure').value); // For dynamic confinement
            const particleSpeedFactor = temp / 50 + 0.5; 

            particles.forEach(p => {
                if (currentMatterState === 'Solid') {
                    // Vibrate around grid position
                    p.x += p.vx * particleSpeedFactor * 0.2;
                    p.y += p.vy * particleSpeedFactor * 0.2;
                    // Gently pull back to grid position to simulate bonds
                    p.vx += (p.gridX - p.x) * 0.01;
                    p.vy += (p.gridY - p.y) * 0.01;
                    // Dampen vibration
                    p.vx *= 0.9;
                    p.vy *= 0.9;
                    // Boundaries (should mostly stay within grid)
                    if (p.x < p.radius || p.x > canvas.width - p.radius) p.vx *= -1;
                    if (p.y < p.radius || p.y > canvas.height - p.radius) p.vy *= -1;

                } else if (currentMatterState === 'Liquid') {
                    p.x += p.vx * particleSpeedFactor;
                    p.y += p.vy * particleSpeedFactor;

                    // Bouncing off walls
                    if (p.x < p.radius || p.x > canvas.width - p.radius) p.vx *= -1;
                    // Liquid specific: tends to stay in lower part, pressure can affect density
                    // A very crude "gravity" or containment effect:
                    const liquidLevel = canvas.height * (1 - press / 250); // Higher pressure = more compressed
                    if (p.y < liquidLevel * 0.4 && p.vy < 0) { // Bouncing off "surface" if trying to escape upwards too much
                         p.vy *= -0.5; // Less bouncy surface
                    }
                    if (p.y > canvas.height - p.radius && p.vy > 0) { // Bottom
                        p.vy *= -0.8;
                         p.y = canvas.height - p.radius;
                    }
                     // Simple inter-particle repulsion (very basic)
                    /* particles.forEach(other => {
                        if (p === other) return;
                        let dx = other.x - p.x;
                        let dy = other.y - p.y;
                        let dist = Math.sqrt(dx*dx + dy*dy);
                        if (dist < p.radius * 2.5) { // If too close
                            p.vx -= dx * 0.001 * particleSpeedFactor;
                            p.vy -= dy * 0.001 * particleSpeedFactor;
                        }
                    }); */

                } else { // Gas
                    p.x += p.vx * particleSpeedFactor * 1.5; // Gas particles move faster
                    p.y += p.vy * particleSpeedFactor * 1.5;

                    if (p.x < p.radius || p.x > canvas.width - p.radius) {
                        p.vx *= -1;
                        p.x = Math.max(p.radius, Math.min(p.x, canvas.width - p.radius)); // Prevent sticking
                    }
                    if (p.y < p.radius || p.y > canvas.height - p.radius) {
                        p.vy *= -1;
                        p.y = Math.max(p.radius, Math.min(p.y, canvas.height - p.radius)); // Prevent sticking
                    }
                }

                // Ensure particles stay within bounds after velocity changes (clamp position)
                p.x = Math.max(p.radius, Math.min(canvas.width - p.radius, p.x));
                p.y = Math.max(p.radius, Math.min(canvas.height - p.radius, p.y));

                ctx.beginPath();
                ctx.arc(p.x, p.y, p.radius, 0, Math.PI * 2);
                ctx.fillStyle = p.color;
                ctx.fill();
            });

            chemistryAnimationId = requestAnimationFrame(animateParticles);
        }
    </script>
</body>
</html>
