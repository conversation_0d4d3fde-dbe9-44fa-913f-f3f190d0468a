/* Physics Virtual Lab - Index Page Styles */

/* Import all CSS modules */
@import url('./css/style.css');
@import url('./css/animations.css');
@import url('./css/components.css');

/* Additional styles specific to index page */

/* Navigation enhancements */
.navbar.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .navbar.scrolled {
  background: rgba(31, 41, 55, 0.98);
}

.navbar-nav a.active {
  background-color: var(--primary-100);
  color: var(--primary-700);
}

[data-theme="dark"] .navbar-nav a.active {
  background-color: var(--primary-900);
  color: var(--primary-300);
}

/* Dropdown menu styles */
.nav-dropdown {
  position: relative;
}

.nav-dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  padding: var(--spacing-sm);
  min-width: 200px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all var(--transition-fast);
  z-index: 1000;
  list-style: none;
  margin: 0;
}

[data-theme="dark"] .nav-dropdown-menu {
  background: var(--gray-800);
}

.nav-dropdown.active .nav-dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.nav-dropdown-menu li {
  margin: 0;
}

.nav-dropdown-menu a {
  display: block;
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-primary);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.nav-dropdown-menu a:hover {
  background-color: var(--primary-50);
  color: var(--primary-600);
}

[data-theme="dark"] .nav-dropdown-menu a:hover {
  background-color: var(--primary-900);
  color: var(--primary-300);
}

/* Mobile menu styles */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-sm);
  gap: 4px;
}

.mobile-menu-toggle span {
  width: 25px;
  height: 3px;
  background: var(--text-primary);
  border-radius: 2px;
  transition: all var(--transition-fast);
}

.mobile-menu-toggle.active span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

@media (max-width: 768px) {
  .mobile-menu-toggle {
    display: flex;
  }
  
  .navbar-nav-container {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-lg);
    transform: translateY(-10px);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
  }
  
  [data-theme="dark"] .navbar-nav-container {
    background: var(--gray-800);
  }
  
  .navbar.mobile-open .navbar-nav-container {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }
  
  .navbar-nav {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .navbar-actions {
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

/* Notification system styles */
.notification-container {
  position: fixed;
  top: 80px;
  right: var(--spacing-lg);
  z-index: 2000;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  max-width: 400px;
}

.notification {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  overflow: hidden;
  border-left: 4px solid var(--primary-500);
}

[data-theme="dark"] .notification {
  background: var(--gray-800);
}

.notification-success {
  border-left-color: #22c55e;
}

.notification-error {
  border-left-color: #ef4444;
}

.notification-warning {
  border-left-color: #f59e0b;
}

.notification-info {
  border-left-color: var(--primary-500);
}

.notification-content {
  display: flex;
  align-items: center;
  padding: var(--spacing-md);
  gap: var(--spacing-sm);
}

.notification-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.notification-message {
  flex-grow: 1;
  color: var(--text-primary);
}

.notification-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  flex-shrink: 0;
}

.notification-close:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

/* Loading overlay styles */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 3000;
}

.loading-content {
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--spacing-2xl);
  text-align: center;
  box-shadow: var(--shadow-xl);
}

[data-theme="dark"] .loading-content {
  background: var(--gray-800);
}

.loading-message {
  margin-top: var(--spacing-lg);
  color: var(--text-primary);
  font-weight: 500;
}

/* Scroll progress indicator */
.scroll-progress {
  position: fixed;
  top: 0;
  left: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-500), var(--secondary-500));
  z-index: 1001;
  transition: width var(--transition-fast);
}

/* Enhanced experiment card hover effects */
.experiment-card {
  position: relative;
  overflow: hidden;
}

.experiment-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
  z-index: 1;
}

.experiment-card:hover::before {
  left: 100%;
}

/* Completion badge */
.completion-badge {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  background: #22c55e;
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  font-weight: bold;
  z-index: 2;
}

/* Difficulty indicators */
.difficulty-beginner {
  background: #22c55e;
  color: white;
  padding: 2px 8px;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

.difficulty-intermediate {
  background: #f59e0b;
  color: white;
  padding: 2px 8px;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

.difficulty-advanced {
  background: #ef4444;
  color: white;
  padding: 2px 8px;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

/* Social links in footer */
.social-links {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  transition: all var(--transition-fast);
}

.social-links a:hover {
  background: var(--primary-600);
  color: white;
  transform: translateY(-2px);
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .notification-container {
    left: var(--spacing-md);
    right: var(--spacing-md);
    max-width: none;
  }
  
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
  
  .quick-access-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .experiment-grid {
    grid-template-columns: 1fr;
  }
}

/* Print styles */
@media print {
  .navbar,
  .notification-container,
  .loading-overlay,
  .scroll-progress {
    display: none !important;
  }
  
  .hero {
    background: none !important;
    color: black !important;
  }
  
  .card,
  .experiment-card {
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .btn {
    border: 2px solid currentColor;
  }
  
  .card {
    border: 2px solid var(--border-color);
  }
  
  .navbar {
    border-bottom: 2px solid var(--border-color);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .scroll-progress {
    transition: none;
  }
  
  .notification {
    animation: none !important;
  }
  
  .experiment-card::before {
    display: none;
  }
}
