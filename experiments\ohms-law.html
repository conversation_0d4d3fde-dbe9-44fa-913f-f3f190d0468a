<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ohm's Law Explorer - Physics Virtual Lab</title>
    <meta name="author" content="Dr<PERSON> <PERSON>, SUST-BME">
    <meta name="copyright" content="Dr. <PERSON>il - +249912867327, +966538076790">
    <meta name="institution" content="Sudan University of Science and Technology - Biomedical Engineering">
    <meta name="description" content="Interactive Ohm's Law simulation for understanding voltage, current, and resistance relationships">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/animations.css">
    <link rel="stylesheet" href="../css/components.css">
    <style>
        body {
            font-family: var(--font-family-sans);
            margin: 0;
            padding: 0;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .experiment-header {
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--border-color);
            padding: var(--spacing-md);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .experiment-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }
        
        .back-button {
            background: var(--primary-600);
            color: white;
            border: none;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-md);
            text-decoration: none;
            font-weight: 500;
            transition: all var(--transition-fast);
        }
        
        .back-button:hover {
            background: var(--primary-700);
            transform: translateY(-2px);
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: var(--spacing-lg);
            gap: var(--spacing-lg);
        }

        .experiment-container {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            padding: var(--spacing-xl);
            width: 100%;
            max-width: 800px;
        }

        [data-theme="dark"] .experiment-container {
            background: var(--gray-700);
        }

        .circuit-area {
            width: 100%;
            max-width: 500px;
            margin: 0 auto var(--spacing-lg) auto;
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            border: 2px solid var(--border-color);
        }

        #circuitSvg {
            width: 100%;
            height: auto;
            display: block;
        }

        .controls-section {
            background: var(--bg-secondary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }

        .control-group {
            margin-bottom: var(--spacing-lg);
        }

        .control-group:last-child {
            margin-bottom: 0;
        }

        .control-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-primary);
        }

        .control-slider {
            width: 100%;
            height: 8px;
            border-radius: 4px;
            background: var(--gray-200);
            outline: none;
            cursor: pointer;
            margin-bottom: var(--spacing-sm);
            accent-color: var(--primary-600);
        }

        [data-theme="dark"] .control-slider {
            background: var(--gray-600);
        }

        .value-display {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--primary-600);
            text-align: center;
            padding: var(--spacing-sm);
            background: var(--bg-primary);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
        }

        .data-display {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .data-card {
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-md);
            text-align: center;
            border: 1px solid var(--border-color);
        }

        .data-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-xs);
        }

        .data-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-600);
        }

        .explanation-section {
            background: var(--bg-secondary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
        }

        .explanation-title {
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
        }

        .formula {
            text-align: center;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-600);
            background: var(--bg-primary);
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
            margin: var(--spacing-md) 0;
            border: 2px solid var(--primary-200);
        }

        [data-theme="dark"] .formula {
            border-color: var(--primary-800);
        }

        .insights {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-md);
            margin-top: var(--spacing-lg);
        }

        .insight-card {
            background: var(--bg-primary);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            border-left: 4px solid var(--accent-500);
        }

        .insight-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
        }

        .insight-text {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        /* SVG Styling */
        .wire { stroke: var(--gray-600); stroke-width: 3px; fill: none; }
        .battery-body { fill: var(--secondary-200); stroke: var(--gray-700); stroke-width: 2px; }
        .battery-terminal { fill: var(--gray-500); }
        .battery-text { font-size: 20px; text-anchor: middle; fill: var(--gray-700); dominant-baseline: central; user-select: none; }
        .resistor-path { stroke: var(--gray-700); stroke-width: 3px; fill: none; }
        .component-label { font-size: 12px; text-anchor: middle; fill: var(--text-primary); user-select: none; }
        #lightBulbElement { stroke: var(--gray-600); stroke-width: 2px; transition: fill 0.3s ease; }
        #lightBulbGlow { transition: opacity 0.3s ease; }
        .bulb-base { fill: var(--gray-500); stroke: var(--gray-700); stroke-width: 1px; }

        @media (max-width: 768px) {
            .main-content {
                padding: var(--spacing-md);
            }
            
            .experiment-container {
                padding: var(--spacing-lg);
            }
            
            .data-display {
                grid-template-columns: 1fr;
            }
            
            .insights {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="experiment-header">
        <h1 class="experiment-title">🔌 Ohm's Law Explorer</h1>
        <a href="../dashboard.html" class="back-button">← Back to Dashboard</a>
    </div>

    <div class="main-content">
        <div class="experiment-container animate-fade-in">
            <div class="circuit-area">
                <svg id="circuitSvg" viewBox="0 0 400 250" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <filter id="glowEffectFilter">
                            <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
                        </filter>
                    </defs>
                    
                    <!-- Battery -->
                    <rect x="20" y="85" width="40" height="80" class="battery-body"/>
                    <rect x="35" y="75" width="10" height="10" class="battery-terminal"/>
                    <text x="40" y="105" class="battery-text">+</text>
                    <text x="40" y="145" class="battery-text">-</text>
                    <text x="40" y="60" class="component-label">Battery (12V)</text>

                    <!-- Wires -->
                    <line x1="60" y1="90" x2="100" y2="90" class="wire"/>
                    <line x1="220" y1="90" x2="280" y2="90" class="wire"/>
                    <line x1="280" y1="90" x2="280" y2="105" class="wire"/>
                    <line x1="280" y1="165" x2="280" y2="180" class="wire"/>
                    <line x1="280" y1="180" x2="60" y2="180" class="wire"/>
                    <line x1="60" y1="180" x2="60" y2="160" class="wire"/>

                    <!-- Resistor -->
                    <path d="M100 90 l10 -8 l20 16 l20 -16 l20 16 l20 -16 l20 16 l10 -8" class="resistor-path"/>
                    <text x="160" y="60" class="component-label">Resistor</text>

                    <!-- Light Bulb -->
                    <circle id="lightBulbGlow" cx="280" cy="135" r="25" fill="hsl(60, 100%, 70%)" opacity="0.0" filter="url(#glowEffectFilter)"/>
                    <circle id="lightBulbElement" cx="280" cy="135" r="22" fill="hsl(60, 100%, 20%)"/>
                    <rect x="272" y="157" width="16" height="10" class="bulb-base"/>
                    <text x="280" y="195" class="component-label">Light Bulb</text>
                </svg>
            </div>

            <div class="controls-section">
                <div class="control-group">
                    <label for="resistanceSlider" class="control-label">Adjust Resistance (R):</label>
                    <input type="range" id="resistanceSlider" class="control-slider" min="10" max="200" value="100" step="1">
                    <div class="value-display">
                        Resistance: <span id="resistanceValue">100.0</span> Ω
                    </div>
                </div>
            </div>

            <div class="data-display">
                <div class="data-card">
                    <div class="data-label">Voltage (V)</div>
                    <div class="data-value">12.0 V</div>
                </div>
                <div class="data-card">
                    <div class="data-label">Current (I)</div>
                    <div class="data-value"><span id="currentValue">0.12</span> A</div>
                </div>
                <div class="data-card">
                    <div class="data-label">Power (P)</div>
                    <div class="data-value"><span id="powerValue">1.44</span> W</div>
                </div>
            </div>
        </div>

        <div class="experiment-container explanation-section animate-slide-up">
            <h2 class="explanation-title">Understanding Ohm's Law</h2>
            <p>Ohm's Law describes the fundamental relationship between voltage, current, and resistance in electrical circuits:</p>
            
            <div class="formula">V = I × R</div>
            
            <p>Where:</p>
            <ul>
                <li><strong>V</strong> = Voltage (measured in Volts)</li>
                <li><strong>I</strong> = Current (measured in Amperes)</li>
                <li><strong>R</strong> = Resistance (measured in Ohms)</li>
            </ul>

            <div class="insights">
                <div class="insight-card">
                    <div class="insight-title">Inverse Relationship</div>
                    <div class="insight-text">As resistance increases, current decreases (when voltage is constant). This makes the bulb dimmer.</div>
                </div>
                <div class="insight-card">
                    <div class="insight-title">Power Calculation</div>
                    <div class="insight-text">Power (P) = V × I = I² × R. This determines how bright the bulb glows.</div>
                </div>
                <div class="insight-card">
                    <div class="insight-title">Real-World Applications</div>
                    <div class="insight-text">Ohm's Law is used in designing circuits, calculating power consumption, and electrical safety.</div>
                </div>
            </div>
        </div>
    </div>

    <script type="module" src="../js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const VOLTAGE = 12; // Volts

            const resistanceSlider = document.getElementById('resistanceSlider');
            const resistanceValueDisplay = document.getElementById('resistanceValue');
            const currentValueDisplay = document.getElementById('currentValue');
            const powerValueDisplay = document.getElementById('powerValue');
            const lightBulbElement = document.getElementById('lightBulbElement');
            const lightBulbGlow = document.getElementById('lightBulbGlow');

            const minResistance = parseFloat(resistanceSlider.min);
            const maxResistance = parseFloat(resistanceSlider.max);
            const maxCurrent = VOLTAGE / minResistance;
            const minCurrent = VOLTAGE / maxResistance;

            function updateCircuit() {
                const resistance = parseFloat(resistanceSlider.value);
                const current = VOLTAGE / resistance;
                const power = VOLTAGE * current;

                // Update displays
                resistanceValueDisplay.textContent = resistance.toFixed(1);
                currentValueDisplay.textContent = current.toFixed(3);
                powerValueDisplay.textContent = power.toFixed(2);

                // Calculate brightness
                let brightnessFactor = (current - minCurrent) / (maxCurrent - minCurrent);
                brightnessFactor = Math.max(0, Math.min(1, brightnessFactor));
                brightnessFactor = Math.pow(brightnessFactor, 0.5);

                // Update bulb appearance
                const minLightness = 20;
                const maxLightness = 95;
                const bulbLightness = minLightness + brightnessFactor * (maxLightness - minLightness);
                lightBulbElement.style.fill = `hsl(60, 100%, ${bulbLightness}%)`;

                const maxGlowOpacity = 0.6;
                lightBulbGlow.style.opacity = brightnessFactor * maxGlowOpacity;
            }

            resistanceSlider.addEventListener('input', updateCircuit);
            updateCircuit();
        });
    </script>
</body>
</html>
