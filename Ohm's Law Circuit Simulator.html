<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ohm's Law Circuit Simulator</title>
    <style>
        /* Basic Reset & Body Styles */
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .container {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 700px; /* Max width for larger screens */
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 20px;
        }

        /* Controls Section */
        .controls {
            display: flex;
            flex-wrap: wrap; /* Allow wrapping on smaller screens */
            justify-content: space-around;
            margin-bottom: 20px;
            gap: 20px; /* Spacing between control groups */
        }

        .control-group {
            flex: 1; /* Allow groups to grow and shrink */
            min-width: 250px; /* Minimum width before wrapping */
            margin-bottom: 10px;
        }

        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .control-group input[type="range"] {
            width: 100%;
            cursor: pointer;
        }

        .control-group span {
            font-weight: normal;
            color: #555;
        }

        /* Info Section */
        .info-section {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 5px;
        }

        .info-section p {
            margin: 10px 0; /* Increased margin for better readability */
        }

        .equation {
            font-size: 1.5em;
            font-weight: bold;
            text-align: center;
            color: #007bff;
            margin: 15px 0;
        }

        .dynamic-message {
            margin-top: 10px;
            padding: 10px;
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            border-radius: 4px;
            text-align: center;
            min-height: 1.6em; /* To prevent layout shift */
            font-style: italic;
        }

        /* Circuit Visualization */
        .circuit-diagram {
            width: 100%;
            max-width: 450px; /* Control max size of SVG, increased for better component spacing */
            margin: 30px auto; /* Center the diagram */
            border: 1px solid #ccc;
            padding: 10px;
            box-sizing: border-box;
            background-color: #f0f8ff; /* Light blue background for diagram */
        }

        .circuit-svg {
            width: 100%;
            height: auto; /* Maintain aspect ratio */
            display: block;
        }

        /* SVG Elements Styling */
        .wire {
            stroke: #333; /* Darker wire */
            stroke-width: 2.5; /* Slightly thicker wire */
            fill: none;
        }

        .component-text {
            font-family: Arial, sans-serif;
            font-size: 12px;
            fill: #333;
            text-anchor: middle;
        }
        
        .battery-symbol {
            font-size: 18px;
            font-weight: bold;
            text-anchor: middle;
        }

        .battery-positive-terminal { fill: #e74c3c; } /* Red for positive */
        .battery-negative-terminal { fill: #3498db; } /* Blue for negative */
        /* .battery-body { fill: #f0e68c; stroke: #ccc; stroke-width:1; }  Optional body */


        .resistor-body {
            stroke: #2c3e50;
            stroke-width: 2;
            fill: none;
        }
        .resistor-symbol {
            font-size: 18px;
            font-weight: bold;
            text-anchor: middle;
        }

        .ammeter-body {
            stroke: #27ae60; /* Green border */
            stroke-width: 2;
            fill: #e8f8f5; /* Light green fill */
        }
        .ammeter-text-symbol {
            font-size: 20px; /* Larger 'A' */
            fill: #145A32; /* Darker green for text */
            text-anchor: middle;
            dominant-baseline: central;
        }

        .electron {
            fill: #007bff; /* Blue electrons */
            /* Animation will be controlled by JS by changing CSS variables or directly setting animation-duration */
        }

        /* Animation path for electrons - this path is drawn but not visible */
        #electronPath {
            stroke: none; 
        }

        /* Responsive adjustments */
        @media (max-width: 600px) {
            .controls {
                flex-direction: column; /* Stack controls vertically */
            }
            .control-group {
                width: 100%; /* Full width for control groups */
                min-width: unset;
            }
            h1 {
                font-size: 1.6em; /* Slightly smaller h1 on mobile */
            }
            .equation {
                font-size: 1.3em;
            }
            .component-text {
                font-size: 10px;
            }
            .battery-symbol, .resistor-symbol {
                font-size: 16px;
            }
            .ammeter-text-symbol {
                font-size: 18px;
            }
        }

    </style>
</head>
<body>
    <div class="container">
        <h1>Ohm's Law Circuit Simulator</h1>

        <div class="controls">
            <div class="control-group">
                <label for="voltage">Voltage (V): <span id="voltageValue">5.0</span> V</label>
                <input type="range" id="voltage" min="0" max="10" value="5" step="0.1">
            </div>
            <div class="control-group">
                <label for="resistance">Resistance (R): <span id="resistanceValue">5.0</span> Ω</label>
                <input type="range" id="resistance" min="1" max="10" value="5" step="0.1">
            </div>
        </div>

        <div class="circuit-diagram">
            <!-- SVG ViewBox: min-x, min-y, width, height -->
            <svg class="circuit-svg" viewBox="0 0 300 200" preserveAspectRatio="xMidYMid meet">
                <!-- Electron Animation Path (matches wire layout) -->
                <!-- Electron flow: Bat- -> Resistor -> Ammeter -> Bat+ -->
                <!-- Coordinates are for electron flow direction (conventional current is opposite) -->
                <path id="electronPath" 
                      d="M 70 130 <!-- Start at Bat- output (bottom plate of battery symbol) -->
                         L 70 160 <!-- Down -->
                         L 230 160 <!-- Right, along bottom wire -->
                         L 230 120 <!-- Up, to bottom of resistor -->
                         <!-- Path segment through resistor -->
                         L 230 80  <!-- Up, from top of resistor -->
                         L 230 40  <!-- Up, along right wire -->
                         L 170 40  <!-- Left, to right of ammeter -->
                         <!-- Path segment through ammeter -->
                         L 130 40  <!-- Left, from left of ammeter -->
                         L 70 40   <!-- Left, along top wire -->
                         L 70 70 Z" /> <!-- Down, to Bat+ input (top plate of battery symbol), Z closes path -->

                <!-- Wires connecting components -->
                <line class="wire" x1="70" y1="70" x2="70" y2="40" />   <!-- Bat+ (top) to Top-Left Corner -->
                <line class="wire" x1="70" y1="40" x2="130" y2="40" />  <!-- Top-Left Corner to Ammeter Left -->
                <line class="wire" x1="170" y1="40" x2="230" y2="40" /> <!-- Ammeter Right to Top-Right Corner -->
                <line class="wire" x1="230" y1="40" x2="230" y2="80" /> <!-- Top-Right Corner to Resistor Top -->
                <line class="wire" x1="230" y1="120" x2="230" y2="160" /> <!-- Resistor Bottom to Bottom-Right Corner -->
                <line class="wire" x1="230" y1="160" x2="70" y2="160" /> <!-- Bottom-Right Corner to Bottom-Left Corner -->
                <line class="wire" x1="70" y1="160" x2="70" y2="130" />  <!-- Bottom-Left Corner to Bat- (bottom) -->

                <!-- Voltage Source (Battery Symbol) -->
                <!-- Positive plate (shorter, thicker) -->
                <rect x="65" y="70" width="10" height="20" class="battery-positive-terminal" />
                 <!-- Negative plate (longer, thinner by convention for battery symbol) -->
                <rect x="67" y="110" width="6" height="20" class="battery-negative-terminal" />
                <text x="45" y="100" class="battery-symbol">V</text> <!-- Label for Voltage Source -->
                <text x="80" y="80" class="component-text">+</text> <!-- Positive sign -->
                <text x="80" y="125" class="component-text">-</text> <!-- Negative sign -->


                <!-- Ammeter -->
                <circle cx="150" cy="40" r="15" class="ammeter-body" />
                <text x="150" y="40" class="ammeter-text-symbol">A</text>
                <text x="150" y="18" class="component-text">Ammeter</text>

                <!-- Resistor -->
                <!-- Zigzag path for resistor symbol -->
                <path class="resistor-body" d="M 230 80 l 0 5 l -7 4 l 14 8 l -14 8 l 14 8 l -7 4 l 0 5" /> 
                <text x="255" y="100" class="resistor-symbol">R</text> <!-- Label for Resistor -->
                <text x="230" y="138" class="component-text">Resistor</text>
                
                <!-- Electrons - prototype, cloned by JavaScript -->
                <circle class="electron" id="electronProto" cx="0" cy="0" r="3.5"> <!-- Slightly larger electrons -->
                    <animateMotion dur="10s" repeatCount="indefinite">
                        <mpath xlink:href="#electronPath"/>
                    </animateMotion>
                </circle>
            </svg>
        </div>
        
        <div class="info-section">
            <p><strong>Current (I):</strong> <span id="currentValue">1.00</span> A</p>
            <p class="equation">I = V / R</p>
            <p><strong>Ohm's Law:</strong> "The current through a conductor between two points is directly proportional to the voltage across the two points and inversely proportional to the resistance between them."</p>
            <div class="dynamic-message" id="dynamicMessage">Adjust sliders to see changes.</div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const voltageSlider = document.getElementById('voltage');
            const resistanceSlider = document.getElementById('resistance');
            const voltageValueDisplay = document.getElementById('voltageValue');
            const resistanceValueDisplay = document.getElementById('resistanceValue');
            const currentValueDisplay = document.getElementById('currentValue');
            const dynamicMessageDisplay = document.getElementById('dynamicMessage');
            
            const electronProto = document.getElementById('electronProto');
            const circuitSVG = document.querySelector('.circuit-svg');
            
            let previousVoltage = parseFloat(voltageSlider.value);
            let previousResistance = parseFloat(resistanceSlider.value);
            let previousCurrent = 0; 

            const NUM_ELECTRONS = 12; 
            const electrons = [];

            function createElectrons() {
                electronProto.style.display = 'none'; 

                for (let i = 0; i < NUM_ELECTRONS; i++) {
                    const electron = electronProto.cloneNode(true);
                    electron.removeAttribute('id');
                    electron.style.display = 'block'; 
                    
                    const animateMotion = electron.querySelector('animateMotion');
                    if (animateMotion) {
                        // Stagger the start of electrons.
                        // Initial duration used for stagger calculation, will be updated.
                        const initialEstimatedDuration = 10; // seconds
                        const staggerDelay = (initialEstimatedDuration / NUM_ELECTRONS) * i;
                        animateMotion.setAttribute('begin', `${staggerDelay}s`);
                    }
                    circuitSVG.appendChild(electron);
                    electrons.push(electron);
                }
            }

            function updateSimulation() {
                const voltage = parseFloat(voltageSlider.value);
                const resistance = parseFloat(resistanceSlider.value);

                voltageValueDisplay.textContent = voltage.toFixed(1);
                resistanceValueDisplay.textContent = resistance.toFixed(1);

                let current = 0;
                // Resistance slider min is 1, so R is always > 0, preventing division by zero.
                current = voltage / resistance;
                currentValueDisplay.textContent = current.toFixed(2);

                let message = "";
                
                if (voltage !== previousVoltage && resistance === previousResistance) {
                    if (voltage > previousVoltage) {
                        message = "Increased voltage leads to increased current.";
                    } else {
                        message = "Decreased voltage leads to decreased current.";
                    }
                } else if (resistance !== previousResistance && voltage === previousVoltage) {
                    if (resistance > previousResistance) {
                        message = "Increased resistance leads to decreased current.";
                    } else {
                        message = "Decreased resistance leads to increased current.";
                    }
                } else if (voltage !== previousVoltage && resistance !== previousResistance) {
                    if (current > previousCurrent) {
                        message = "Current has increased due to changes in voltage and/or resistance.";
                    } else if (current < previousCurrent) {
                        message = "Current has decreased due to changes in voltage and/or resistance.";
                    } else if (current.toFixed(2) === previousCurrent.toFixed(2)) { // Compare formatted values
                         message = "Voltage and resistance changed, but current remains effectively the same.";
                    }
                }

                if (current === 0 && voltage === 0) {
                    message = "No voltage, so no current flows.";
                } else if (message === "" && (voltage !== previousVoltage || resistance !== previousResistance)) {
                     if (current > previousCurrent) message = "Current has increased.";
                     else if (current < previousCurrent) message = "Current has decreased.";
                }
                
                if (message === "" && current.toFixed(2) === previousCurrent.toFixed(2) && voltage === previousVoltage && resistance === previousResistance) {
                     message = "Adjust sliders to observe changes in current.";
                } else if (message === "") { 
                     message = "Current is " + current.toFixed(2) + " A. Adjust sliders for more observations.";
                }

                dynamicMessageDisplay.textContent = message;

                const baseAnimationDuration = 30; 
                let animationDuration;

                if (current > 0.001) { 
                    animationDuration = baseAnimationDuration / current;
                    animationDuration = Math.max(0.2, Math.min(animationDuration, 300)); 
                } else {
                    animationDuration = 300000; 
                }
                
                electrons.forEach((electron, index) => {
                    const animateMotion = electron.querySelector('animateMotion');
                    if (animateMotion) {
                        animateMotion.setAttribute('dur', animationDuration + 's');
                        const newStaggerDelay = (animationDuration / NUM_ELECTRONS) * index;
                        animateMotion.setAttribute('begin', `${newStaggerDelay}s`);

                        // Forcing SMIL to restart/update smoothly can be tricky.
                        // Forcing a reflow or re-adding the element is a heavy-handed approach.
                        // Modern browsers are generally good at picking up 'dur' and 'begin' changes.
                        // If animations don't update smoothly, one might need to:
                        // 1. electron.pauseAnimations(); (if API available)
                        // 2. Set attributes
                        // 3. electron.unpauseAnimations(); OR electron.setCurrentTime(0);
                        // SMIL standard doesn't offer robust JS control like CSS Animations or Web Animations API.
                        // The remove/add child trick is a common workaround for older browsers or complex cases.
                        // For this scenario, simply updating 'dur' and 'begin' should suffice for most modern browsers.
                    }
                });

                if (current <= 0.001) {
                    electrons.forEach(e => e.style.visibility = 'hidden');
                } else {
                    electrons.forEach(e => e.style.visibility = 'visible');
                }

                previousVoltage = voltage;
                previousResistance = resistance;
                previousCurrent = current;
            }

            voltageSlider.addEventListener('input', updateSimulation);
            resistanceSlider.addEventListener('input', updateSimulation);

            createElectrons();
            updateSimulation(); 
        });
    </script>
</body>
</html>
