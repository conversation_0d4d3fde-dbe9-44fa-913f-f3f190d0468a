<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Creation Studio - Physics Virtual Lab</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/components.css">
    <style>
        body {
            font-family: var(--font-family-sans);
            margin: 0;
            padding: 0;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .studio-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: var(--spacing-xl) 0;
            text-align: center;
        }

        .studio-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: var(--spacing-sm);
        }

        .studio-subtitle {
            font-size: 1.125rem;
            opacity: 0.9;
        }

        .main-content {
            flex: 1;
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--spacing-xl);
            width: 100%;
        }

        .studio-tabs {
            display: flex;
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            margin-bottom: var(--spacing-xl);
            overflow: hidden;
        }

        [data-theme="dark"] .studio-tabs {
            background: var(--gray-700);
        }

        .tab-button {
            flex: 1;
            padding: var(--spacing-lg);
            border: none;
            background: transparent;
            color: var(--text-secondary);
            font-weight: 600;
            cursor: pointer;
            transition: all var(--transition-fast);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
        }

        .tab-button.active {
            background: var(--primary-600);
            color: white;
        }

        .tab-button:hover:not(.active) {
            background: var(--primary-50);
            color: var(--primary-600);
        }

        [data-theme="dark"] .tab-button:hover:not(.active) {
            background: var(--primary-900);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .creation-form {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            padding: var(--spacing-xl);
        }

        [data-theme="dark"] .creation-form {
            background: var(--gray-700);
        }

        .form-section {
            margin-bottom: var(--spacing-xl);
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .form-group {
            margin-bottom: var(--spacing-lg);
        }

        .form-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-input,
        .form-select,
        .form-textarea {
            width: 100%;
            padding: var(--spacing-md);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            font-family: var(--font-family-sans);
            background: var(--bg-primary);
            color: var(--text-primary);
            transition: all var(--transition-fast);
        }

        .form-input:focus,
        .form-select:focus,
        .form-textarea:focus {
            outline: none;
            border-color: var(--primary-500);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-textarea {
            min-height: 120px;
            resize: vertical;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-lg);
        }

        .upload-area {
            border: 2px dashed var(--border-color);
            border-radius: var(--radius-lg);
            padding: var(--spacing-2xl);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-fast);
            background: var(--bg-secondary);
        }

        .upload-area:hover {
            border-color: var(--primary-500);
            background: var(--primary-50);
        }

        [data-theme="dark"] .upload-area:hover {
            background: var(--primary-900);
        }

        .upload-icon {
            font-size: 3rem;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-md);
        }

        .quiz-builder {
            background: var(--accent-50);
            border: 1px solid var(--accent-200);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
        }

        [data-theme="dark"] .quiz-builder {
            background: var(--accent-900);
            border-color: var(--accent-700);
        }

        .quiz-question {
            background: white;
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-md);
            border: 1px solid var(--border-color);
        }

        [data-theme="dark"] .quiz-question {
            background: var(--gray-800);
        }

        .question-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-md);
        }

        .question-number {
            font-weight: 600;
            color: var(--accent-600);
        }

        .remove-question {
            background: var(--advanced-color);
            color: white;
            border: none;
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            cursor: pointer;
            font-size: 0.75rem;
        }

        .option-input {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-sm);
        }

        .option-input input[type="radio"] {
            accent-color: var(--accent-600);
        }

        .option-input input[type="text"] {
            flex: 1;
        }

        .add-button {
            background: var(--accent-600);
            color: white;
            border: none;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-md);
            cursor: pointer;
            font-weight: 500;
            transition: all var(--transition-fast);
        }

        .add-button:hover {
            background: var(--accent-700);
        }

        .preview-section {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            padding: var(--spacing-xl);
        }

        [data-theme="dark"] .preview-section {
            background: var(--gray-700);
        }

        .video-preview {
            width: 100%;
            max-width: 600px;
            margin: 0 auto var(--spacing-lg) auto;
            background: var(--bg-secondary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            text-align: center;
        }

        .preview-placeholder {
            width: 100%;
            height: 300px;
            background: linear-gradient(135deg, var(--primary-400), var(--secondary-400));
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-bottom: var(--spacing-md);
        }

        .action-buttons {
            display: flex;
            gap: var(--spacing-md);
            justify-content: center;
            margin-top: var(--spacing-xl);
        }

        .btn {
            padding: var(--spacing-md) var(--spacing-xl);
            border: none;
            border-radius: var(--radius-lg);
            font-weight: 600;
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .btn-primary {
            background: var(--primary-600);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-700);
        }

        .btn-secondary {
            background: var(--gray-200);
            color: var(--text-primary);
        }

        [data-theme="dark"] .btn-secondary {
            background: var(--gray-600);
        }

        .btn-secondary:hover {
            background: var(--gray-300);
        }

        [data-theme="dark"] .btn-secondary:hover {
            background: var(--gray-500);
        }

        .templates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-lg);
        }

        .template-card {
            background: white;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            overflow: hidden;
            transition: all var(--transition-normal);
            cursor: pointer;
        }

        [data-theme="dark"] .template-card {
            background: var(--gray-700);
        }

        .template-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }

        .template-thumbnail {
            height: 150px;
            background: linear-gradient(135deg, var(--primary-400), var(--secondary-400));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
        }

        .template-info {
            padding: var(--spacing-lg);
        }

        .template-title {
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
            color: var(--text-primary);
        }

        .template-description {
            color: var(--text-secondary);
            font-size: 0.875rem;
            margin-bottom: var(--spacing-md);
        }

        .template-tags {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-xs);
        }

        .template-tag {
            background: var(--primary-100);
            color: var(--primary-700);
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-size: 0.75rem;
        }

        [data-theme="dark"] .template-tag {
            background: var(--primary-900);
            color: var(--primary-300);
        }

        @media (max-width: 768px) {
            .studio-tabs {
                flex-direction: column;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <header id="main-header"></header>

    <div class="studio-header animate-fade-in">
        <h1 class="studio-title">🎬 Video Creation Studio</h1>
        <p class="studio-subtitle">Create engaging physics videos with interactive elements</p>
    </div>

    <div class="main-content">
        <div class="studio-tabs animate-slide-up">
            <button class="tab-button active" data-tab="create">
                <span>🎥</span> Create Video
            </button>
            <button class="tab-button" data-tab="templates">
                <span>📋</span> Templates
            </button>
            <button class="tab-button" data-tab="preview">
                <span>👁️</span> Preview
            </button>
            <button class="tab-button" data-tab="publish">
                <span>🚀</span> Publish
            </button>
        </div>

        <!-- Create Video Tab -->
        <div class="tab-content active" id="create-tab">
            <div class="creation-form animate-slide-up">
                <div class="form-section">
                    <h2 class="section-title">📝 Basic Information</h2>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Video Title</label>
                            <input type="text" class="form-input" id="videoTitle" placeholder="Enter video title...">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Duration</label>
                            <input type="text" class="form-input" id="videoDuration" placeholder="e.g., 8:45">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Description</label>
                        <textarea class="form-textarea" id="videoDescription" placeholder="Describe what students will learn..."></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Category</label>
                            <select class="form-select" id="videoCategory">
                                <option value="mechanics">Mechanics</option>
                                <option value="electromagnetism">Electromagnetism</option>
                                <option value="waves">Waves & Oscillations</option>
                                <option value="thermodynamics">Thermodynamics</option>
                                <option value="modern">Modern Physics</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Difficulty Level</label>
                            <select class="form-select" id="videoDifficulty">
                                <option value="beginner">Beginner</option>
                                <option value="intermediate">Intermediate</option>
                                <option value="advanced">Advanced</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h2 class="section-title">🎬 Video Content</h2>
                    <div class="form-group">
                        <label class="form-label">Video URL (YouTube, Vimeo, etc.)</label>
                        <input type="url" class="form-input" id="videoUrl" placeholder="https://www.youtube.com/watch?v=...">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Or Upload Video File</label>
                        <div class="upload-area" onclick="document.getElementById('videoFile').click()">
                            <div class="upload-icon">📹</div>
                            <div>Click to upload video file</div>
                            <div style="font-size: 0.875rem; color: var(--text-secondary); margin-top: 0.5rem;">
                                Supports MP4, WebM, AVI (Max 500MB)
                            </div>
                        </div>
                        <input type="file" id="videoFile" accept="video/*" style="display: none;">
                    </div>
                </div>

                <div class="form-section">
                    <h2 class="section-title">🧠 Interactive Quiz</h2>
                    <div class="quiz-builder">
                        <div id="quizQuestions">
                            <!-- Quiz questions will be added here -->
                        </div>
                        <button class="add-button" onclick="addQuizQuestion()">+ Add Question</button>
                    </div>
                </div>

                <div class="form-section">
                    <h2 class="section-title">💡 Key Concepts</h2>
                    <div class="form-group">
                        <label class="form-label">Learning Objectives</label>
                        <textarea class="form-textarea" id="learningObjectives" placeholder="List the key learning objectives (one per line)..."></textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Key Concepts</label>
                        <input type="text" class="form-input" id="keyConcepts" placeholder="Enter key concepts separated by commas...">
                    </div>
                </div>
            </div>
        </div>

        <!-- Templates Tab -->
        <div class="tab-content" id="templates-tab">
            <div class="templates-grid animate-slide-up">
                <div class="template-card" onclick="loadTemplate('physics-intro')">
                    <div class="template-thumbnail">🔬</div>
                    <div class="template-info">
                        <h3 class="template-title">Physics Introduction</h3>
                        <p class="template-description">Perfect for introducing new physics concepts with clear explanations and examples.</p>
                        <div class="template-tags">
                            <span class="template-tag">Beginner</span>
                            <span class="template-tag">Introduction</span>
                        </div>
                    </div>
                </div>

                <div class="template-card" onclick="loadTemplate('problem-solving')">
                    <div class="template-thumbnail">🧮</div>
                    <div class="template-info">
                        <h3 class="template-title">Problem Solving</h3>
                        <p class="template-description">Step-by-step problem solving with interactive checkpoints and explanations.</p>
                        <div class="template-tags">
                            <span class="template-tag">Intermediate</span>
                            <span class="template-tag">Problem Solving</span>
                        </div>
                    </div>
                </div>

                <div class="template-card" onclick="loadTemplate('experiment-demo')">
                    <div class="template-thumbnail">⚗️</div>
                    <div class="template-info">
                        <h3 class="template-title">Experiment Demonstration</h3>
                        <p class="template-description">Showcase virtual experiments with interactive elements and data analysis.</p>
                        <div class="template-tags">
                            <span class="template-tag">All Levels</span>
                            <span class="template-tag">Practical</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Preview Tab -->
        <div class="tab-content" id="preview-tab">
            <div class="preview-section animate-slide-up">
                <h2 class="section-title">👁️ Video Preview</h2>
                <div class="video-preview">
                    <div class="preview-placeholder" id="previewPlaceholder">
                        Video Preview
                    </div>
                    <h3 id="previewTitle">Your Video Title</h3>
                    <p id="previewDescription">Your video description will appear here...</p>
                </div>
            </div>
        </div>

        <!-- Publish Tab -->
        <div class="tab-content" id="publish-tab">
            <div class="preview-section animate-slide-up">
                <h2 class="section-title">🚀 Publish Video</h2>
                <div style="text-align: center;">
                    <div style="font-size: 3rem; margin-bottom: 1rem;">✅</div>
                    <h3>Ready to Publish!</h3>
                    <p>Your interactive video is ready to be shared with students.</p>
                    <div class="action-buttons">
                        <button class="btn btn-secondary">Save as Draft</button>
                        <button class="btn btn-primary">Publish Video</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer id="main-footer-container"></footer>

    <script type="module" src="js/main.js"></script>
    <script>
        // Video Studio Controller
        let questionCount = 0;

        // Tab switching
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', () => {
                const tabId = button.dataset.tab;
                switchTab(tabId, button);
            });
        });

        function switchTab(tabId, button) {
            // Update active tab button
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');

            // Update active tab content
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            document.getElementById(`${tabId}-tab`).classList.add('active');

            // Update preview when switching to preview tab
            if (tabId === 'preview') {
                updatePreview();
            }
        }

        function addQuizQuestion() {
            questionCount++;
            const questionsContainer = document.getElementById('quizQuestions');
            
            const questionHTML = `
                <div class="quiz-question" id="question-${questionCount}">
                    <div class="question-header">
                        <span class="question-number">Question ${questionCount}</span>
                        <button class="remove-question" onclick="removeQuestion(${questionCount})">Remove</button>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Question Text</label>
                        <input type="text" class="form-input" placeholder="Enter your question...">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Trigger Time (seconds)</label>
                        <input type="number" class="form-input" placeholder="e.g., 120" min="0">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Answer Options</label>
                        <div class="option-input">
                            <input type="radio" name="correct-${questionCount}" value="0">
                            <input type="text" class="form-input" placeholder="Option A">
                        </div>
                        <div class="option-input">
                            <input type="radio" name="correct-${questionCount}" value="1">
                            <input type="text" class="form-input" placeholder="Option B">
                        </div>
                        <div class="option-input">
                            <input type="radio" name="correct-${questionCount}" value="2">
                            <input type="text" class="form-input" placeholder="Option C">
                        </div>
                        <div class="option-input">
                            <input type="radio" name="correct-${questionCount}" value="3">
                            <input type="text" class="form-input" placeholder="Option D">
                        </div>
                    </div>
                </div>
            `;
            
            questionsContainer.insertAdjacentHTML('beforeend', questionHTML);
        }

        function removeQuestion(questionId) {
            const question = document.getElementById(`question-${questionId}`);
            if (question) {
                question.remove();
            }
        }

        function updatePreview() {
            const title = document.getElementById('videoTitle').value || 'Your Video Title';
            const description = document.getElementById('videoDescription').value || 'Your video description will appear here...';
            
            document.getElementById('previewTitle').textContent = title;
            document.getElementById('previewDescription').textContent = description;
        }

        function loadTemplate(templateId) {
            // Load template data based on templateId
            const templates = {
                'physics-intro': {
                    title: 'Introduction to [Physics Concept]',
                    description: 'This video introduces students to fundamental concepts in physics with clear explanations and visual demonstrations.',
                    category: 'mechanics',
                    difficulty: 'beginner',
                    objectives: 'Understand basic principles\nIdentify key concepts\nApply knowledge to simple problems',
                    concepts: 'Force, Motion, Energy, Momentum'
                },
                'problem-solving': {
                    title: 'Solving [Physics Problem Type]',
                    description: 'Step-by-step approach to solving physics problems with interactive checkpoints.',
                    category: 'mechanics',
                    difficulty: 'intermediate',
                    objectives: 'Identify problem type\nApply appropriate formulas\nCheck solution validity',
                    concepts: 'Problem Analysis, Mathematical Methods, Solution Verification'
                },
                'experiment-demo': {
                    title: '[Experiment Name] Virtual Lab',
                    description: 'Virtual demonstration of physics experiments with data collection and analysis.',
                    category: 'mechanics',
                    difficulty: 'intermediate',
                    objectives: 'Understand experimental setup\nCollect and analyze data\nDraw scientific conclusions',
                    concepts: 'Experimental Design, Data Analysis, Scientific Method'
                }
            };

            const template = templates[templateId];
            if (template) {
                document.getElementById('videoTitle').value = template.title;
                document.getElementById('videoDescription').value = template.description;
                document.getElementById('videoCategory').value = template.category;
                document.getElementById('videoDifficulty').value = template.difficulty;
                document.getElementById('learningObjectives').value = template.objectives;
                document.getElementById('keyConcepts').value = template.concepts;

                // Switch to create tab
                switchTab('create', document.querySelector('[data-tab="create"]'));
                
                // Show success message
                if (window.app && window.app.showNotification) {
                    window.app.showNotification('Template loaded successfully! 🎉', 'success');
                }
            }
        }

        // Auto-save functionality
        setInterval(() => {
            const formData = {
                title: document.getElementById('videoTitle').value,
                description: document.getElementById('videoDescription').value,
                category: document.getElementById('videoCategory').value,
                difficulty: document.getElementById('videoDifficulty').value,
                url: document.getElementById('videoUrl').value,
                objectives: document.getElementById('learningObjectives').value,
                concepts: document.getElementById('keyConcepts').value
            };
            
            localStorage.setItem('video-studio-draft', JSON.stringify(formData));
        }, 30000); // Auto-save every 30 seconds

        // Load draft on page load
        document.addEventListener('DOMContentLoaded', () => {
            const draft = localStorage.getItem('video-studio-draft');
            if (draft) {
                const data = JSON.parse(draft);
                document.getElementById('videoTitle').value = data.title || '';
                document.getElementById('videoDescription').value = data.description || '';
                document.getElementById('videoCategory').value = data.category || 'mechanics';
                document.getElementById('videoDifficulty').value = data.difficulty || 'beginner';
                document.getElementById('videoUrl').value = data.url || '';
                document.getElementById('learningObjectives').value = data.objectives || '';
                document.getElementById('keyConcepts').value = data.concepts || '';
            }
        });
    </script>
</body>
</html>
