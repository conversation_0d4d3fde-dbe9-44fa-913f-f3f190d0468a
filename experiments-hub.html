<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Experiments Hub - Physics Virtual Lab</title>
    <meta name="author" content="<PERSON><PERSON>, SUST-BME">
    <meta name="copyright" content="Dr. <PERSON> E<PERSON>il - +249912867327, +966538076790">
    <meta name="institution" content="Sudan University of Science and Technology - Biomedical Engineering">
    <meta name="description" content="Comprehensive hub for all Physics Virtual Lab experiments and simulations">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/components.css">
    <style>
        body {
            font-family: var(--font-family-sans);
            margin: 0;
            padding: 0;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .experiments-header {
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
            color: white;
            padding: var(--spacing-3xl) 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .experiments-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.1);
            z-index: 1;
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        .experiments-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: var(--spacing-md);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .experiments-subtitle {
            font-size: 1.25rem;
            opacity: 0.9;
            max-width: 700px;
            margin: 0 auto;
        }

        .floating-icons {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .floating-icon {
            position: absolute;
            font-size: 2rem;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }

        .floating-icon:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
        .floating-icon:nth-child(2) { top: 20%; right: 15%; animation-delay: 1s; }
        .floating-icon:nth-child(3) { bottom: 30%; left: 20%; animation-delay: 2s; }
        .floating-icon:nth-child(4) { bottom: 20%; right: 10%; animation-delay: 3s; }

        .main-content {
            flex: 1;
            max-width: 1400px;
            margin: 0 auto;
            padding: var(--spacing-xl);
            width: 100%;
        }

        .category-section {
            margin-bottom: var(--spacing-3xl);
        }

        .category-header {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
        }

        [data-theme="dark"] .category-header {
            background: var(--gray-700);
        }

        .category-icon {
            font-size: 3rem;
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--radius-xl);
            background: linear-gradient(135deg, var(--primary-400), var(--secondary-400));
            color: white;
        }

        .category-info h2 {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin: 0 0 var(--spacing-sm) 0;
        }

        .category-description {
            color: var(--text-secondary);
            font-size: 1.125rem;
            margin: 0;
        }

        .experiments-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: var(--spacing-xl);
        }

        .experiment-card {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            transition: all var(--transition-normal);
            position: relative;
        }

        [data-theme="dark"] .experiment-card {
            background: var(--gray-700);
        }

        .experiment-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-xl);
        }

        .experiment-thumbnail {
            height: 200px;
            background: linear-gradient(135deg, var(--primary-400), var(--primary-600));
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .experiment-thumbnail.electromagnetism {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        }

        .experiment-thumbnail.mechanics {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
        }

        .experiment-thumbnail.thermodynamics {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }

        .experiment-thumbnail.general {
            background: linear-gradient(135deg, #10b981, #059669);
        }

        .experiment-icon {
            font-size: 4rem;
            color: white;
            opacity: 0.9;
        }

        .difficulty-badge {
            position: absolute;
            top: var(--spacing-md);
            right: var(--spacing-md);
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-md);
            font-size: 0.75rem;
            font-weight: 600;
            color: white;
        }

        .difficulty-beginner {
            background: var(--beginner-color);
        }

        .difficulty-intermediate {
            background: var(--intermediate-color);
        }

        .difficulty-advanced {
            background: var(--advanced-color);
        }

        .experiment-content {
            padding: var(--spacing-lg);
        }

        .experiment-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
        }

        .experiment-description {
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: var(--spacing-md);
        }

        .experiment-meta {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
        }

        .meta-tag {
            padding: var(--spacing-xs) var(--spacing-sm);
            background: var(--accent-100);
            color: var(--accent-700);
            border-radius: var(--radius-sm);
            font-size: 0.75rem;
            font-weight: 500;
        }

        [data-theme="dark"] .meta-tag {
            background: var(--accent-900);
            color: var(--accent-300);
        }

        .experiment-actions {
            display: flex;
            gap: var(--spacing-sm);
        }

        .action-btn {
            flex: 1;
            padding: var(--spacing-sm) var(--spacing-md);
            border: none;
            border-radius: var(--radius-md);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-fast);
            text-decoration: none;
            text-align: center;
            font-size: 0.875rem;
        }

        .btn-primary {
            background: var(--primary-600);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-700);
        }

        .btn-secondary {
            background: var(--gray-200);
            color: var(--text-primary);
        }

        [data-theme="dark"] .btn-secondary {
            background: var(--gray-600);
        }

        .btn-secondary:hover {
            background: var(--gray-300);
        }

        [data-theme="dark"] .btn-secondary:hover {
            background: var(--gray-500);
        }

        .stats-section {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            padding: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-lg);
        }

        [data-theme="dark"] .stats-section {
            background: var(--gray-700);
        }

        .stat-item {
            text-align: center;
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: var(--spacing-sm);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-600);
            margin-bottom: var(--spacing-xs);
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        @media (max-width: 768px) {
            .experiments-grid {
                grid-template-columns: 1fr;
            }
            
            .category-header {
                flex-direction: column;
                text-align: center;
            }
            
            .stats-section {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <header id="main-header"></header>

    <div class="experiments-header animate-fade-in">
        <div class="floating-icons">
            <div class="floating-icon">⚡</div>
            <div class="floating-icon">🔬</div>
            <div class="floating-icon">🌡️</div>
            <div class="floating-icon">🧪</div>
        </div>
        <div class="header-content">
            <h1 class="experiments-title">🧪 Physics Experiments Hub</h1>
            <p class="experiments-subtitle">
                Explore the fascinating world of physics through interactive simulations and virtual experiments
            </p>
        </div>
    </div>

    <div class="main-content">
        <div class="stats-section animate-slide-up">
            <div class="stat-item">
                <div class="stat-icon">🧪</div>
                <div class="stat-value">15</div>
                <div class="stat-label">Total Experiments</div>
            </div>
            <div class="stat-item">
                <div class="stat-icon">📚</div>
                <div class="stat-value">4</div>
                <div class="stat-label">Physics Categories</div>
            </div>
            <div class="stat-item">
                <div class="stat-icon">🎯</div>
                <div class="stat-value">3</div>
                <div class="stat-label">Difficulty Levels</div>
            </div>
            <div class="stat-item">
                <div class="stat-icon">⏱️</div>
                <div class="stat-value">45</div>
                <div class="stat-label">Minutes of Content</div>
            </div>
        </div>

        <!-- Mechanics Section -->
        <div class="category-section animate-slide-up animate-stagger-1">
            <div class="category-header">
                <div class="category-icon">🔬</div>
                <div class="category-info">
                    <h2>Mechanics & Waves</h2>
                    <p class="category-description">Study motion, forces, oscillations, and wave phenomena</p>
                </div>
            </div>
            <div class="experiments-grid">
                <div class="experiment-card">
                    <div class="experiment-thumbnail mechanics">
                        <div class="experiment-icon">⚖️</div>
                        <div class="difficulty-badge difficulty-beginner">Beginner</div>
                    </div>
                    <div class="experiment-content">
                        <h3 class="experiment-title">Pendulum Laboratory</h3>
                        <p class="experiment-description">
                            Explore simple harmonic motion and investigate how pendulum length affects the period of oscillation.
                        </p>
                        <div class="experiment-meta">
                            <span class="meta-tag">SHM</span>
                            <span class="meta-tag">Period</span>
                            <span class="meta-tag">20 min</span>
                        </div>
                        <div class="experiment-actions">
                            <a href="experiments/pendulum-lab.html" class="action-btn btn-primary">Start Experiment</a>
                            <a href="video-library.html?topic=pendulum" class="action-btn btn-secondary">Watch Video</a>
                        </div>
                    </div>
                </div>

                <div class="experiment-card">
                    <div class="experiment-thumbnail mechanics">
                        <div class="experiment-icon">🚀</div>
                        <div class="difficulty-badge difficulty-intermediate">Intermediate</div>
                    </div>
                    <div class="experiment-content">
                        <h3 class="experiment-title">Projectile Motion</h3>
                        <p class="experiment-description">
                            Analyze projectile motion with varying angles and velocities to understand trajectory physics.
                        </p>
                        <div class="experiment-meta">
                            <span class="meta-tag">Kinematics</span>
                            <span class="meta-tag">Trajectory</span>
                            <span class="meta-tag">25 min</span>
                        </div>
                        <div class="experiment-actions">
                            <a href="experiments/projectile-motion.html" class="action-btn btn-primary">Start Experiment</a>
                            <a href="video-library.html?topic=projectile" class="action-btn btn-secondary">Watch Video</a>
                        </div>
                    </div>
                </div>

                <div class="experiment-card">
                    <div class="experiment-thumbnail mechanics">
                        <div class="experiment-icon">🌊</div>
                        <div class="difficulty-badge difficulty-intermediate">Intermediate</div>
                    </div>
                    <div class="experiment-content">
                        <h3 class="experiment-title">Wave Mechanics</h3>
                        <p class="experiment-description">
                            Study wave properties, interference patterns, and different types of wave motion.
                        </p>
                        <div class="experiment-meta">
                            <span class="meta-tag">Waves</span>
                            <span class="meta-tag">Interference</span>
                            <span class="meta-tag">30 min</span>
                        </div>
                        <div class="experiment-actions">
                            <a href="experiments/wave-mechanics.html" class="action-btn btn-primary">Start Experiment</a>
                            <a href="video-library.html?topic=waves" class="action-btn btn-secondary">Watch Video</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Electromagnetism Section -->
        <div class="category-section animate-slide-up animate-stagger-2">
            <div class="category-header">
                <div class="category-icon">⚡</div>
                <div class="category-info">
                    <h2>Electromagnetism</h2>
                    <p class="category-description">Explore electrical circuits, current flow, and electromagnetic phenomena</p>
                </div>
            </div>
            <div class="experiments-grid">
                <div class="experiment-card">
                    <div class="experiment-thumbnail electromagnetism">
                        <div class="experiment-icon">🔌</div>
                        <div class="difficulty-badge difficulty-beginner">Beginner</div>
                    </div>
                    <div class="experiment-content">
                        <h3 class="experiment-title">Ohm's Law Explorer</h3>
                        <p class="experiment-description">
                            Investigate the fundamental relationship between voltage, current, and resistance through interactive controls.
                        </p>
                        <div class="experiment-meta">
                            <span class="meta-tag">V = IR</span>
                            <span class="meta-tag">Interactive</span>
                            <span class="meta-tag">15 min</span>
                        </div>
                        <div class="experiment-actions">
                            <a href="experiments/ohms-law.html" class="action-btn btn-primary">Start Experiment</a>
                            <a href="video-library.html?topic=ohms-law" class="action-btn btn-secondary">Watch Video</a>
                        </div>
                    </div>
                </div>

                <div class="experiment-card">
                    <div class="experiment-thumbnail electromagnetism">
                        <div class="experiment-icon">⚡</div>
                        <div class="difficulty-badge difficulty-intermediate">Intermediate</div>
                    </div>
                    <div class="experiment-content">
                        <h3 class="experiment-title">Circuit Simulator</h3>
                        <p class="experiment-description">
                            Build and analyze complex electrical circuits with various components and measurement tools.
                        </p>
                        <div class="experiment-meta">
                            <span class="meta-tag">Circuit Analysis</span>
                            <span class="meta-tag">Simulation</span>
                            <span class="meta-tag">20 min</span>
                        </div>
                        <div class="experiment-actions">
                            <a href="experiments/circuit-simulator.html" class="action-btn btn-primary">Start Experiment</a>
                            <a href="video-library.html?topic=circuits" class="action-btn btn-secondary">Watch Video</a>
                        </div>
                    </div>
                </div>

                <div class="experiment-card">
                    <div class="experiment-thumbnail electromagnetism">
                        <div class="experiment-icon">🔬</div>
                        <div class="difficulty-badge difficulty-advanced">Advanced</div>
                    </div>
                    <div class="experiment-content">
                        <h3 class="experiment-title">Advanced Circuit Lab</h3>
                        <p class="experiment-description">
                            Advanced circuit analysis with complex components and comprehensive measurement capabilities.
                        </p>
                        <div class="experiment-meta">
                            <span class="meta-tag">Advanced</span>
                            <span class="meta-tag">Comprehensive</span>
                            <span class="meta-tag">25 min</span>
                        </div>
                        <div class="experiment-actions">
                            <a href="Ohm's Law Circuit Simulator.html" class="action-btn btn-primary">Start Experiment</a>
                            <a href="video-library.html?topic=advanced-circuits" class="action-btn btn-secondary">Watch Video</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Thermodynamics Section -->
        <div class="category-section animate-slide-up animate-stagger-3">
            <div class="category-header">
                <div class="category-icon">🌡️</div>
                <div class="category-info">
                    <h2>Thermodynamics</h2>
                    <p class="category-description">Explore heat transfer, gas laws, and thermal properties</p>
                </div>
            </div>
            <div class="experiments-grid">
                <div class="experiment-card">
                    <div class="experiment-thumbnail thermodynamics">
                        <div class="experiment-icon">🔥</div>
                        <div class="difficulty-badge difficulty-intermediate">Intermediate</div>
                    </div>
                    <div class="experiment-content">
                        <h3 class="experiment-title">Heat Transfer Laboratory</h3>
                        <p class="experiment-description">
                            Study conduction, convection, and radiation heat transfer mechanisms with interactive simulations.
                        </p>
                        <div class="experiment-meta">
                            <span class="meta-tag">Heat Transfer</span>
                            <span class="meta-tag">Thermal</span>
                            <span class="meta-tag">25 min</span>
                        </div>
                        <div class="experiment-actions">
                            <a href="experiments/heat-transfer.html" class="action-btn btn-primary">Start Experiment</a>
                            <a href="video-library.html?topic=thermodynamics" class="action-btn btn-secondary">Watch Video</a>
                        </div>
                    </div>
                </div>

                <div class="experiment-card">
                    <div class="experiment-thumbnail thermodynamics">
                        <div class="experiment-icon">🌡️</div>
                        <div class="difficulty-badge difficulty-beginner">Beginner</div>
                    </div>
                    <div class="experiment-content">
                        <h3 class="experiment-title">Boyle's Law Simulation</h3>
                        <p class="experiment-description">
                            Explore the relationship between pressure and volume in gases through interactive simulations.
                        </p>
                        <div class="experiment-meta">
                            <span class="meta-tag">Gas Laws</span>
                            <span class="meta-tag">PV = constant</span>
                            <span class="meta-tag">15 min</span>
                        </div>
                        <div class="experiment-actions">
                            <a href="Boyle's Law Simulation.html" class="action-btn btn-primary">Start Experiment</a>
                            <a href="video-library.html?topic=gas-laws" class="action-btn btn-secondary">Watch Video</a>
                        </div>
                    </div>
                </div>

                <div class="experiment-card">
                    <div class="experiment-thumbnail thermodynamics">
                        <div class="experiment-icon">📊</div>
                        <div class="difficulty-badge difficulty-beginner">Beginner</div>
                    </div>
                    <div class="experiment-content">
                        <h3 class="experiment-title">Gas Laws Laboratory</h3>
                        <p class="experiment-description">
                            Investigate volume-pressure relationships and understand fundamental gas law principles.
                        </p>
                        <div class="experiment-meta">
                            <span class="meta-tag">Gas Behavior</span>
                            <span class="meta-tag">Analysis</span>
                            <span class="meta-tag">20 min</span>
                        </div>
                        <div class="experiment-actions">
                            <a href="volume, pressure Relationship.html" class="action-btn btn-primary">Start Experiment</a>
                            <a href="video-library.html?topic=gas-laws" class="action-btn btn-secondary">Watch Video</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Navigation -->
        <div style="text-align: center; margin-top: var(--spacing-3xl);">
            <a href="index.html" class="action-btn btn-secondary" style="display: inline-block; margin-right: var(--spacing-md);">← Back to Home</a>
            <a href="dashboard.html" class="action-btn btn-primary" style="display: inline-block;">View Dashboard →</a>
        </div>
    </div>

    <footer id="main-footer-container"></footer>

    <script type="module" src="js/main.js"></script>
</body>
</html>
