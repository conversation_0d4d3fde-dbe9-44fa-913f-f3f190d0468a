<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pendulum Laboratory - Physics Virtual Lab</title>
    <meta name="author" content="Dr<PERSON>, SUST-BME">
    <meta name="copyright" content="Dr. <PERSON>il - +249912867327, +966538076790">
    <meta name="institution" content="Sudan University of Science and Technology - Biomedical Engineering">
    <meta name="description" content="Interactive pendulum simulation for studying simple harmonic motion and period calculations">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/animations.css">
    <link rel="stylesheet" href="../css/components.css">
    <style>
        body {
            font-family: var(--font-family-sans);
            margin: 0;
            padding: 0;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .experiment-header {
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--border-color);
            padding: var(--spacing-md);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .experiment-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }
        
        .back-button {
            background: var(--primary-600);
            color: white;
            border: none;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-md);
            text-decoration: none;
            font-weight: 500;
            transition: all var(--transition-fast);
        }
        
        .back-button:hover {
            background: var(--primary-700);
            transform: translateY(-2px);
        }

        .main-content {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-lg);
            padding: var(--spacing-lg);
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
        }

        .left-panel, .right-panel {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
        }

        .panel-section {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            padding: var(--spacing-lg);
        }

        [data-theme="dark"] .panel-section {
            background: var(--gray-700);
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .pendulum-canvas {
            width: 100%;
            height: 300px;
            border: 2px solid var(--border-color);
            border-radius: var(--radius-lg);
            background: var(--bg-primary);
        }

        .control-group {
            margin-bottom: var(--spacing-md);
        }

        .control-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 500;
            color: var(--text-primary);
        }

        .control-slider {
            width: 100%;
            height: 8px;
            border-radius: 4px;
            background: var(--gray-200);
            outline: none;
            cursor: pointer;
            margin-bottom: var(--spacing-sm);
            accent-color: var(--primary-600);
        }

        [data-theme="dark"] .control-slider {
            background: var(--gray-600);
        }

        .control-value {
            font-weight: 600;
            color: var(--primary-600);
            text-align: center;
            padding: var(--spacing-sm);
            background: var(--bg-secondary);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
        }

        .control-buttons {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--spacing-md);
        }

        .btn {
            flex: 1;
            padding: var(--spacing-sm) var(--spacing-md);
            border: none;
            border-radius: var(--radius-md);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .btn-primary {
            background: var(--primary-600);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-700);
        }

        .btn-secondary {
            background: var(--gray-200);
            color: var(--text-primary);
        }

        [data-theme="dark"] .btn-secondary {
            background: var(--gray-600);
        }

        .btn-secondary:hover {
            background: var(--gray-300);
        }

        [data-theme="dark"] .btn-secondary:hover {
            background: var(--gray-500);
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: var(--spacing-md);
        }

        .data-table th,
        .data-table td {
            padding: var(--spacing-sm);
            text-align: center;
            border-bottom: 1px solid var(--border-color);
        }

        .data-table th {
            background: var(--bg-secondary);
            font-weight: 600;
            color: var(--text-primary);
        }

        .data-table td {
            color: var(--text-secondary);
        }

        .data-message {
            text-align: center;
            color: var(--text-secondary);
            font-style: italic;
            margin-top: var(--spacing-sm);
        }

        .graph-container {
            width: 100%;
            height: 250px;
            border: 2px solid var(--border-color);
            border-radius: var(--radius-lg);
            background: var(--bg-primary);
            position: relative;
        }

        .graph-canvas {
            width: 100%;
            height: 100%;
            border-radius: var(--radius-lg);
        }

        .formula-box {
            background: var(--accent-50);
            border: 2px solid var(--accent-200);
            border-radius: var(--radius-lg);
            padding: var(--spacing-md);
            text-align: center;
            margin-top: var(--spacing-md);
        }

        [data-theme="dark"] .formula-box {
            background: var(--accent-900);
            border-color: var(--accent-700);
        }

        .formula {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--accent-700);
            margin-bottom: var(--spacing-sm);
        }

        [data-theme="dark"] .formula {
            color: var(--accent-300);
        }

        .formula-description {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                padding: var(--spacing-md);
            }
            
            .control-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <header id="main-header"></header>

    <div class="experiment-header">
        <h1 class="experiment-title">⚖️ Pendulum Laboratory</h1>
        <div class="header-actions">
            <a href="../experiments-hub.html" class="back-button">← Experiments Hub</a>
            <a href="../video-library.html?topic=pendulum" class="back-button">📹 Watch Video</a>
            <a href="../dashboard.html" class="back-button">📊 Dashboard</a>
        </div>
    </div>

    <div class="main-content">
        <div class="left-panel">
            <div class="panel-section animate-fade-in">
                <h2 class="section-title">🎯 Pendulum Simulation</h2>
                <canvas id="pendulumCanvas" class="pendulum-canvas"></canvas>
                
                <div class="control-group">
                    <label for="lengthSlider" class="control-label">Pendulum Length:</label>
                    <input type="range" id="lengthSlider" class="control-slider" min="10" max="100" value="50">
                    <div class="control-value">
                        Length: <span id="lengthValue">50</span> cm
                    </div>
                </div>

                <div class="control-value">
                    Calculated Period: <span id="periodDisplay">1.42</span> s
                </div>

                <div class="control-buttons">
                    <button type="button" id="startStopButton" class="btn btn-primary">Start</button>
                    <button type="button" id="resetButton" class="btn btn-secondary">Reset</button>
                    <button type="button" id="collectDataButton" class="btn btn-secondary">Collect Data</button>
                </div>

                <div class="formula-box">
                    <div class="formula">T = 2π√(L/g)</div>
                    <div class="formula-description">
                        Where T = period, L = length, g = 9.81 m/s²
                    </div>
                </div>
            </div>
        </div>

        <div class="right-panel">
            <div class="panel-section animate-slide-up">
                <h2 class="section-title">📊 Data Collection</h2>
                <table id="dataTable" class="data-table">
                    <thead>
                        <tr>
                            <th>Length (cm)</th>
                            <th>Period (s)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Data rows will be added here -->
                    </tbody>
                </table>
                <div id="dataMessage" class="data-message">0 of 5 data points collected.</div>
            </div>

            <div class="panel-section animate-slide-up animate-stagger-1">
                <h2 class="section-title">📈 Length vs. Period Graph</h2>
                <div class="graph-container">
                    <canvas id="graphCanvas" class="graph-canvas"></canvas>
                </div>
            </div>
        </div>
    </div>

    <footer id="main-footer-container"></footer>

    <script type="module" src="../js/main.js"></script>
    <script>
        // Pendulum Laboratory JavaScript
        document.addEventListener('DOMContentLoaded', () => {
            // Constants
            const G = 9.81; // m/s^2
            const MIN_LENGTH_CM = 10;
            const MAX_LENGTH_CM = 100;
            const MAX_DATA_POINTS = 5;

            // DOM Elements
            const lengthSlider = document.getElementById('lengthSlider');
            const lengthValue = document.getElementById('lengthValue');
            const periodDisplay = document.getElementById('periodDisplay');
            const startStopButton = document.getElementById('startStopButton');
            const resetButton = document.getElementById('resetButton');
            const collectDataButton = document.getElementById('collectDataButton');
            const pendulumCanvas = document.getElementById('pendulumCanvas');
            const graphCanvas = document.getElementById('graphCanvas');
            const dataTable = document.getElementById('dataTable').getElementsByTagName('tbody')[0];
            const dataMessage = document.getElementById('dataMessage');

            // Simulation state
            let isRunning = false;
            let animationId = null;
            let angle = Math.PI / 6; // Initial angle
            let angularVelocity = 0;
            let dataPoints = [];

            // Canvas contexts
            const pendulumCtx = pendulumCanvas.getContext('2d');
            const graphCtx = graphCanvas.getContext('2d');

            // Initialize canvases
            function initCanvases() {
                pendulumCanvas.width = pendulumCanvas.offsetWidth;
                pendulumCanvas.height = pendulumCanvas.offsetHeight;
                graphCanvas.width = graphCanvas.offsetWidth;
                graphCanvas.height = graphCanvas.offsetHeight;
            }

            // Calculate period using the formula
            function calculatePeriod(lengthCm) {
                const lengthM = lengthCm / 100;
                return 2 * Math.PI * Math.sqrt(lengthM / G);
            }

            // Update displays
            function updateDisplays() {
                const length = parseInt(lengthSlider.value);
                const period = calculatePeriod(length);
                
                lengthValue.textContent = length;
                periodDisplay.textContent = period.toFixed(2);
            }

            // Draw pendulum
            function drawPendulum() {
                const ctx = pendulumCtx;
                const width = pendulumCanvas.width;
                const height = pendulumCanvas.height;
                const length = parseInt(lengthSlider.value);
                
                // Clear canvas
                ctx.clearRect(0, 0, width, height);
                
                // Calculate pendulum position
                const pivotX = width / 2;
                const pivotY = 50;
                const pendulumLength = (length / 100) * (height - 100);
                const bobX = pivotX + pendulumLength * Math.sin(angle);
                const bobY = pivotY + pendulumLength * Math.cos(angle);
                
                // Draw pivot point
                ctx.fillStyle = '#666';
                ctx.fillRect(pivotX - 5, pivotY - 5, 10, 10);
                
                // Draw string
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(pivotX, pivotY);
                ctx.lineTo(bobX, bobY);
                ctx.stroke();
                
                // Draw bob
                ctx.fillStyle = '#e74c3c';
                ctx.beginPath();
                ctx.arc(bobX, bobY, 15, 0, 2 * Math.PI);
                ctx.fill();
                
                // Draw length indicator
                ctx.fillStyle = '#666';
                ctx.font = '12px Arial';
                ctx.fillText(`${length} cm`, 10, 20);
            }

            // Animate pendulum
            function animate() {
                if (!isRunning) return;
                
                const length = parseInt(lengthSlider.value) / 100; // Convert to meters
                const angularAcceleration = -(G / length) * Math.sin(angle);
                
                angularVelocity += angularAcceleration * 0.016; // Assuming 60 FPS
                angle += angularVelocity * 0.016;
                
                // Apply damping
                angularVelocity *= 0.999;
                
                drawPendulum();
                animationId = requestAnimationFrame(animate);
            }

            // Start/stop simulation
            function toggleSimulation() {
                if (isRunning) {
                    isRunning = false;
                    cancelAnimationFrame(animationId);
                    startStopButton.textContent = 'Start';
                    startStopButton.classList.remove('btn-secondary');
                    startStopButton.classList.add('btn-primary');
                } else {
                    isRunning = true;
                    startStopButton.textContent = 'Stop';
                    startStopButton.classList.remove('btn-primary');
                    startStopButton.classList.add('btn-secondary');
                    animate();
                }
            }

            // Reset pendulum
            function resetPendulum() {
                isRunning = false;
                cancelAnimationFrame(animationId);
                angle = Math.PI / 6;
                angularVelocity = 0;
                startStopButton.textContent = 'Start';
                startStopButton.classList.remove('btn-secondary');
                startStopButton.classList.add('btn-primary');
                drawPendulum();
            }

            // Add data point
            function addDataPoint() {
                if (dataPoints.length >= MAX_DATA_POINTS) return;
                
                const length = parseInt(lengthSlider.value);
                const period = calculatePeriod(length);
                
                dataPoints.push({ length, period });
                updateDataTable();
                updateGraph();
            }

            // Update data table
            function updateDataTable() {
                dataTable.innerHTML = '';
                dataPoints.forEach(point => {
                    const row = dataTable.insertRow();
                    row.insertCell(0).textContent = point.length;
                    row.insertCell(1).textContent = point.period.toFixed(2);
                });
                
                dataMessage.textContent = `${dataPoints.length} of ${MAX_DATA_POINTS} data points collected.`;
            }

            // Update graph
            function updateGraph() {
                const ctx = graphCtx;
                const width = graphCanvas.width;
                const height = graphCanvas.height;
                
                // Clear canvas
                ctx.clearRect(0, 0, width, height);
                
                if (dataPoints.length === 0) return;
                
                // Draw axes
                ctx.strokeStyle = '#666';
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.moveTo(50, height - 50);
                ctx.lineTo(width - 20, height - 50);
                ctx.moveTo(50, height - 50);
                ctx.lineTo(50, 20);
                ctx.stroke();
                
                // Draw data points
                ctx.fillStyle = '#e74c3c';
                dataPoints.forEach(point => {
                    const x = 50 + ((point.length - MIN_LENGTH_CM) / (MAX_LENGTH_CM - MIN_LENGTH_CM)) * (width - 70);
                    const y = height - 50 - (point.period / 3) * (height - 70);
                    
                    ctx.beginPath();
                    ctx.arc(x, y, 4, 0, 2 * Math.PI);
                    ctx.fill();
                });
                
                // Draw labels
                ctx.fillStyle = '#666';
                ctx.font = '12px Arial';
                ctx.fillText('Length (cm)', width / 2 - 30, height - 10);
                ctx.save();
                ctx.translate(15, height / 2);
                ctx.rotate(-Math.PI / 2);
                ctx.fillText('Period (s)', 0, 0);
                ctx.restore();
            }

            // Event listeners
            lengthSlider.addEventListener('input', () => {
                updateDisplays();
                drawPendulum();
            });

            startStopButton.addEventListener('click', toggleSimulation);
            resetButton.addEventListener('click', resetPendulum);

            // Double-click to add data point
            pendulumCanvas.addEventListener('dblclick', addDataPoint);

            // Initialize
            initCanvases();
            updateDisplays();
            drawPendulum();
            updateGraph();

            // Handle window resize
            window.addEventListener('resize', () => {
                initCanvases();
                drawPendulum();
                updateGraph();
            });
        });
    </script>
</body>
</html>
