// Physics Virtual Lab - Dashboard Module

import { Utils } from './utils.js';

class Dashboard {
  constructor() {
    this.utils = new Utils();
    this.userData = this.loadUserData();
    this.experiments = this.loadExperiments();
    this.achievements = this.loadAchievements();
    
    this.init();
  }

  init() {
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.render());
    } else {
      this.render();
    }
  }

  loadUserData() {
    return this.utils.getLocalStorage('physics-lab-user', {
      name: 'Physics Student',
      level: 1,
      experience: 0,
      experimentsCompleted: 0,
      totalTime: 0,
      lastLogin: new Date().toISOString(),
      preferences: {
        units: 'metric',
        difficulty: 'intermediate',
        notifications: true
      }
    });
  }

  loadExperiments() {
    return this.utils.getLocalStorage('physics-lab-experiments', [
      {
        id: 'circuit-simulator',
        name: 'Circuit Simulator',
        description: 'Build and analyze electrical circuits',
        category: 'Electromagnetism',
        difficulty: 'Intermediate',
        estimatedTime: '30 minutes',
        completed: false,
        progress: 0,
        lastAccessed: null,
        icon: '⚡',
        url: 'experiments/circuit-simulator.html'
      },
      {
        id: 'ohms-law',
        name: "Ohm's Law Explorer",
        description: 'Investigate voltage, current, and resistance relationships',
        category: 'Electromagnetism',
        difficulty: 'Beginner',
        estimatedTime: '20 minutes',
        completed: false,
        progress: 0,
        lastAccessed: null,
        icon: '🔌',
        url: "experiments/ohms-law.html"
      },
      {
        id: 'pendulum-lab',
        name: 'Pendulum Laboratory',
        description: 'Explore pendulum motion and period relationships',
        category: 'Mechanics',
        difficulty: 'Beginner',
        estimatedTime: '25 minutes',
        completed: false,
        progress: 0,
        lastAccessed: null,
        icon: '⚖️',
        url: 'experiments/pendulum-lab.html'
      },
      {
        id: 'wave-mechanics',
        name: 'Wave Mechanics',
        description: 'Study wave properties and interference',
        category: 'Waves',
        difficulty: 'Advanced',
        estimatedTime: '45 minutes',
        completed: false,
        progress: 0,
        lastAccessed: null,
        icon: '🌊',
        url: '#coming-soon'
      },
      {
        id: 'projectile-motion',
        name: 'Projectile Motion Lab',
        description: 'Explore projectile motion with interactive simulations',
        category: 'Mechanics',
        difficulty: 'Intermediate',
        estimatedTime: '30 minutes',
        completed: false,
        progress: 0,
        lastAccessed: null,
        icon: '🚀',
        url: 'experiments/projectile-motion.html'
      },
      {
        id: 'science-simulations',
        name: 'Interactive Science Simulations',
        description: 'Multiple physics concepts in one interactive tool',
        category: 'General',
        difficulty: 'Beginner',
        estimatedTime: '45 minutes',
        completed: false,
        progress: 0,
        lastAccessed: null,
        icon: '🔬',
        url: 'experiments/science-simulations.html'
      }
    ]);
  }

  loadAchievements() {
    return this.utils.getLocalStorage('physics-lab-achievements', [
      {
        id: 'first-experiment',
        name: 'First Steps',
        description: 'Complete your first experiment',
        icon: '🎯',
        unlocked: false,
        unlockedDate: null
      },
      {
        id: 'circuit-master',
        name: 'Circuit Master',
        description: 'Complete all circuit experiments',
        icon: '⚡',
        unlocked: false,
        unlockedDate: null
      },
      {
        id: 'time-keeper',
        name: 'Time Keeper',
        description: 'Spend 2 hours in the lab',
        icon: '⏰',
        unlocked: false,
        unlockedDate: null
      },
      {
        id: 'perfectionist',
        name: 'Perfectionist',
        description: 'Complete an experiment with 100% accuracy',
        icon: '💯',
        unlocked: false,
        unlockedDate: null
      }
    ]);
  }

  render() {
    const container = document.getElementById('dashboard-content');
    if (!container) return;

    container.innerHTML = `
      <div class="dashboard-header animate-fade-in">
        <h1 class="dashboard-title">Welcome back, ${this.userData.name}! 👋</h1>
        <p class="dashboard-subtitle">Continue your physics journey</p>
      </div>

      <div class="dashboard-stats animate-slide-up">
        ${this.renderStats()}
      </div>

      <div class="dashboard-section animate-slide-up animate-stagger-1">
        <h2 class="section-title">Quick Actions</h2>
        <div class="quick-actions-grid">
          ${this.renderQuickActions()}
        </div>
      </div>

      <div class="dashboard-section animate-slide-up animate-stagger-2">
        <h2 class="section-title">Your Experiments</h2>
        <div class="experiments-grid">
          ${this.renderExperiments()}
        </div>
      </div>

      <div class="dashboard-section animate-slide-up animate-stagger-3">
        <h2 class="section-title">Achievements</h2>
        <div class="achievements-grid">
          ${this.renderAchievements()}
        </div>
      </div>

      <div class="dashboard-section animate-slide-up animate-stagger-4">
        <h2 class="section-title">Recent Activity</h2>
        <div class="activity-feed">
          ${this.renderActivity()}
        </div>
      </div>
    `;

    this.setupEventListeners();
  }

  renderStats() {
    const completedExperiments = this.experiments.filter(exp => exp.completed).length;
    const totalExperiments = this.experiments.length;
    const completionRate = totalExperiments > 0 ? (completedExperiments / totalExperiments * 100).toFixed(0) : 0;
    const unlockedAchievements = this.achievements.filter(ach => ach.unlocked).length;

    return `
      <div class="stat-card">
        <div class="stat-value">${completedExperiments}/${totalExperiments}</div>
        <div class="stat-label">Experiments Completed</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">${completionRate}%</div>
        <div class="stat-label">Completion Rate</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">${this.userData.level}</div>
        <div class="stat-label">Current Level</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">${unlockedAchievements}</div>
        <div class="stat-label">Achievements</div>
      </div>
    `;
  }

  renderQuickActions() {
    return `
      <button class="quick-action-btn" data-action="continue-experiment">
        <span class="action-icon">▶️</span>
        <span class="action-text">Continue Last Experiment</span>
      </button>
      <button class="quick-action-btn" data-action="random-experiment">
        <span class="action-icon">🎲</span>
        <span class="action-text">Random Experiment</span>
      </button>
      <button class="quick-action-btn" data-action="view-progress">
        <span class="action-icon">📊</span>
        <span class="action-text">View Progress</span>
      </button>
      <button class="quick-action-btn" data-action="settings">
        <span class="action-icon">⚙️</span>
        <span class="action-text">Settings</span>
      </button>
    `;
  }

  renderExperiments() {
    return this.experiments.map(experiment => `
      <div class="experiment-card ${experiment.completed ? 'completed' : ''}" data-experiment-id="${experiment.id}">
        <div class="experiment-card-header ${experiment.category.toLowerCase()}">
          <div class="experiment-card-icon">${experiment.icon}</div>
          ${experiment.completed ? '<div class="completion-badge">✓</div>' : ''}
        </div>
        <div class="experiment-card-body">
          <h3 class="experiment-card-title">${experiment.name}</h3>
          <p class="experiment-card-description">${experiment.description}</p>
          <div class="experiment-meta">
            <span class="experiment-category">${experiment.category}</span>
            <span class="experiment-difficulty difficulty-${experiment.difficulty.toLowerCase()}">${experiment.difficulty}</span>
            <span class="experiment-time">⏱️ ${experiment.estimatedTime}</span>
          </div>
          ${experiment.progress > 0 ? `
            <div class="progress-container">
              <div class="progress-bar">
                <div class="progress-fill" style="width: ${experiment.progress}%"></div>
              </div>
              <div class="progress-text">${experiment.progress}% Complete</div>
            </div>
          ` : ''}
          <div class="experiment-actions">
            <a href="${experiment.url}" class="btn btn-primary experiment-start-btn">
              ${experiment.progress > 0 ? 'Continue' : 'Start'} Experiment
            </a>
          </div>
        </div>
      </div>
    `).join('');
  }

  renderAchievements() {
    return this.achievements.map(achievement => `
      <div class="achievement-card ${achievement.unlocked ? 'unlocked' : 'locked'}">
        <div class="achievement-icon">${achievement.icon}</div>
        <div class="achievement-content">
          <h4 class="achievement-name">${achievement.name}</h4>
          <p class="achievement-description">${achievement.description}</p>
          ${achievement.unlocked ? `
            <div class="achievement-date">
              Unlocked ${this.utils.getRelativeTime(achievement.unlockedDate)}
            </div>
          ` : ''}
        </div>
      </div>
    `).join('');
  }

  renderActivity() {
    const activities = this.generateRecentActivity();
    
    if (activities.length === 0) {
      return `
        <div class="activity-empty">
          <div class="empty-icon">📝</div>
          <p>No recent activity. Start an experiment to see your progress here!</p>
        </div>
      `;
    }

    return activities.map(activity => `
      <div class="activity-item">
        <div class="activity-icon">${activity.icon}</div>
        <div class="activity-content">
          <div class="activity-text">${activity.text}</div>
          <div class="activity-time">${this.utils.getRelativeTime(activity.timestamp)}</div>
        </div>
      </div>
    `).join('');
  }

  generateRecentActivity() {
    const activities = [];
    
    // Add completed experiments
    this.experiments.forEach(exp => {
      if (exp.completed && exp.lastAccessed) {
        activities.push({
          icon: '✅',
          text: `Completed ${exp.name}`,
          timestamp: exp.lastAccessed
        });
      }
    });

    // Add unlocked achievements
    this.achievements.forEach(ach => {
      if (ach.unlocked && ach.unlockedDate) {
        activities.push({
          icon: '🏆',
          text: `Unlocked achievement: ${ach.name}`,
          timestamp: ach.unlockedDate
        });
      }
    });

    // Sort by timestamp (most recent first)
    return activities.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp)).slice(0, 5);
  }

  setupEventListeners() {
    // Quick action buttons
    document.querySelectorAll('.quick-action-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const action = e.currentTarget.dataset.action;
        this.handleQuickAction(action);
      });
    });

    // Experiment cards
    document.querySelectorAll('.experiment-card').forEach(card => {
      card.addEventListener('click', (e) => {
        if (!e.target.closest('.experiment-start-btn')) {
          const experimentId = card.dataset.experimentId;
          this.showExperimentDetails(experimentId);
        }
      });
    });

    // Track experiment starts
    document.querySelectorAll('.experiment-start-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const experimentId = e.target.closest('.experiment-card').dataset.experimentId;
        this.trackExperimentStart(experimentId);
      });
    });
  }

  handleQuickAction(action) {
    switch (action) {
      case 'continue-experiment':
        this.continueLastExperiment();
        break;
      case 'random-experiment':
        this.startRandomExperiment();
        break;
      case 'view-progress':
        this.showProgressModal();
        break;
      case 'settings':
        this.showSettingsModal();
        break;
    }
  }

  continueLastExperiment() {
    const lastExperiment = this.experiments
      .filter(exp => exp.lastAccessed && !exp.completed)
      .sort((a, b) => new Date(b.lastAccessed) - new Date(a.lastAccessed))[0];

    if (lastExperiment) {
      window.location.href = lastExperiment.url;
    } else {
      this.utils.showNotification('No experiments in progress. Start a new one!', 'info');
    }
  }

  startRandomExperiment() {
    const availableExperiments = this.experiments.filter(exp => !exp.completed && exp.url !== '#coming-soon');
    
    if (availableExperiments.length > 0) {
      const randomExp = availableExperiments[Math.floor(Math.random() * availableExperiments.length)];
      window.location.href = randomExp.url;
    } else {
      this.utils.showNotification('All experiments completed! 🎉', 'success');
    }
  }

  showExperimentDetails(experimentId) {
    const experiment = this.experiments.find(exp => exp.id === experimentId);
    if (!experiment) return;

    // Create and show modal with experiment details
    const modal = this.createModal('Experiment Details', `
      <div class="experiment-details">
        <div class="experiment-header">
          <span class="experiment-icon-large">${experiment.icon}</span>
          <h3>${experiment.name}</h3>
        </div>
        <p class="experiment-description-full">${experiment.description}</p>
        <div class="experiment-info">
          <div class="info-item">
            <strong>Category:</strong> ${experiment.category}
          </div>
          <div class="info-item">
            <strong>Difficulty:</strong> ${experiment.difficulty}
          </div>
          <div class="info-item">
            <strong>Estimated Time:</strong> ${experiment.estimatedTime}
          </div>
          <div class="info-item">
            <strong>Progress:</strong> ${experiment.progress}%
          </div>
        </div>
      </div>
    `, [
      {
        text: 'Start Experiment',
        class: 'btn-primary',
        action: () => {
          window.location.href = experiment.url;
        }
      }
    ]);
  }

  showProgressModal() {
    const totalProgress = this.calculateOverallProgress();
    
    const modal = this.createModal('Your Progress', `
      <div class="progress-overview">
        <div class="overall-progress">
          <h4>Overall Progress</h4>
          <div class="progress-circle">
            <div class="progress-value">${totalProgress}%</div>
          </div>
        </div>
        <div class="progress-breakdown">
          <h4>Experiment Progress</h4>
          ${this.experiments.map(exp => `
            <div class="progress-item">
              <span class="progress-name">${exp.name}</span>
              <div class="progress-bar">
                <div class="progress-fill" style="width: ${exp.progress}%"></div>
              </div>
              <span class="progress-percent">${exp.progress}%</span>
            </div>
          `).join('')}
        </div>
      </div>
    `);
  }

  showSettingsModal() {
    const modal = this.createModal('Settings', `
      <div class="settings-form">
        <div class="setting-group">
          <label for="user-name">Display Name</label>
          <input type="text" id="user-name" value="${this.userData.name}">
        </div>
        <div class="setting-group">
          <label for="units">Preferred Units</label>
          <select id="units">
            <option value="metric" ${this.userData.preferences.units === 'metric' ? 'selected' : ''}>Metric</option>
            <option value="imperial" ${this.userData.preferences.units === 'imperial' ? 'selected' : ''}>Imperial</option>
          </select>
        </div>
        <div class="setting-group">
          <label for="difficulty">Default Difficulty</label>
          <select id="difficulty">
            <option value="beginner" ${this.userData.preferences.difficulty === 'beginner' ? 'selected' : ''}>Beginner</option>
            <option value="intermediate" ${this.userData.preferences.difficulty === 'intermediate' ? 'selected' : ''}>Intermediate</option>
            <option value="advanced" ${this.userData.preferences.difficulty === 'advanced' ? 'selected' : ''}>Advanced</option>
          </select>
        </div>
        <div class="setting-group">
          <label class="checkbox-label">
            <input type="checkbox" id="notifications" ${this.userData.preferences.notifications ? 'checked' : ''}>
            Enable Notifications
          </label>
        </div>
      </div>
    `, [
      {
        text: 'Save Settings',
        class: 'btn-primary',
        action: () => {
          this.saveSettings();
        }
      }
    ]);
  }

  createModal(title, content, actions = []) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
      <div class="modal-content animate-scale-in">
        <div class="modal-header">
          <h3 class="modal-title">${title}</h3>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          ${content}
        </div>
        ${actions.length > 0 ? `
          <div class="modal-footer">
            ${actions.map(action => `
              <button class="btn ${action.class || 'btn-secondary'}" data-action="${action.text}">
                ${action.text}
              </button>
            `).join('')}
            <button class="btn btn-secondary" data-action="close">Cancel</button>
          </div>
        ` : ''}
      </div>
    `;

    // Add event listeners
    modal.querySelector('.modal-close').addEventListener('click', () => {
      this.closeModal(modal);
    });

    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        this.closeModal(modal);
      }
    });

    actions.forEach(action => {
      const btn = modal.querySelector(`[data-action="${action.text}"]`);
      if (btn) {
        btn.addEventListener('click', () => {
          action.action();
          this.closeModal(modal);
        });
      }
    });

    const closeBtn = modal.querySelector('[data-action="close"]');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        this.closeModal(modal);
      });
    }

    document.body.appendChild(modal);
    return modal;
  }

  closeModal(modal) {
    modal.classList.add('animate-fade-out');
    setTimeout(() => {
      if (modal.parentNode) {
        modal.parentNode.removeChild(modal);
      }
    }, 300);
  }

  calculateOverallProgress() {
    const totalProgress = this.experiments.reduce((sum, exp) => sum + exp.progress, 0);
    return Math.round(totalProgress / this.experiments.length);
  }

  trackExperimentStart(experimentId) {
    const experiment = this.experiments.find(exp => exp.id === experimentId);
    if (experiment) {
      experiment.lastAccessed = new Date().toISOString();
      this.saveExperiments();
    }
  }

  saveSettings() {
    const name = document.getElementById('user-name').value;
    const units = document.getElementById('units').value;
    const difficulty = document.getElementById('difficulty').value;
    const notifications = document.getElementById('notifications').checked;

    this.userData.name = name;
    this.userData.preferences.units = units;
    this.userData.preferences.difficulty = difficulty;
    this.userData.preferences.notifications = notifications;

    this.utils.setLocalStorage('physics-lab-user', this.userData);
    this.utils.showNotification('Settings saved successfully!', 'success');
    
    // Re-render dashboard with updated data
    this.render();
  }

  saveExperiments() {
    this.utils.setLocalStorage('physics-lab-experiments', this.experiments);
  }

  saveAchievements() {
    this.utils.setLocalStorage('physics-lab-achievements', this.achievements);
  }

  // Public API for other modules
  updateExperimentProgress(experimentId, progress) {
    const experiment = this.experiments.find(exp => exp.id === experimentId);
    if (experiment) {
      experiment.progress = Math.max(experiment.progress, progress);
      if (progress >= 100) {
        experiment.completed = true;
        this.checkAchievements();
      }
      this.saveExperiments();
    }
  }

  checkAchievements() {
    // Check for first experiment completion
    const firstExperiment = this.achievements.find(ach => ach.id === 'first-experiment');
    if (!firstExperiment.unlocked && this.experiments.some(exp => exp.completed)) {
      this.unlockAchievement('first-experiment');
    }

    // Check for circuit master
    const circuitExperiments = this.experiments.filter(exp => exp.category === 'Electromagnetism');
    const circuitMaster = this.achievements.find(ach => ach.id === 'circuit-master');
    if (!circuitMaster.unlocked && circuitExperiments.every(exp => exp.completed)) {
      this.unlockAchievement('circuit-master');
    }
  }

  unlockAchievement(achievementId) {
    const achievement = this.achievements.find(ach => ach.id === achievementId);
    if (achievement && !achievement.unlocked) {
      achievement.unlocked = true;
      achievement.unlockedDate = new Date().toISOString();
      this.saveAchievements();
      this.utils.showNotification(`Achievement unlocked: ${achievement.name}! 🏆`, 'success');
    }
  }
}

// Initialize dashboard
const dashboard = new Dashboard();

// Export for use in other modules
export default dashboard;
