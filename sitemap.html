<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Site Map - Physics Virtual Lab</title>
    <meta name="author" content="Dr<PERSON> <PERSON>, SUST-BME">
    <meta name="copyright" content="Dr. <PERSON> Esmail - +249912867327, +966538076790">
    <meta name="institution" content="Sudan University of Science and Technology - Biomedical Engineering">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/components.css">
    <style>
        body {
            font-family: var(--font-family-sans);
            margin: 0;
            padding: 0;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .sitemap-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: var(--spacing-2xl) 0;
            text-align: center;
        }

        .sitemap-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: var(--spacing-sm);
        }

        .sitemap-subtitle {
            font-size: 1.125rem;
            opacity: 0.9;
        }

        .main-content {
            flex: 1;
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--spacing-xl);
            width: 100%;
        }

        .sitemap-section {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            padding: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
        }

        [data-theme="dark"] .sitemap-section {
            background: var(--gray-700);
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--primary-600);
            margin-bottom: var(--spacing-lg);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .sitemap-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-lg);
        }

        .page-group {
            background: var(--bg-secondary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
        }

        .group-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .page-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .page-item {
            margin-bottom: var(--spacing-sm);
        }

        .page-link {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm);
            border-radius: var(--radius-md);
            text-decoration: none;
            color: var(--text-primary);
            transition: all var(--transition-fast);
        }

        .page-link:hover {
            background: var(--accent-50);
            color: var(--accent-700);
            transform: translateX(4px);
        }

        [data-theme="dark"] .page-link:hover {
            background: var(--accent-900);
            color: var(--accent-300);
        }

        .page-icon {
            font-size: 1.25rem;
        }

        .page-info {
            flex: 1;
        }

        .page-name {
            font-weight: 500;
            margin-bottom: 2px;
        }

        .page-description {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .stat-card {
            background: white;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            padding: var(--spacing-lg);
            text-align: center;
        }

        [data-theme="dark"] .stat-card {
            background: var(--gray-700);
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: var(--spacing-sm);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-600);
            margin-bottom: var(--spacing-xs);
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .navigation-flow {
            background: var(--accent-50);
            border: 1px solid var(--accent-200);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            margin-top: var(--spacing-xl);
        }

        [data-theme="dark"] .navigation-flow {
            background: var(--accent-900);
            border-color: var(--accent-700);
        }

        .flow-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--accent-700);
            margin-bottom: var(--spacing-md);
        }

        [data-theme="dark"] .flow-title {
            color: var(--accent-300);
        }

        .flow-path {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-sm);
            flex-wrap: wrap;
        }

        .flow-step {
            padding: var(--spacing-xs) var(--spacing-sm);
            background: var(--accent-600);
            color: white;
            border-radius: var(--radius-sm);
            font-size: 0.875rem;
            font-weight: 500;
        }

        .flow-arrow {
            color: var(--accent-600);
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .sitemap-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-overview {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .flow-path {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .flow-arrow {
                transform: rotate(90deg);
            }
        }
    </style>
</head>
<body>
    <header id="main-header"></header>

    <div class="sitemap-header animate-fade-in">
        <h1 class="sitemap-title">🗺️ Site Map</h1>
        <p class="sitemap-subtitle">Complete navigation guide for Physics Virtual Lab</p>
    </div>

    <div class="main-content">
        <div class="stats-overview animate-slide-up">
            <div class="stat-card">
                <div class="stat-icon">📄</div>
                <div class="stat-value">15+</div>
                <div class="stat-label">Total Pages</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🧪</div>
                <div class="stat-value">12</div>
                <div class="stat-label">Experiments</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🎬</div>
                <div class="stat-value">24+</div>
                <div class="stat-label">Video Content</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🎯</div>
                <div class="stat-value">3</div>
                <div class="stat-label">Learning Paths</div>
            </div>
        </div>

        <div class="sitemap-section animate-slide-up animate-stagger-1">
            <h2 class="section-title">🏠 Main Pages</h2>
            <div class="sitemap-grid">
                <div class="page-group">
                    <h3 class="group-title">🎯 Core Navigation</h3>
                    <ul class="page-list">
                        <li class="page-item">
                            <a href="index.html" class="page-link">
                                <span class="page-icon">🏠</span>
                                <div class="page-info">
                                    <div class="page-name">Home</div>
                                    <div class="page-description">Main landing page with overview</div>
                                </div>
                            </a>
                        </li>
                        <li class="page-item">
                            <a href="dashboard.html" class="page-link">
                                <span class="page-icon">📊</span>
                                <div class="page-info">
                                    <div class="page-name">Dashboard</div>
                                    <div class="page-description">Progress tracking and analytics</div>
                                </div>
                            </a>
                        </li>
                        <li class="page-item">
                            <a href="experiments-hub.html" class="page-link">
                                <span class="page-icon">🧪</span>
                                <div class="page-info">
                                    <div class="page-name">Experiments Hub</div>
                                    <div class="page-description">Central hub for all experiments</div>
                                </div>
                            </a>
                        </li>
                        <li class="page-item">
                            <a href="learning-paths.html" class="page-link">
                                <span class="page-icon">🎯</span>
                                <div class="page-info">
                                    <div class="page-name">Learning Paths</div>
                                    <div class="page-description">Structured learning progression</div>
                                </div>
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="page-group">
                    <h3 class="group-title">🎬 Video & Assessment</h3>
                    <ul class="page-list">
                        <li class="page-item">
                            <a href="video-library.html" class="page-link">
                                <span class="page-icon">🎬</span>
                                <div class="page-info">
                                    <div class="page-name">Video Library</div>
                                    <div class="page-description">Animated physics videos</div>
                                </div>
                            </a>
                        </li>
                        <li class="page-item">
                            <a href="interactive-video.html" class="page-link">
                                <span class="page-icon">🎮</span>
                                <div class="page-info">
                                    <div class="page-name">Interactive Video Player</div>
                                    <div class="page-description">Enhanced video experience</div>
                                </div>
                            </a>
                        </li>
                        <li class="page-item">
                            <a href="video-studio.html" class="page-link">
                                <span class="page-icon">🎨</span>
                                <div class="page-info">
                                    <div class="page-name">Video Creation Studio</div>
                                    <div class="page-description">Create custom videos</div>
                                </div>
                            </a>
                        </li>
                        <li class="page-item">
                            <a href="assessment.html" class="page-link">
                                <span class="page-icon">🎓</span>
                                <div class="page-info">
                                    <div class="page-name">Assessment Center</div>
                                    <div class="page-description">Quizzes and evaluations</div>
                                </div>
                            </a>
                        </li>
                        <li class="page-item">
                            <a href="quiz.html" class="page-link">
                                <span class="page-icon">❓</span>
                                <div class="page-info">
                                    <div class="page-name">Interactive Quiz</div>
                                    <div class="page-description">Quiz interface</div>
                                </div>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="sitemap-section animate-slide-up animate-stagger-2">
            <h2 class="section-title">🧪 Experiments</h2>
            <div class="sitemap-grid">
                <div class="page-group">
                    <h3 class="group-title">⚡ Electromagnetism</h3>
                    <ul class="page-list">
                        <li class="page-item">
                            <a href="experiments/ohms-law.html" class="page-link">
                                <span class="page-icon">🔌</span>
                                <div class="page-info">
                                    <div class="page-name">Ohm's Law Explorer</div>
                                    <div class="page-description">V = IR relationships</div>
                                </div>
                            </a>
                        </li>
                        <li class="page-item">
                            <a href="experiments/circuit-simulator.html" class="page-link">
                                <span class="page-icon">⚡</span>
                                <div class="page-info">
                                    <div class="page-name">Circuit Simulator</div>
                                    <div class="page-description">Build electrical circuits</div>
                                </div>
                            </a>
                        </li>
                        <li class="page-item">
                            <a href="Ohm's Law Circuit Simulator.html" class="page-link">
                                <span class="page-icon">🔬</span>
                                <div class="page-info">
                                    <div class="page-name">Advanced Circuit Lab</div>
                                    <div class="page-description">Complex circuit analysis</div>
                                </div>
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="page-group">
                    <h3 class="group-title">🔬 Mechanics</h3>
                    <ul class="page-list">
                        <li class="page-item">
                            <a href="experiments/pendulum-lab.html" class="page-link">
                                <span class="page-icon">⚖️</span>
                                <div class="page-info">
                                    <div class="page-name">Pendulum Laboratory</div>
                                    <div class="page-description">Simple harmonic motion</div>
                                </div>
                            </a>
                        </li>
                        <li class="page-item">
                            <a href="experiments/projectile-motion.html" class="page-link">
                                <span class="page-icon">🚀</span>
                                <div class="page-info">
                                    <div class="page-name">Projectile Motion</div>
                                    <div class="page-description">Trajectory analysis</div>
                                </div>
                            </a>
                        </li>
                        <li class="page-item">
                            <a href="Physics Simulations.html" class="page-link">
                                <span class="page-icon">⚙️</span>
                                <div class="page-info">
                                    <div class="page-name">Physics Simulations</div>
                                    <div class="page-description">Multiple mechanics concepts</div>
                                </div>
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="page-group">
                    <h3 class="group-title">🌡️ Thermodynamics & More</h3>
                    <ul class="page-list">
                        <li class="page-item">
                            <a href="Boyle's Law Simulation.html" class="page-link">
                                <span class="page-icon">🌡️</span>
                                <div class="page-info">
                                    <div class="page-name">Boyle's Law</div>
                                    <div class="page-description">Pressure-volume relationships</div>
                                </div>
                            </a>
                        </li>
                        <li class="page-item">
                            <a href="volume, pressure Relationship.html" class="page-link">
                                <span class="page-icon">📊</span>
                                <div class="page-info">
                                    <div class="page-name">Gas Laws</div>
                                    <div class="page-description">Gas behavior analysis</div>
                                </div>
                            </a>
                        </li>
                        <li class="page-item">
                            <a href="Probability Experiment.html" class="page-link">
                                <span class="page-icon">🎲</span>
                                <div class="page-info">
                                    <div class="page-name">Probability Lab</div>
                                    <div class="page-description">Statistical analysis</div>
                                </div>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="navigation-flow animate-slide-up animate-stagger-3">
            <h3 class="flow-title">🧭 Recommended Learning Flow</h3>
            <div class="flow-path">
                <span class="flow-step">Home</span>
                <span class="flow-arrow">→</span>
                <span class="flow-step">Dashboard</span>
                <span class="flow-arrow">→</span>
                <span class="flow-step">Learning Paths</span>
                <span class="flow-arrow">→</span>
                <span class="flow-step">Experiments</span>
                <span class="flow-arrow">→</span>
                <span class="flow-step">Assessment</span>
            </div>
            <div class="flow-path">
                <span class="flow-step">Video Library</span>
                <span class="flow-arrow">→</span>
                <span class="flow-step">Interactive Videos</span>
                <span class="flow-arrow">→</span>
                <span class="flow-step">Related Experiments</span>
                <span class="flow-arrow">→</span>
                <span class="flow-step">Quiz</span>
            </div>
        </div>
    </div>

    <footer id="main-footer-container"></footer>

    <script type="module" src="js/main.js"></script>
</body>
</html>
