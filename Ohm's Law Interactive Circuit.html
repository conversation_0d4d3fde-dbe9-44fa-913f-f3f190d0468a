<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ohm's Law Interactive Circuit</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            margin: 0;
            padding: 20px;
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            line-height: 1.6;
        }

        .container {
            background-color: #ffffff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 600px;
            margin-bottom: 20px;
        }

        h1 {
            color: #1a73e8; /* A nice blue */
            text-align: center;
            margin-top: 0;
            margin-bottom: 20px;
        }

        .circuit-area {
            width: 100%;
            max-width: 400px; /* SVG native width */
            margin: 0 auto 20px auto; /* Centered */
        }

        #circuitSvg {
            width: 100%;
            height: auto; /* Maintain aspect ratio */
            display: block; /* Remove extra space below */
        }
        
        /* SVG element styling */
        .wire { stroke: #555555; stroke-width: 3px; fill: none; }
        .battery-body { fill: #DDDDDD; stroke: #333333; stroke-width: 2px; }
        .battery-terminal { fill: #AAAAAA; }
        .battery-text { font-size: 20px; text-anchor: middle; fill: #333333; dominant-baseline: central; user-select: none; }
        .resistor-path { stroke: #333333; stroke-width: 3px; fill: none; }
        .component-label { font-size: 12px; text-anchor: middle; fill: #333333; user-select: none; }
        #lightBulbElement { stroke: #666666; stroke-width: 2px; transition: fill 0.1s linear; }
        #lightBulbGlow { transition: opacity 0.1s linear; }
        .bulb-base { fill: #777777; stroke: #333333; stroke-width: 1px; }


        .controls, .data-display, .explanation {
            margin-top: 15px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }

        input[type="range"] {
            width: 100%;
            cursor: pointer;
            margin-bottom: 5px;
        }

        .value-display-container {
            margin-top: 5px;
            font-size: 1.1em;
        }
        
        .value-display {
            font-weight: bold;
            color: #d32f2f; /* Material Red */
            min-width: 50px; /* Ensure space for value */
            display: inline-block;
            text-align: right; /* Align numbers to the right if min-width creates space */
        }
        
        .unit {
            color: #555;
            margin-left: 3px;
        }

        .explanation h2 {
            margin-top: 0;
            color: #1a73e8;
            font-size: 1.2em;
        }
        .explanation p, .explanation ul {
            margin-bottom: 10px;
        }
        .explanation ul {
            padding-left: 20px;
        }
        .explanation code {
            background-color: #e0e0e0;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
        }

        /* Responsive adjustments */
        @media (max-width: 640px) {
            body { padding: 10px; }
            .container { padding: 15px; }
            h1 { font-size: 1.5em; } /* Slightly smaller H1 on mobile */
            .value-display-container { font-size: 1em; }
            .component-label { font-size: 11px; }
        }

    </style>
</head>
<body>

    <div class="container">
        <h1>Ohm's Law Interactive Circuit</h1>

        <div class="circuit-area">
            <svg id="circuitSvg" viewBox="0 0 400 250" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <filter id="glowEffectFilter">
                        <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
                    </filter>
                </defs>
                
                <!-- Battery -->
                <rect x="20" y="85" width="40" height="80" class="battery-body"/>
                <rect x="35" y="75" width="10" height="10" class="battery-terminal"/> <!-- Positive terminal nub -->
                <text x="40" y="105" class="battery-text">+</text>
                <text x="40" y="145" class="battery-text">-</text>
                <text x="40" y="60" class="component-label">Battery (12V)</text>

                <!-- Wires -->
                <line x1="60" y1="90" x2="100" y2="90" class="wire"/> <!-- Battery to Resistor start -->
                <line x1="220" y1="90" x2="280" y2="90" class="wire"/> <!-- Resistor end to Corner -->
                <line x1="280" y1="90" x2="280" y2="105" class="wire"/> <!-- Corner to Bulb top -->
                <line x1="280" y1="165" x2="280" y2="180" class="wire"/> <!-- Bulb bottom to Corner -->
                <line x1="280" y1="180" x2="60" y2="180" class="wire"/>   <!-- Bottom wire -->
                <line x1="60" y1="180" x2="60" y2="160" class="wire"/>   <!-- Wire to Battery bottom -->

                <!-- Resistor -->
                <path d="M100 90 l10 -8 l20 16 l20 -16 l20 16 l20 -16 l20 16 l10 -8" class="resistor-path"/>
                <text x="160" y="60" class="component-label">Resistor</text>

                <!-- Light Bulb -->
                <circle id="lightBulbGlow" cx="280" cy="135" r="25" fill="hsl(60, 100%, 70%)" opacity="0.0" filter="url(#glowEffectFilter)"/>
                <circle id="lightBulbElement" cx="280" cy="135" r="22" fill="hsl(60, 100%, 20%)"/>
                <rect x="272" y="157" width="16" height="10" class="bulb-base"/> <!-- Bulb base -->
                <text x="280" y="195" class="component-label">Light Bulb</text>
            </svg>
        </div>

        <div class="controls">
            <label for="resistanceSlider">Adjust Resistance (R):</label>
            <input type="range" id="resistanceSlider" min="10" max="200" value="100" step="1">
            <div class="value-display-container">
                Resistance: <span id="resistanceValue" class="value-display">100.0</span><span class="unit">&Omega;</span>
            </div>
        </div>

        <div class="data-display">
            Current (I): <span id="currentValue" class="value-display">0.00</span><span class="unit">A</span>
        </div>

    </div>

    <div class="container explanation">
        <h2>Understanding Ohm's Law</h2>
        <p>Ohm's Law describes the relationship between voltage (V), current (I), and resistance (R) in an electrical circuit. It is expressed by the formula:</p>
        <p style="text-align: center; font-size: 1.2em; font-weight: bold;"><code>V = I × R</code></p>
        <p>In this interactive simulation:</p>
        <ul>
            <li>The <strong>Voltage (V)</strong> provided by the battery is constant (12 Volts).</li>
            <li>You can adjust the <strong>Resistance (R)</strong> using the slider. The resistance value is displayed above.</li>
            <li>The <strong>Current (I)</strong> flowing through the circuit changes based on the resistance. It is calculated as <code>I = V / R</code> and displayed above.</li>
        </ul>
        <p>Observe the following:</p>
        <ul>
            <li>When you <strong>increase resistance (R)</strong>, the current (I) decreases, and the light bulb becomes dimmer.</li>
            <li>When you <strong>decrease resistance (R)</strong>, the current (I) increases, and the light bulb becomes brighter.</li>
        </ul>
        <p>This demonstrates the inverse proportionality between current and resistance when voltage is constant.</p>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Constants
            const VOLTAGE = 12; // Volts

            // DOM Elements
            const resistanceSlider = document.getElementById('resistanceSlider');
            const resistanceValueDisplay = document.getElementById('resistanceValue');
            const currentValueDisplay = document.getElementById('currentValue');
            const lightBulbElement = document.getElementById('lightBulbElement');
            const lightBulbGlow = document.getElementById('lightBulbGlow');

            // Slider properties
            const minResistance = parseFloat(resistanceSlider.min);
            const maxResistance = parseFloat(resistanceSlider.max);

            // Calculate min/max current for brightness mapping
            // Max current occurs at min resistance, min current occurs at max resistance
            const maxCurrent = VOLTAGE / minResistance; 
            const minCurrent = VOLTAGE / maxResistance; 

            function updateCircuit() {
                const resistance = parseFloat(resistanceSlider.value);
                
                // Current calculation, ensure resistance is not zero if slider could go to 0.
                // Here, min resistance is 10, so no division by zero.
                const current = VOLTAGE / resistance;

                // Update displays
                resistanceValueDisplay.textContent = resistance.toFixed(1);
                currentValueDisplay.textContent = current.toFixed(2);

                // Update light bulb brightness
                let brightnessFactor = 0; // Default to 0 (dimmest)
                if (maxCurrent - minCurrent !== 0) { // Avoid division by zero if minR = maxR
                    brightnessFactor = (current - minCurrent) / (maxCurrent - minCurrent);
                } else if (current >= minCurrent) { // Handle case where minR = maxR (fixed current)
                     // If there's current, it's at its 'only' level. Can be set to 0.5 or 1.
                     // Given it's a single point, mapping to 'average' or 'full' makes sense.
                     // For simplicity, if minCurrent = maxCurrent, then current is always that value.
                     // So brightnessFactor can be 0.5 or 1, depending on how you want to represent a single state.
                     // Let's assume if current is flowing, it's somewhat bright.
                    brightnessFactor = (minCurrent > 0) ? 0.5 : 0; // Arbitrary for single fixed current
                }
                
                brightnessFactor = Math.max(0, Math.min(1, brightnessFactor)); // Clamp between 0 and 1
                
                // Apply a power curve to make brightness changes more perceptible
                // (e.g., sqrt makes it more sensitive at lower currents, visually enhancing dim changes)
                brightnessFactor = Math.pow(brightnessFactor, 0.5);

                // Map brightnessFactor to HSL lightness for the bulb color
                const minLightness = 20; // L value for HSL (dimmest yellow)
                const maxLightness = 95; // L value for HSL (brightest yellow/white)
                const bulbLightness = minLightness + brightnessFactor * (maxLightness - minLightness);
                lightBulbElement.style.fill = `hsl(60, 100%, ${bulbLightness}%)`;

                // Update glow effect opacity
                const minGlowOpacity = 0.0; // No glow when fully dim
                const maxGlowOpacity = 0.6; // Max glow intensity
                lightBulbGlow.style.opacity = minGlowOpacity + brightnessFactor * (maxGlowOpacity - minGlowOpacity);
            }

            // Event Listener for the slider
            resistanceSlider.addEventListener('input', updateCircuit);

            // Initial call to set up the circuit display based on default slider value
            updateCircuit();
        });
    </script>

</body>
</html>
