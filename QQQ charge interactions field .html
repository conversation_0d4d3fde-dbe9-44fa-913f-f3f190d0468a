<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Electric Fields & Equipotentials</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0;
            padding: 10px;
            background-color: #f0f0f0;
            touch-action: none; /* Prevent default touch actions like pinch-zoom on canvas */
        }
        #controls {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 10px;
            padding: 10px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button, select {
            padding: 8px 12px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background-color: #e9e9e9;
            cursor: pointer;
        }
        button:hover {
            background-color: #ddd;
        }
        #canvasContainer {
            position: relative;
            width: 90vw;
            max-width: 800px;
            aspect-ratio: 1.5; /* Maintain aspect ratio */
            border: 1px solid #000;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        canvas {
            display: block;
            width: 100%;
            height: 100%;
        }
        #potentialDisplay {
            margin-top: 10px;
            padding: 8px;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .legend {
            margin-top: 15px;
            padding: 10px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 90vw;
        }
        .legend h3 {
            margin-top: 0;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            margin-right: 8px;
            border: 1px solid #ccc;
        }
        .selected {
            border: 2px dashed gold !important;
            transform: scale(1.1);
        }
    </style>
</head>
<body>

    <div id="controls">
        <button id="addPositive">Add +Q (Max 3)</button>
        <button id="addNegative">Add -Q (Max 3)</button>
        <button id="removeSelected">Remove Selected Charge</button>
        <button id="clearAll">Clear All Charges</button>
        <label for="toggleSensor">Sensor:</label>
        <select id="toggleSensor">
            <option value="on">On</option>
            <option value="off" selected>Off</option>
        </select>
        <label for="toggleFieldLines">Field Arrows:</label>
        <select id="toggleFieldLines">
            <option value="on" selected>On</option>
            <option value="off">Off</option>
        </select>
        <label for="toggleEquipotentials">Equipotentials:</label>
        <select id="toggleEquipotentials">
            <option value="on" selected>On</option>
            <option value="off">Off</option>
        </select>
    </div>

    <div id="canvasContainer">
        <canvas id="simulationCanvas"></canvas>
    </div>

    <div id="potentialDisplay">Potential at sensor: N/A</div>

    <div class="legend">
        <h3>Legend</h3>
        <div class="legend-item">
            <div class="legend-color" style="background-color: red; border-radius: 50%; text-align: center; color: white; line-height: 20px;">+</div>
            <span>Positive Charge (+Q)</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: blue; border-radius: 50%; text-align: center; color: white; line-height: 20px;">-</div>
            <span>Negative Charge (-Q)</span>
        </div>
        <div class="legend-item">
            <svg width="20" height="20" viewBox="0 0 20 20" style="margin-right: 8px;">
                <line x1="2" y1="10" x2="15" y2="10" stroke="black" stroke-width="1"/>
                <polyline points="12,7 15,10 12,13" stroke="black" stroke-width="1" fill="none"/>
            </svg>
            <span>Electric Field Arrow (Direction shown. Length/color intensity indicates strength. Drawn on a grid.)</span>
        </div>
        <div class="legend-item">
            <svg width="20" height="20" viewBox="0 0 20 20" style="margin-right: 8px;">
                 <path d="M 2 10 Q 10 2 18 10" stroke="green" stroke-width="1" fill="none"/>
            </svg>
            <span>Equipotential Line (Constant electric potential. Reddish for V&gt;0, Bluish for V&lt;0, Gray for V~0)</span>
        </div>
         <div class="legend-item">
            <div class="legend-color" style="background-color: #FFD700; border-radius: 50%; border: 1px solid black;"></div>
            <span>Potential Sensor (Draggable when active)</span>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('simulationCanvas');
        const ctx = canvas.getContext('2d');
        const potentialDisplay = document.getElementById('potentialDisplay');

        let charges = [];
        let selectedChargeIndex = -1;
        let draggingCharge = null;
        let draggingSensor = false;
        let dragOffsetX, dragOffsetY;

        const K_CONST = 50000; // Scaled Coulomb's constant for visualization
        const Q_MAGNITUDE = 1;
        const CHARGE_RADIUS = 10;
        const MIN_DIST_SQ = 5*5; // Minimum distance squared for calculations to avoid infinities

        let sensor = {
            x: 100,
            y: 100,
            radius: 8,
            visible: false
        };

        let showFieldLines = true;
        let showEquipotentials = true;

        const MAX_POS_CHARGES = 3;
        const MAX_NEG_CHARGES = 3;
        let numPosCharges = 0;
        let numNegCharges = 0;

        function resizeCanvas() {
            const container = document.getElementById('canvasContainer');
            canvas.width = container.clientWidth;
            canvas.height = container.clientHeight;
            // Reset sensor position if it's out of bounds, or to center
            sensor.x = Math.min(sensor.x, canvas.width - sensor.radius);
            sensor.y = Math.min(sensor.y, canvas.height - sensor.radius);
            if (charges.length === 0) { // Initial placement if no charges yet
                sensor.x = canvas.width / 4;
                sensor.y = canvas.height / 2;
            }
            drawAll();
        }

        function addCharge(type) {
            if (charges.length >= MAX_POS_CHARGES + MAX_NEG_CHARGES) {
                alert(`Maximum total charges (${MAX_POS_CHARGES + MAX_NEG_CHARGES}) reached.`);
                return;
            }

            if (type === 'positive') {
                if (numPosCharges >= MAX_POS_CHARGES) {
                    alert(`Maximum positive charges (${MAX_POS_CHARGES}) reached.`);
                    return;
                }
                numPosCharges++;
            } else {
                if (numNegCharges >= MAX_NEG_CHARGES) {
                    alert(`Maximum negative charges (${MAX_NEG_CHARGES}) reached.`);
                    return;
                }
                numNegCharges++;
            }

            const charge = {
                x: canvas.width / 2 + (Math.random() - 0.5) * 50,
                y: canvas.height / 2 + (Math.random() - 0.5) * 50,
                q: type === 'positive' ? Q_MAGNITUDE : -Q_MAGNITUDE,
                radius: CHARGE_RADIUS,
                id: Date.now() + Math.random() // Unique ID
            };
            charges.push(charge);
            selectedChargeIndex = charges.length - 1; // Select the new charge
            drawAll();
        }

        function removeSelectedCharge() {
            if (selectedChargeIndex !== -1 && charges[selectedChargeIndex]) {
                const removedCharge = charges.splice(selectedChargeIndex, 1)[0];
                if (removedCharge.q > 0) numPosCharges--;
                else numNegCharges--;
                selectedChargeIndex = -1;
                drawAll();
            }
        }

        function clearAllCharges() {
            charges = [];
            selectedChargeIndex = -1;
            numPosCharges = 0;
            numNegCharges = 0;
            drawAll();
        }

        function getMousePos(event) {
            const rect = canvas.getBoundingClientRect();
            let x, y;
            if (event.touches && event.touches.length > 0) {
                x = event.touches[0].clientX - rect.left;
                y = event.touches[0].clientY - rect.top;
            } else {
                x = event.clientX - rect.left;
                y = event.clientY - rect.top;
            }
            return { x, y };
        }

        function handleMouseDown(event) {
            event.preventDefault(); // Prevent text selection, etc.
            const mousePos = getMousePos(event);

            // Check for sensor drag
            if (sensor.visible) {
                const distToSensorSq = (mousePos.x - sensor.x)**2 + (mousePos.y - sensor.y)**2;
                if (distToSensorSq < (sensor.radius + 5)**2) { // +5 for easier grabbing
                    draggingSensor = true;
                    dragOffsetX = mousePos.x - sensor.x;
                    dragOffsetY = mousePos.y - sensor.y;
                    selectedChargeIndex = -1; // Deselect charge if dragging sensor
                    drawAll(); // Redraw to show sensor selection if any
                    return;
                }
            }
            
            // Check for charge drag/selection
            selectedChargeIndex = -1; // Deselect by default
            for (let i = charges.length - 1; i >= 0; i--) {
                const charge = charges[i];
                const distSq = (mousePos.x - charge.x)**2 + (mousePos.y - charge.y)**2;
                if (distSq < charge.radius**2) {
                    draggingCharge = charge;
                    selectedChargeIndex = i;
                    dragOffsetX = mousePos.x - charge.x;
                    dragOffsetY = mousePos.y - charge.y;
                    drawAll(); // Redraw to show selection
                    return;
                }
            }
            // If clicked on empty space, deselect
            drawAll();
        }

        function handleMouseMove(event) {
            if (!draggingCharge && !draggingSensor) return;
            event.preventDefault();
            const mousePos = getMousePos(event);

            if (draggingCharge) {
                draggingCharge.x = Math.max(0, Math.min(canvas.width, mousePos.x - dragOffsetX));
                draggingCharge.y = Math.max(0, Math.min(canvas.height, mousePos.y - dragOffsetY));
                drawAll();
            } else if (draggingSensor) {
                sensor.x = Math.max(0, Math.min(canvas.width, mousePos.x - dragOffsetX));
                sensor.y = Math.max(0, Math.min(canvas.height, mousePos.y - dragOffsetY));
                drawAll();
            }
        }

        function handleMouseUp(event) {
            event.preventDefault();
            draggingCharge = null;
            draggingSensor = false;
        }

        function calculatePotentialAt(x, y) {
            let totalPotential = 0;
            for (const charge of charges) {
                const dx = x - charge.x;
                const dy = y - charge.y;
                const rSq = dx**2 + dy**2;
                if (rSq < MIN_DIST_SQ) { // If very close, use a minimum distance effect
                     const rMin = Math.sqrt(MIN_DIST_SQ);
                     totalPotential += K_CONST * charge.q / (charge.q > 0 ? rMin : -rMin); // keep sign
                } else {
                    totalPotential += K_CONST * charge.q / Math.sqrt(rSq);
                }
            }
            return totalPotential;
        }

        function calculateFieldAt(x, y) {
            let Ex = 0, Ey = 0;
            for (const charge of charges) {
                const dx = x - charge.x;
                const dy = y - charge.y;
                let rSq = dx**2 + dy**2;
                
                if (rSq < MIN_DIST_SQ) { // Avoid extreme fields very close to charge
                    rSq = MIN_DIST_SQ;
                }
                
                const r = Math.sqrt(rSq);
                if (r === 0) continue; // Should be caught by MIN_DIST_SQ

                const E_magnitude_component = K_CONST * charge.q / rSq;
                Ex += E_magnitude_component * (dx / r);
                Ey += E_magnitude_component * (dy / r);
            }
            const magnitude = Math.sqrt(Ex**2 + Ey**2);
            return { Ex, Ey, magnitude };
        }

        function drawCharges() {
            charges.forEach((charge, index) => {
                ctx.beginPath();
                ctx.arc(charge.x, charge.y, charge.radius, 0, 2 * Math.PI);
                ctx.fillStyle = charge.q > 0 ? 'red' : 'blue';
                ctx.fill();
                if (index === selectedChargeIndex) {
                    ctx.strokeStyle = 'gold';
                    ctx.lineWidth = 3;
                    ctx.stroke();
                }
                ctx.fillStyle = 'white';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.font = 'bold 12px Arial';
                ctx.fillText(charge.q > 0 ? '+' : '-', charge.x, charge.y);
            });
        }

        function drawFieldLines() {
            if (!showFieldLines || charges.length === 0) return;
            const gridSize = Math.min(canvas.width, canvas.height) / 20; // Dynamic grid size
            const arrowBaseLength = gridSize * 0.6;
            const maxArrowLength = gridSize * 0.9;
            const fieldStrengthScale = 0.005; // Adjust to scale arrow lengths appropriately

            for (let x = gridSize / 2; x < canvas.width; x += gridSize) {
                for (let y = gridSize / 2; y < canvas.height; y += gridSize) {
                    // Don't draw arrows too close to charges
                    let tooClose = false;
                    for (const charge of charges) {
                        if ((x - charge.x)**2 + (y - charge.y)**2 < (charge.radius * 1.5)**2) {
                            tooClose = true;
                            break;
                        }
                    }
                    if (tooClose) continue;

                    const field = calculateFieldAt(x, y);
                    if (field.magnitude < 1) continue; // Skip very weak fields

                    const angle = Math.atan2(field.Ey, field.Ex);
                    // Scale length based on magnitude, but cap it
                    let len = arrowBaseLength * Math.log1p(field.magnitude * fieldStrengthScale); // Log scale for better visuals
                    len = Math.min(len, maxArrowLength);
                    
                    if (len < 2) continue; // Skip tiny arrows

                    // Arrow color based on strength (e.g. grayscale or intensity)
                    const strengthRatio = Math.min(1, field.magnitude / (K_CONST * Q_MAGNITUDE / (gridSize*gridSize) * 5 )); // Normalize based on typical strong field
                    const colorIntensity = Math.floor(150 * (1 - strengthRatio)); // Darker for stronger
                    ctx.strokeStyle = `rgb(${colorIntensity},${colorIntensity},${colorIntensity})`;
                    ctx.lineWidth = 1 + strengthRatio; // Thicker for stronger

                    ctx.beginPath();
                    ctx.moveTo(x, y);
                    const endX = x + len * Math.cos(angle);
                    const endY = y + len * Math.sin(angle);
                    ctx.lineTo(endX, endY);
                    
                    // Arrowhead
                    const headlen = Math.max(3, len * 0.3);
                    ctx.moveTo(endX, endY);
                    ctx.lineTo(endX - headlen * Math.cos(angle - Math.PI / 6), endY - headlen * Math.sin(angle - Math.PI / 6));
                    ctx.moveTo(endX, endY);
                    ctx.lineTo(endX - headlen * Math.cos(angle + Math.PI / 6), endY - headlen * Math.sin(angle + Math.PI / 6));
                    ctx.stroke();
                }
            }
        }

        function drawEquipotentials() {
            if (!showEquipotentials || charges.length === 0) return;
            
            const gridStep = Math.min(canvas.width, canvas.height) / 40; // Finer grid for marching squares
            const numX = Math.floor(canvas.width / gridStep);
            const numY = Math.floor(canvas.height / gridStep);

            const potentials = [];
            for (let i = 0; i <= numX; i++) {
                potentials[i] = [];
                for (let j = 0; j <= numY; j++) {
                    potentials[i][j] = calculatePotentialAt(i * gridStep, j * gridStep);
                }
            }

            // Determine dynamic range of potential levels
            let minPot = Infinity, maxPot = -Infinity;
            for (let i = 0; i <= numX; i++) {
                for (let j = 0; j <= numY; j++) {
                    if (!isNaN(potentials[i][j])) {
                        minPot = Math.min(minPot, potentials[i][j]);
                        maxPot = Math.max(maxPot, potentials[i][j]);
                    }
                }
            }
            if (minPot === Infinity || maxPot === -Infinity || minPot === maxPot) return;

            const numLevels = 10; // Number of equipotential lines
            const potentialLevels = [];
            // Create more levels around zero
            const range = Math.max(Math.abs(minPot), Math.abs(maxPot));
            for(let i = 1; i <= numLevels/2; i++) {
                potentialLevels.push( (i / (numLevels/2)) * range );
                potentialLevels.push( -(i / (numLevels/2)) * range );
            }
            potentialLevels.push(0); // Zero potential line
            potentialLevels.sort((a,b) => a-b); // Ensure sorted

            const uniqueLevels = [...new Set(potentialLevels.map(p => p.toFixed(1)))].map(parseFloat);


            ctx.lineWidth = 1.5;

            uniqueLevels.forEach(level => {
                // Color mapping for equipotentials
                if (level > 0) {
                    const intensity = Math.min(255, Math.floor(50 + 205 * (level / maxPot)));
                    ctx.strokeStyle = `rgb(${intensity},${Math.floor(intensity/2)},${Math.floor(intensity/3)})`; // Reddish
                } else if (level < 0) {
                    const intensity = Math.min(255, Math.floor(50 + 205 * (level / minPot))); // minPot is negative
                     ctx.strokeStyle = `rgb(${Math.floor(intensity/3)},${Math.floor(intensity/2)},${intensity})`; // Bluish
                } else {
                    ctx.strokeStyle = 'grey'; // Zero potential
                }

                for (let i = 0; i < numX; i++) {
                    for (let j = 0; j < numY; j++) {
                        const cellPotentials = [
                            potentials[i][j],     // Top-left
                            potentials[i+1][j],   // Top-right
                            potentials[i+1][j+1], // Bottom-right
                            potentials[i][j+1]    // Bottom-left
                        ];
                        
                        const cellCorners = [
                            { x: i * gridStep, y: j * gridStep },
                            { x: (i + 1) * gridStep, y: j * gridStep },
                            { x: (i + 1) * gridStep, y: (j + 1) * gridStep },
                            { x: i * gridStep, y: (j + 1) * gridStep }
                        ];

                        let caseIndex = 0;
                        if (cellPotentials[0] > level) caseIndex |= 1;
                        if (cellPotentials[1] > level) caseIndex |= 2;
                        if (cellPotentials[2] > level) caseIndex |= 4;
                        if (cellPotentials[3] > level) caseIndex |= 8;

                        if (caseIndex === 0 || caseIndex === 15) continue; // All below or all above

                        const interpolate = (p1, p2, v1, v2, targetVal) => {
                            if (Math.abs(v1 - v2) < 1e-6) return p1; // Avoid division by zero if potentials are too close
                            const t = (targetVal - v1) / (v2 - v1);
                            return { x: p1.x + t * (p2.x - p1.x), y: p1.y + t * (p2.y - p1.y) };
                        };
                        
                        const points = [];
                        // Top edge: P0-P1
                        if ((caseIndex & 1) !== (caseIndex >> 1 & 1)) {
                            points.push(interpolate(cellCorners[0], cellCorners[1], cellPotentials[0], cellPotentials[1], level));
                        }
                        // Right edge: P1-P2
                        if ((caseIndex >> 1 & 1) !== (caseIndex >> 2 & 1)) {
                            points.push(interpolate(cellCorners[1], cellCorners[2], cellPotentials[1], cellPotentials[2], level));
                        }
                        // Bottom edge: P2-P3 (reversed for winding)
                        if ((caseIndex >> 2 & 1) !== (caseIndex >> 3 & 1)) {
                             points.push(interpolate(cellCorners[3], cellCorners[2], cellPotentials[3], cellPotentials[2], level)); // P3 to P2
                        }
                        // Left edge: P3-P0 (reversed for winding)
                        if ((caseIndex >> 3 & 1) !== (caseIndex & 1)) {
                            points.push(interpolate(cellCorners[0], cellCorners[3], cellPotentials[0], cellPotentials[3], level)); // P0 to P3
                        }
                        
                        // Marching squares cases (simplified to drawing segments between valid interpolated points)
                        // This simplified version directly connects points found on edges.
                        // For a robust Marching Squares, you'd use a lookup table for the 16 cases.
                        // This version might draw multiple small segments per cell instead of one or two longer ones.
                        // A common simple approach is to connect points in order of edges checked.
                        
                        // Connect points for this cell based on standard Marching Squares line drawing logic
                        ctx.beginPath();
                        switch (caseIndex) {
                            case 1: case 14: // P0 isolated
                                ctx.moveTo(interpolate(cellCorners[0], cellCorners[1], cellPotentials[0], cellPotentials[1], level).x, interpolate(cellCorners[0], cellCorners[1], cellPotentials[0], cellPotentials[1], level).y);
                                ctx.lineTo(interpolate(cellCorners[0], cellCorners[3], cellPotentials[0], cellPotentials[3], level).x, interpolate(cellCorners[0], cellCorners[3], cellPotentials[0], cellPotentials[3], level).y);
                                break;
                            case 2: case 13: // P1 isolated
                                ctx.moveTo(interpolate(cellCorners[0], cellCorners[1], cellPotentials[0], cellPotentials[1], level).x, interpolate(cellCorners[0], cellCorners[1], cellPotentials[0], cellPotentials[1], level).y);
                                ctx.lineTo(interpolate(cellCorners[1], cellCorners[2], cellPotentials[1], cellPotentials[2], level).x, interpolate(cellCorners[1], cellCorners[2], cellPotentials[1], cellPotentials[2], level).y);
                                break;
                            case 3: case 12: // P0, P1 together
                                ctx.moveTo(interpolate(cellCorners[0], cellCorners[3], cellPotentials[0], cellPotentials[3], level).x, interpolate(cellCorners[0], cellCorners[3], cellPotentials[0], cellPotentials[3], level).y);
                                ctx.lineTo(interpolate(cellCorners[1], cellCorners[2], cellPotentials[1], cellPotentials[2], level).x, interpolate(cellCorners[1], cellCorners[2], cellPotentials[1], cellPotentials[2], level).y);
                                break;
                            case 4: case 11: // P2 isolated
                                ctx.moveTo(interpolate(cellCorners[1], cellCorners[2], cellPotentials[1], cellPotentials[2], level).x, interpolate(cellCorners[1], cellCorners[2], cellPotentials[1], cellPotentials[2], level).y);
                                ctx.lineTo(interpolate(cellCorners[2], cellCorners[3], cellPotentials[2], cellPotentials[3], level).x, interpolate(cellCorners[2], cellCorners[3], cellPotentials[2], cellPotentials[3], level).y);
                                break;
                            case 5: case 10: // P0, P2 diagonal or P1, P3 diagonal (saddle point)
                                // This is the ambiguous case. For simplicity, draw two separate lines.
                                // Or pick one standard connection (e.g. (P0-Ptop, Pleft-Pbottom) or (Ptop-Pright, Pbottom-Pleft))
                                // Simple connection: Top to Left, Right to Bottom
                                ctx.moveTo(interpolate(cellCorners[0], cellCorners[1], cellPotentials[0], cellPotentials[1], level).x, interpolate(cellCorners[0], cellCorners[1], cellPotentials[0], cellPotentials[1], level).y);
                                ctx.lineTo(interpolate(cellCorners[0], cellCorners[3], cellPotentials[0], cellPotentials[3], level).x, interpolate(cellCorners[0], cellCorners[3], cellPotentials[0], cellPotentials[3], level).y);
                                ctx.stroke(); // First segment
                                ctx.beginPath();
                                ctx.moveTo(interpolate(cellCorners[1], cellCorners[2], cellPotentials[1], cellPotentials[2], level).x, interpolate(cellCorners[1], cellCorners[2], cellPotentials[1], cellPotentials[2], level).y);
                                ctx.lineTo(interpolate(cellCorners[2], cellCorners[3], cellPotentials[2], cellPotentials[3], level).x, interpolate(cellCorners[2], cellCorners[3], cellPotentials[2], cellPotentials[3], level).y);
                                break;
                            case 6: case 9: // P1, P2 together
                                ctx.moveTo(interpolate(cellCorners[0], cellCorners[1], cellPotentials[0], cellPotentials[1], level).x, interpolate(cellCorners[0], cellCorners[1], cellPotentials[0], cellPotentials[1], level).y);
                                ctx.lineTo(interpolate(cellCorners[2], cellCorners[3], cellPotentials[2], cellPotentials[3], level).x, interpolate(cellCorners[2], cellCorners[3], cellPotentials[2], cellPotentials[3], level).y);
                                break;
                            case 7: case 8: // P3 isolated (or P0,P1,P2 together)
                                ctx.moveTo(interpolate(cellCorners[0], cellCorners[3], cellPotentials[0], cellPotentials[3], level).x, interpolate(cellCorners[0], cellCorners[3], cellPotentials[0], cellPotentials[3], level).y);
                                ctx.lineTo(interpolate(cellCorners[2], cellCorners[3], cellPotentials[2], cellPotentials[3], level).x, interpolate(cellCorners[2], cellCorners[3], cellPotentials[2], cellPotentials[3], level).y);
                                break;
                        }
                        ctx.stroke();
                    }
                }
            });
        }


        function drawSensor() {
            if (!sensor.visible) {
                potentialDisplay.textContent = 'Potential at sensor: N/A (Sensor Off)';
                return;
            }
            ctx.beginPath();
            ctx.arc(sensor.x, sensor.y, sensor.radius, 0, 2 * Math.PI);
            ctx.fillStyle = 'gold';
            ctx.fill();
            ctx.strokeStyle = 'black';
            ctx.lineWidth = 1;
            ctx.stroke();

            const potential = calculatePotentialAt(sensor.x, sensor.y);
            potentialDisplay.textContent = `Potential at sensor: ${potential.toFixed(2)} V (scaled)`;
        }

        function drawAll() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            if (showEquipotentials) {
                drawEquipotentials();
            }
            if (showFieldLines) {
                drawFieldLines();
            }
            drawCharges();
            if (sensor.visible) {
                drawSensor();
            }
        }

        // Event Listeners
        document.getElementById('addPositive').addEventListener('click', () => addCharge('positive'));
        document.getElementById('addNegative').addEventListener('click', () => addCharge('negative'));
        document.getElementById('removeSelected').addEventListener('click', removeSelectedCharge);
        document.getElementById('clearAll').addEventListener('click', clearAllCharges);
        
        document.getElementById('toggleSensor').addEventListener('change', (e) => {
            sensor.visible = e.target.value === 'on';
            if (!sensor.visible) {
                 potentialDisplay.textContent = 'Potential at sensor: N/A (Sensor Off)';
            }
            drawAll();
        });
        document.getElementById('toggleFieldLines').addEventListener('change', (e) => {
            showFieldLines = e.target.value === 'on';
            drawAll();
        });
        document.getElementById('toggleEquipotentials').addEventListener('change', (e) => {
            showEquipotentials = e.target.value === 'on';
            drawAll();
        });

        canvas.addEventListener('mousedown', handleMouseDown);
        canvas.addEventListener('mousemove', handleMouseMove);
        canvas.addEventListener('mouseup', handleMouseUp);
        canvas.addEventListener('mouseleave', handleMouseUp); // Stop dragging if mouse leaves canvas

        // Touch events
        canvas.addEventListener('touchstart', handleMouseDown);
        canvas.addEventListener('touchmove', handleMouseMove);
        canvas.addEventListener('touchend', handleMouseUp);
        canvas.addEventListener('touchcancel', handleMouseUp);

        window.addEventListener('resize', resizeCanvas);

        // Initial setup
        resizeCanvas(); // Set initial canvas size and draw
        // Place initial sensor to a default location
        sensor.x = canvas.width / 4;
        sensor.y = canvas.height / 2;
        drawAll();

    </script>
</body>
</html>
