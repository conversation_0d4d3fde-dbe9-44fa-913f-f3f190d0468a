// Physics Virtual Lab - Theme Management

export class ThemeManager {
  constructor() {
    this.currentTheme = this.getStoredTheme() || this.getSystemTheme();
    this.listeners = [];
  }

  init() {
    this.applyTheme(this.currentTheme);
    this.setupSystemThemeListener();
  }

  getSystemTheme() {
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return 'dark';
    }
    return 'light';
  }

  getStoredTheme() {
    try {
      return localStorage.getItem('physics-lab-theme');
    } catch (e) {
      console.warn('Unable to access localStorage for theme preference');
      return null;
    }
  }

  setStoredTheme(theme) {
    try {
      localStorage.setItem('physics-lab-theme', theme);
    } catch (e) {
      console.warn('Unable to save theme preference to localStorage');
    }
  }

  applyTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    document.body.className = document.body.className.replace(/theme-\w+/g, '');
    document.body.classList.add(`theme-${theme}`);
    
    // Update meta theme-color for mobile browsers
    this.updateMetaThemeColor(theme);
    
    // Notify listeners
    this.notifyListeners(theme);
  }

  updateMetaThemeColor(theme) {
    let metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (!metaThemeColor) {
      metaThemeColor = document.createElement('meta');
      metaThemeColor.name = 'theme-color';
      document.head.appendChild(metaThemeColor);
    }
    
    const colors = {
      light: '#ffffff',
      dark: '#1f2937'
    };
    
    metaThemeColor.content = colors[theme] || colors.light;
  }

  toggle() {
    const newTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
    this.setTheme(newTheme);
  }

  setTheme(theme) {
    if (theme !== 'light' && theme !== 'dark') {
      console.warn(`Invalid theme: ${theme}. Using 'light' as fallback.`);
      theme = 'light';
    }
    
    this.currentTheme = theme;
    this.applyTheme(theme);
    this.setStoredTheme(theme);
  }

  getCurrentTheme() {
    return this.currentTheme;
  }

  isDarkMode() {
    return this.currentTheme === 'dark';
  }

  setupSystemThemeListener() {
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      
      const handleSystemThemeChange = (e) => {
        // Only auto-switch if user hasn't manually set a preference
        if (!this.getStoredTheme()) {
          const systemTheme = e.matches ? 'dark' : 'light';
          this.setTheme(systemTheme);
        }
      };

      // Modern browsers
      if (mediaQuery.addEventListener) {
        mediaQuery.addEventListener('change', handleSystemThemeChange);
      } else {
        // Fallback for older browsers
        mediaQuery.addListener(handleSystemThemeChange);
      }
    }
  }

  addThemeChangeListener(callback) {
    if (typeof callback === 'function') {
      this.listeners.push(callback);
    }
  }

  removeThemeChangeListener(callback) {
    const index = this.listeners.indexOf(callback);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  notifyListeners(theme) {
    this.listeners.forEach(callback => {
      try {
        callback(theme);
      } catch (e) {
        console.error('Error in theme change listener:', e);
      }
    });
  }

  // Utility methods for theme-aware styling
  getThemeColors() {
    const colors = {
      light: {
        primary: '#3b82f6',
        secondary: '#0ea5e9',
        accent: '#8b5cf6',
        background: '#ffffff',
        surface: '#f9fafb',
        text: '#111827',
        textSecondary: '#6b7280',
        border: '#e5e7eb'
      },
      dark: {
        primary: '#60a5fa',
        secondary: '#38bdf8',
        accent: '#a78bfa',
        background: '#111827',
        surface: '#1f2937',
        text: '#f9fafb',
        textSecondary: '#d1d5db',
        border: '#374151'
      }
    };
    
    return colors[this.currentTheme] || colors.light;
  }

  // CSS custom property helpers
  setCSSProperty(property, value) {
    document.documentElement.style.setProperty(property, value);
  }

  getCSSProperty(property) {
    return getComputedStyle(document.documentElement).getPropertyValue(property);
  }

  // Animation helpers for theme transitions
  animateThemeTransition() {
    document.body.style.transition = 'background-color 0.3s ease, color 0.3s ease';
    
    // Remove transition after animation completes
    setTimeout(() => {
      document.body.style.transition = '';
    }, 300);
  }

  // Accessibility helpers
  updateAriaLabels() {
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
      const label = this.isDarkMode() ? 'Switch to light mode' : 'Switch to dark mode';
      themeToggle.setAttribute('aria-label', label);
    }
  }

  // Debug helpers
  logThemeInfo() {
    console.group('Theme Manager Info');
    console.log('Current theme:', this.currentTheme);
    console.log('Stored theme:', this.getStoredTheme());
    console.log('System theme:', this.getSystemTheme());
    console.log('Theme colors:', this.getThemeColors());
    console.groupEnd();
  }

  // Reset to system default
  resetToSystemTheme() {
    try {
      localStorage.removeItem('physics-lab-theme');
    } catch (e) {
      console.warn('Unable to remove theme preference from localStorage');
    }
    
    const systemTheme = this.getSystemTheme();
    this.setTheme(systemTheme);
  }

  // Export theme configuration
  exportThemeConfig() {
    return {
      current: this.currentTheme,
      stored: this.getStoredTheme(),
      system: this.getSystemTheme(),
      colors: this.getThemeColors()
    };
  }

  // Import theme configuration
  importThemeConfig(config) {
    if (config && config.current) {
      this.setTheme(config.current);
    }
  }
}
