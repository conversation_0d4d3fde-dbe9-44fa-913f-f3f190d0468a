// Physics Virtual Lab - Main JavaScript Module

// Import utilities
import { ThemeManager } from './theme.js';
import { Utils } from './utils.js';

class PhysicsLabApp {
  constructor() {
    this.themeManager = new ThemeManager();
    this.utils = new Utils();
    this.currentPage = this.getCurrentPage();
    
    this.init();
  }

  init() {
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.onDOMReady());
    } else {
      this.onDOMReady();
    }
  }

  onDOMReady() {
    this.renderNavigation();
    this.renderFooter();
    this.setupEventListeners();
    this.initializeAnimations();
    this.themeManager.init();
    
    // Page-specific initialization
    this.initializePage();
  }

  getCurrentPage() {
    const path = window.location.pathname;
    const filename = path.split('/').pop() || 'index.html';
    return filename.replace('.html', '') || 'index';
  }

  renderNavigation() {
    const header = document.getElementById('main-header');
    if (!header) return;

    const navHTML = `
      <nav class="navbar">
        <div class="navbar-container">
          <a href="index.html" class="navbar-brand">
            <span>🧪</span>
            Physics Virtual Lab
          </a>
          
          <div class="navbar-nav-container">
            <ul class="navbar-nav">
              <li><a href="index.html" class="${this.currentPage === 'index' ? 'active' : ''}">Home</a></li>
              <li><a href="dashboard.html" class="${this.currentPage === 'dashboard' ? 'active' : ''}">Dashboard</a></li>
              <li><a href="learning-paths.html" class="${this.currentPage === 'learning-paths' ? 'active' : ''}">Learning Paths</a></li>
              <li class="nav-dropdown">
                <a href="#" class="nav-dropdown-toggle">Experiments</a>
                <ul class="nav-dropdown-menu">
                  <li><a href="experiments/ohms-law.html">Ohm's Law</a></li>
                  <li><a href="experiments/pendulum-lab.html">Pendulum Lab</a></li>
                  <li><a href="experiments/circuit-simulator.html">Circuit Simulator</a></li>
                  <li><a href="experiments/projectile-motion.html">Projectile Motion</a></li>
                  <li><a href="experiments/science-simulations.html">Science Simulations</a></li>
                </ul>
              </li>
              <li class="nav-dropdown">
                <a href="#" class="nav-dropdown-toggle">Videos</a>
                <ul class="nav-dropdown-menu">
                  <li><a href="video-library.html">Video Library</a></li>
                  <li><a href="interactive-video.html">Interactive Player</a></li>
                  <li><a href="video-studio.html">Create Videos</a></li>
                </ul>
              </li>
              <li><a href="assessment.html" class="${this.currentPage === 'assessment' ? 'active' : ''}">Assessment</a></li>
            </ul>
            
            <div class="navbar-actions">
              <button id="theme-toggle" class="theme-toggle" aria-label="Toggle theme">
                <span class="theme-icon">🌙</span>
              </button>
              <button id="mobile-menu-toggle" class="mobile-menu-toggle">
                <span></span>
                <span></span>
                <span></span>
              </button>
            </div>
          </div>
        </div>
      </nav>
    `;

    header.innerHTML = navHTML;
    this.setupNavigationEvents();
  }

  renderFooter() {
    const footer = document.getElementById('main-footer-container');
    if (!footer) return;

    const footerHTML = `
      <footer class="footer">
        <div class="container">
          <div class="footer-content">
            <div class="footer-section">
              <h3>Physics Virtual Lab</h3>
              <p>Advancing physics education through interactive simulations and virtual experiments.</p>
              <div class="social-links">
                <a href="#" aria-label="GitHub"><span>📚</span></a>
                <a href="#" aria-label="Documentation"><span>📖</span></a>
                <a href="#" aria-label="Support"><span>💬</span></a>
              </div>
            </div>
            
            <div class="footer-section">
              <h3>Experiments</h3>
              <ul>
                <li><a href="experiments/circuit-simulator.html">Circuit Simulator</a></li>
                <li><a href="experiments/ohms-law.html">Ohm's Law</a></li>
                <li><a href="experiments/pendulum-lab.html">Pendulum Lab</a></li>
                <li><a href="#coming-soon">Wave Mechanics</a></li>
              </ul>
            </div>
            
            <div class="footer-section">
              <h3>Resources</h3>
              <ul>
                <li><a href="dashboard.html">Dashboard</a></li>
                <li><a href="#tutorials">Tutorials</a></li>
                <li><a href="#documentation">Documentation</a></li>
                <li><a href="#support">Support</a></li>
              </ul>
            </div>
            
            <div class="footer-section">
              <h3>About</h3>
              <ul>
                <li><a href="#about">About Us</a></li>
                <li><a href="#contact">Contact</a></li>
                <li><a href="#privacy">Privacy Policy</a></li>
                <li><a href="#terms">Terms of Service</a></li>
              </ul>
            </div>
          </div>
          
          <div class="footer-bottom">
            <p>&copy; ${new Date().getFullYear()} Physics Virtual Lab. All rights reserved.</p>
            <p><strong>Author:</strong> Dr. Mohammed Yagoub Esmail, SUST-BME</p>
            <p><strong>Contact:</strong> +249912867327, +966538076790</p>
            <p><strong>Institution:</strong> Sudan University of Science and Technology - Biomedical Engineering</p>
            <p>Built with ❤️ for physics education</p>
          </div>
        </div>
      </footer>
    `;

    footer.innerHTML = footerHTML;
  }

  setupNavigationEvents() {
    // Theme toggle
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
      themeToggle.addEventListener('click', () => {
        this.themeManager.toggle();
        this.updateThemeIcon();
      });
    }

    // Mobile menu toggle
    const mobileToggle = document.getElementById('mobile-menu-toggle');
    if (mobileToggle) {
      mobileToggle.addEventListener('click', () => {
        this.toggleMobileMenu();
      });
    }

    // Dropdown menus
    const dropdowns = document.querySelectorAll('.nav-dropdown');
    dropdowns.forEach(dropdown => {
      const toggle = dropdown.querySelector('.nav-dropdown-toggle');
      const menu = dropdown.querySelector('.nav-dropdown-menu');
      
      if (toggle && menu) {
        toggle.addEventListener('click', (e) => {
          e.preventDefault();
          dropdown.classList.toggle('active');
        });
      }
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', (e) => {
      if (!e.target.closest('.nav-dropdown')) {
        document.querySelectorAll('.nav-dropdown.active').forEach(dropdown => {
          dropdown.classList.remove('active');
        });
      }
    });
  }

  setupEventListeners() {
    // Smooth scrolling for anchor links
    document.addEventListener('click', (e) => {
      if (e.target.matches('a[href^="#"]')) {
        e.preventDefault();
        const targetId = e.target.getAttribute('href').substring(1);
        const targetElement = document.getElementById(targetId);
        
        if (targetElement) {
          targetElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      }
    });

    // Handle window resize
    window.addEventListener('resize', this.utils.debounce(() => {
      this.handleResize();
    }, 250));

    // Handle scroll events
    window.addEventListener('scroll', this.utils.throttle(() => {
      this.handleScroll();
    }, 16));

    // Handle keyboard navigation
    document.addEventListener('keydown', (e) => {
      this.handleKeyboard(e);
    });
  }

  initializeAnimations() {
    // Initialize scroll reveal animations
    this.setupScrollReveal();
    
    // Initialize page entrance animations
    this.animatePageEntrance();
  }

  setupScrollReveal() {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('revealed');
          observer.unobserve(entry.target);
        }
      });
    }, observerOptions);

    // Observe elements with scroll-reveal class
    document.querySelectorAll('.scroll-reveal').forEach(el => {
      observer.observe(el);
    });
  }

  animatePageEntrance() {
    // Add entrance animations to main content
    const mainContent = document.querySelector('main');
    if (mainContent) {
      mainContent.classList.add('animate-fade-in');
    }

    // Stagger animations for cards and grid items
    const cards = document.querySelectorAll('.card, .quick-access-card, .experiment-card');
    cards.forEach((card, index) => {
      card.style.animationDelay = `${index * 0.1}s`;
      card.classList.add('animate-slide-up');
    });
  }

  updateThemeIcon() {
    const themeIcon = document.querySelector('.theme-icon');
    if (themeIcon) {
      const isDark = this.themeManager.getCurrentTheme() === 'dark';
      themeIcon.textContent = isDark ? '☀️' : '🌙';
    }
  }

  toggleMobileMenu() {
    const navbar = document.querySelector('.navbar');
    const toggle = document.getElementById('mobile-menu-toggle');
    
    if (navbar && toggle) {
      navbar.classList.toggle('mobile-open');
      toggle.classList.toggle('active');
    }
  }

  handleResize() {
    // Handle responsive behavior
    const navbar = document.querySelector('.navbar');
    if (window.innerWidth > 768 && navbar) {
      navbar.classList.remove('mobile-open');
      const toggle = document.getElementById('mobile-menu-toggle');
      if (toggle) {
        toggle.classList.remove('active');
      }
    }
  }

  handleScroll() {
    // Handle navbar background on scroll
    const navbar = document.querySelector('.navbar');
    if (navbar) {
      if (window.scrollY > 50) {
        navbar.classList.add('scrolled');
      } else {
        navbar.classList.remove('scrolled');
      }
    }

    // Update scroll progress
    this.updateScrollProgress();
  }

  updateScrollProgress() {
    const scrollProgress = document.querySelector('.scroll-progress');
    if (scrollProgress) {
      const scrollPercent = (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100;
      scrollProgress.style.width = `${Math.min(scrollPercent, 100)}%`;
    }
  }

  handleKeyboard(e) {
    // Handle keyboard shortcuts
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 'k':
          e.preventDefault();
          // Open search (if implemented)
          break;
        case 'd':
          e.preventDefault();
          this.themeManager.toggle();
          this.updateThemeIcon();
          break;
      }
    }

    // Handle escape key
    if (e.key === 'Escape') {
      // Close modals, dropdowns, etc.
      document.querySelectorAll('.nav-dropdown.active').forEach(dropdown => {
        dropdown.classList.remove('active');
      });
    }
  }

  initializePage() {
    // Page-specific initialization
    switch (this.currentPage) {
      case 'index':
        this.initializeHomePage();
        break;
      case 'dashboard':
        this.initializeDashboard();
        break;
      default:
        this.initializeExperimentPage();
        break;
    }
  }

  initializeHomePage() {
    // Initialize hero animations
    const heroElements = document.querySelectorAll('.hero h1, .hero p, .hero .btn');
    heroElements.forEach((el, index) => {
      el.style.animationDelay = `${index * 0.2}s`;
      el.classList.add('animate-slide-up');
    });

    // Initialize floating particles (if desired)
    this.createFloatingParticles();
  }

  initializeDashboard() {
    // Dashboard-specific initialization will be handled by dashboard.js
    console.log('Dashboard page initialized');
  }

  initializeExperimentPage() {
    // Common experiment page initialization
    console.log('Experiment page initialized');
  }

  createFloatingParticles() {
    const hero = document.querySelector('.hero');
    if (!hero) return;

    for (let i = 0; i < 5; i++) {
      const particle = document.createElement('div');
      particle.className = 'physics-particle';
      particle.style.left = `${Math.random() * 100}%`;
      particle.style.top = `${Math.random() * 100}%`;
      particle.style.animationDelay = `${Math.random() * 2}s`;
      particle.style.animationDuration = `${2 + Math.random() * 2}s`;
      hero.appendChild(particle);
    }
  }

  // Public API methods
  showNotification(message, type = 'info') {
    this.utils.showNotification(message, type);
  }

  showLoading(show = true) {
    this.utils.showLoading(show);
  }
}

// Initialize the application
const app = new PhysicsLabApp();

// Export for use in other modules
export default app;
