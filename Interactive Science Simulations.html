<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Science Simulations</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            margin: 0;
            padding: 0;
            background-color: #f4f4f9;
            color: #333;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        header {
            background-color: #007bff;
            color: white;
            padding: 1rem;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        header h1 {
            margin: 0;
            font-size: 1.8rem;
        }

        nav#domain-selection {
            background-color: #e9ecef;
            padding: 1rem;
            text-align: center;
            border-bottom: 1px solid #ced4da;
        }

        nav#domain-selection button,
        #topic-buttons button {
            background-color: #6c757d;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            margin: 0.5rem;
            border-radius: 0.25rem;
            cursor: pointer;
            font-size: 1rem;
            transition: background-color 0.2s ease-in-out;
        }

        nav#domain-selection button:hover,
        #topic-buttons button:hover {
            background-color: #5a6268;
        }

        nav#domain-selection button.active,
        #topic-buttons button.active {
            background-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }

        main {
            flex-grow: 1;
            padding: 1rem;
            max-width: 900px;
            margin: 0 auto;
            width: 100%;
            box-sizing: border-box;
        }

        section {
            background-color: white;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-radius: 0.3rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
        }

        .hidden {
            display: none;
        }

        h2, h3 {
            color: #007bff;
            margin-top: 0;
        }

        #simulation-canvas-area {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 1rem;
            background: #f0f0f0;
            border: 1px solid #ccc;
            border-radius: 4px;
            overflow: hidden; /* Ensures canvas corners are rounded if canvas itself is not */
        }

        #simulation-canvas {
            background-color: white;
            border-radius: 4px; /* If canvas background is different from area */
        }
        
        #simulation-controls {
            display: grid;
            gap: 1rem;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        }

        .control-group {
            display: flex;
            flex-direction: column;
        }

        .control-group label {
            margin-bottom: 0.25rem;
            font-weight: bold;
            font-size: 0.9rem;
        }
        
        .control-group input[type="range"] {
            width: 100%;
            cursor: pointer;
        }
        
        .control-group .value-display {
            font-size: 0.85rem;
            color: #555;
            min-width: 40px; /* Ensure space for units */
            text-align: right;
        }
        
        .param-label-line {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        #expectation-area textarea {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ced4da;
            border-radius: 0.25rem;
            box-sizing: border-box;
            margin-bottom: 0.5rem;
            font-family: inherit;
            font-size: 0.95rem;
        }

        #expectation-area button {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 0.6rem 1.2rem;
            border-radius: 0.25rem;
            cursor: pointer;
            font-size: 1rem;
            transition: background-color 0.2s ease-in-out;
        }

        #expectation-area button:hover {
            background-color: #218838;
        }

        footer {
            background-color: #343a40;
            color: #f8f9fa;
            text-align: center;
            padding: 1rem;
            font-size: 0.85rem;
            margin-top: auto; /* Pushes footer to bottom */
        }
        footer p {
            margin: 0;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            header h1 {
                font-size: 1.5rem;
            }
            nav#domain-selection button,
            #topic-buttons button {
                padding: 0.6rem 1rem;
                font-size: 0.9rem;
            }
            #simulation-controls {
                grid-template-columns: 1fr; /* Single column on smaller screens */
            }
        }
    </style>
</head>
<body>
    <header>
        <h1>Interactive Science Simulations</h1>
    </header>

    <nav id="domain-selection">
        <!-- Domain buttons will be injected here -->
    </nav>

    <main>
        <section id="topic-selection-container" class="hidden">
            <h2>Select a Topic</h2>
            <div id="topic-buttons">
                <!-- Topic buttons will be injected here -->
            </div>
        </section>

        <section id="simulation-container" class="hidden">
            <h2 id="simulation-title">Simulation</h2>
            <div id="simulation-canvas-area">
                <canvas id="simulation-canvas"></canvas>
            </div>
            <div id="simulation-controls">
                <!-- Sliders and labels will be injected here -->
            </div>
        </section>

        <section id="expectation-area">
            <h3>What do you expect to happen?</h3>
            <textarea id="expectation-textbox" rows="4" placeholder="Describe your prediction before running or changing the simulation..."></textarea>
            <button id="submit-expectation">Submit Expectation</button>
        </section>
    </main>

    <footer>
        <p>This app is for educational purposes. Please review the licensing terms of any included simulation before use, and provide appropriate attribution.</p>
    </footer>

    <script>
        const CANVAS_WIDTH = 500;
        const CANVAS_HEIGHT = 300;

        // Global state
        let currentDomainKey = null;
        let currentTopicKey = null;
        let currentSimConfig = null;
        let currentAnimationId = null;
        let particleSystem = { particles: [] }; // For Gas Diffusion

        const scientificContent = {
            Physics: {
                topics: {
                    "Projectile Motion": {
                        description: "Observe the path of a projectile based on its initial velocity and launch angle.",
                        simulation: {
                            id: "projectileMotion",
                            parameters: [
                                { name: "Initial Velocity", id: "velocity", min: 10, max: 100, value: 50, unit: "m/s" },
                                { name: "Launch Angle", id: "angle", min: 0, max: 90, value: 45, unit: "°" }
                            ],
                            drawFunction: 'drawProjectileMotion',
                        }
                    },
                    "Ohm's Law": {
                        description: "Explore the relationship between voltage, current, and resistance in a simple circuit.",
                        simulation: {
                            id: "ohmsLaw",
                            parameters: [
                                { name: "Voltage", id: "voltage", min: 1, max: 24, value: 9, unit: "V" },
                                { name: "Resistance", id: "resistance", min: 10, max: 200, value: 100, unit: "Ω" }
                            ],
                            drawFunction: 'drawOhmsLaw',
                        }
                    }
                }
            },
            Chemistry: {
                topics: {
                    "Gas Diffusion": {
                        description: "Simulate the mixing of two types of gas particles.",
                        simulation: {
                            id: "gasDiffusion",
                            parameters: [
                                { name: "Particles Type A", id: "particlesA", min: 5, max: 50, value: 25, unit: "count" },
                                { name: "Particles Type B", id: "particlesB", min: 5, max: 50, value: 25, unit: "count" }
                            ],
                            initFunction: 'initGasDiffusion',
                            drawFunction: 'drawGasDiffusion', // Called by initFunction's loop
                            isAnimated: true
                        }
                    }
                }
            },
            Biology: {
                topics: {
                    "Population Growth": {
                        description: "Model logistic population growth with varying initial population and growth rate (carrying capacity K is fixed).",
                        simulation: {
                            id: "populationGrowth",
                            parameters: [
                                { name: "Initial Population (N₀)", id: "initialPop", min: 2, max: 200, value: 20 },
                                { name: "Growth Rate (r)", id: "growthRate", min: 0.01, max: 0.5, value: 0.1, step: 0.01 }
                            ],
                            drawFunction: 'drawPopulationGrowth',
                        }
                    }
                }
            },
            "Earth Science": {
                topics: {
                    "Simple Plate Movement": {
                        description: "Visualize how two tectonic plates move relative to each other.",
                        simulation: {
                            id: "plateTectonics",
                            parameters: [
                                { name: "Plate 1 Speed", id: "plate1Speed", min: -5, max: 5, value: 1, step: 0.5, unit: "cm/yr" },
                                { name: "Plate 2 Speed", id: "plate2Speed", min: -5, max: 5, value: -1, step: 0.5, unit: "cm/yr" }
                            ],
                            drawFunction: 'drawPlateTectonics',
                        }
                    }
                }
            },
            Math: {
                topics: {
                    "Circle Properties": {
                        description: "Explore how a circle's area changes with its radius and see its outline.",
                        simulation: {
                            id: "circleProperties",
                            parameters: [
                                { name: "Radius", id: "radius", min: 10, max: 100, value: 50, unit: "px" },
                                { name: "Outline Thickness", id: "outline", min: 1, max: 10, value: 3, unit: "px" }
                            ],
                            drawFunction: 'drawCircleProperties',
                        }
                    }
                }
            }
        };

        // --- UI RENDERING FUNCTIONS ---
        function renderDomainButtons() {
            const container = document.getElementById('domain-selection');
            container.innerHTML = '';
            Object.keys(scientificContent).forEach(domainKey => {
                const button = document.createElement('button');
                button.textContent = domainKey;
                button.onclick = () => selectDomain(domainKey);
                button.setAttribute('aria-label', `Select domain: ${domainKey}`);
                container.appendChild(button);
            });
        }

        function selectDomain(domainKey) {
            currentDomainKey = domainKey;
            currentTopicKey = null;
            currentSimConfig = null;

            // Update active button style for domains
            const domainButtons = document.querySelectorAll('#domain-selection button');
            domainButtons.forEach(btn => {
                if (btn.textContent === domainKey) {
                    btn.classList.add('active');
                    btn.setAttribute('aria-pressed', 'true');
                } else {
                    btn.classList.remove('active');
                    btn.setAttribute('aria-pressed', 'false');
                }
            });

            document.getElementById('simulation-container').classList.add('hidden');
            renderTopicButtons(domainKey);
            document.getElementById('topic-selection-container').classList.remove('hidden');
            
            // Clear any running animation
            if (currentAnimationId) {
                cancelAnimationFrame(currentAnimationId);
                currentAnimationId = null;
            }
        }

        function renderTopicButtons(domainKey) {
            const domain = scientificContent[domainKey];
            const container = document.getElementById('topic-buttons');
            container.innerHTML = '';
            if (domain && domain.topics) {
                Object.keys(domain.topics).forEach(topicKey => {
                    const button = document.createElement('button');
                    button.textContent = topicKey;
                    button.onclick = () => selectTopic(domainKey, topicKey);
                    button.setAttribute('aria-label', `Select topic: ${topicKey}`);
                    container.appendChild(button);
                });
            }
        }

        function selectTopic(domainKey, topicKey) {
            currentTopicKey = topicKey;
            const topic = scientificContent[domainKey].topics[topicKey];
            currentSimConfig = topic.simulation;

            // Update active button style for topics
            const topicButtons = document.querySelectorAll('#topic-buttons button');
            topicButtons.forEach(btn => {
                if (btn.textContent === topicKey) {
                    btn.classList.add('active');
                     btn.setAttribute('aria-pressed', 'true');
                } else {
                    btn.classList.remove('active');
                    btn.setAttribute('aria-pressed', 'false');
                }
            });

            document.getElementById('simulation-title').textContent = topicKey;
            renderSimulationUI(currentSimConfig);
            document.getElementById('simulation-container').classList.remove('hidden');
            updateSimulation();
        }

        function renderSimulationUI(simConfig) {
            const controlsContainer = document.getElementById('simulation-controls');
            controlsContainer.innerHTML = '';
            const canvas = document.getElementById('simulation-canvas');
            canvas.width = CANVAS_WIDTH;
            canvas.height = CANVAS_HEIGHT;

            simConfig.parameters.forEach(param => {
                const group = document.createElement('div');
                group.classList.add('control-group');
                
                const labelLine = document.createElement('div');
                labelLine.classList.add('param-label-line');

                const label = document.createElement('label');
                label.setAttribute('for', param.id);
                label.textContent = `${param.name}: `;
                
                const valueDisplay = document.createElement('span');
                valueDisplay.id = `${param.id}-value`;
                valueDisplay.classList.add('value-display');
                valueDisplay.textContent = `${param.value} ${param.unit || ''}`;

                labelLine.appendChild(label);
                labelLine.appendChild(valueDisplay);

                const slider = document.createElement('input');
                slider.type = 'range';
                slider.id = param.id;
                slider.min = param.min;
                slider.max = param.max;
                slider.value = param.value;
                if (param.step) slider.step = param.step;
                
                slider.oninput = () => {
                    valueDisplay.textContent = `${slider.value} ${param.unit || ''}`;
                    updateSimulation();
                };
                
                group.appendChild(labelLine);
                group.appendChild(slider);
                controlsContainer.appendChild(group);
            });
        }

        function updateSimulation() {
            if (!currentSimConfig) return;

            const canvas = document.getElementById('simulation-canvas');
            const params = {};
            currentSimConfig.parameters.forEach(p => {
                const slider = document.getElementById(p.id);
                params[p.id] = parseFloat(slider.value);
            });

            // Clear previous animation if any
            if (currentAnimationId) {
                cancelAnimationFrame(currentAnimationId);
                currentAnimationId = null;
            }
            particleSystem.particles = []; // Reset particles for animated systems

            if (currentSimConfig.isAnimated && typeof window[currentSimConfig.initFunction] === 'function') {
                window[currentSimConfig.initFunction](canvas, params);
            } else if (typeof window[currentSimConfig.drawFunction] === 'function') {
                window[currentSimConfig.drawFunction](canvas, params);
            }
        }

        // --- SIMULATION DRAWING FUNCTIONS ---

        // Physics: Projectile Motion
        function drawProjectileMotion(canvas, params) {
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const g = 9.8; // gravity
            const v0 = params.velocity;
            const angleRad = params.angle * Math.PI / 180;

            const v0x = v0 * Math.cos(angleRad);
            const v0y = v0 * Math.sin(angleRad);

            // Max time and range to scale plot (simplified)
            const tMax = (2 * v0y) / g;
            const range = v0x * tMax;
            const hMax = (v0y * v0y) / (2 * g);

            // Scaling factors - adjust to fit well
            const scaleX = canvas.width / (range * 1.1 || canvas.width) ; // add some padding
            const scaleY = canvas.height / (hMax * 1.2 || canvas.height); // add some padding
            const effectiveScale = Math.min(scaleX, scaleY);


            ctx.beginPath();
            ctx.moveTo(0, canvas.height); // Start from bottom-left

            for (let t = 0; t <= tMax; t += 0.05) {
                const x = v0x * t;
                const y = v0y * t - 0.5 * g * t * t;
                if (y < 0 && t > 0.1) break; // Stop if it hits ground
                ctx.lineTo(x * effectiveScale, canvas.height - (y * effectiveScale));
            }
            ctx.strokeStyle = '#007bff';
            ctx.lineWidth = 2;
            ctx.stroke();

            // Draw axes (simple)
            ctx.beginPath();
            ctx.moveTo(0, canvas.height);
            ctx.lineTo(canvas.width, canvas.height); // X-axis
            ctx.moveTo(0, canvas.height);
            ctx.lineTo(0, 0); // Y-axis
            ctx.strokeStyle = '#ccc';
            ctx.lineWidth = 1;
            ctx.stroke();
            
            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.fillText(`Range: ${range.toFixed(1)}m, Max Height: ${hMax.toFixed(1)}m`, 10, 20);
        }

        // Physics: Ohm's Law
        function drawOhmsLaw(canvas, params) {
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const V = params.voltage;
            const R = params.resistance;
            const I = V / R; // Current

            // Simple circuit diagram
            // Battery
            ctx.fillStyle = '#6c757d';
            ctx.fillRect(50, canvas.height / 2 - 30, 20, 60); // Wider part
            ctx.fillStyle = '#adb5bd';
            ctx.fillRect(70, canvas.height / 2 - 20, 10, 40); // Narrower part
            ctx.fillStyle = '#333';
            ctx.fillText('+  -', 52, canvas.height / 2 - 35);


            // Wires
            ctx.beginPath();
            ctx.moveTo(80, canvas.height / 2);
            ctx.lineTo(150, canvas.height / 2); // to resistor
            ctx.lineTo(150, canvas.height / 2 - 50);
            ctx.lineTo(350, canvas.height / 2 - 50);
            ctx.lineTo(350, canvas.height / 2); // to bulb
            ctx.lineTo(420, canvas.height / 2); // from bulb
            ctx.lineTo(420, canvas.height / 2 + 70);
            ctx.lineTo(50, canvas.height / 2 + 70);
            ctx.lineTo(50, canvas.height / 2 + 30); // connect to battery
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // Resistor
            ctx.beginPath();
            let rx = 150, ry = canvas.height / 2;
            const rSeg = 20;
            ctx.moveTo(rx, ry);
            for (let i = 0; i < 6; i++) {
                ctx.lineTo(rx + (i * rSeg) + rSeg/2, ry + (i%2 === 0 ? -10 : 10) );
            }
            ctx.lineTo(rx + 6*rSeg, ry);
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.stroke();
            ctx.fillText(`${R} Ω`, rx + 3*rSeg - 15, ry + 25);


            // "Light bulb" as indicator of current
            const bulbX = 350, bulbY = canvas.height / 2;
            const brightness = Math.min(1, I / 0.5); // Max current for full brightness
            ctx.beginPath();
            ctx.arc(bulbX, bulbY, 20, 0, 2 * Math.PI);
            ctx.fillStyle = `rgba(255, 255, 0, ${brightness})`;
            ctx.fill();
            ctx.strokeStyle = '#6c757d';
            ctx.stroke();
            
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.fillText(`Voltage: ${V.toFixed(1)} V`, 20, 30);
            ctx.fillText(`Resistance: ${R.toFixed(0)} Ω`, 20, 50);
            ctx.fillText(`Current (I = V/R): ${I.toFixed(3)} A`, 20, 70);
        }
        
        // Chemistry: Gas Diffusion
        function initGasDiffusion(canvas, params) {
            const ctx = canvas.getContext('2d');
            particleSystem.particles = [];
            const numA = params.particlesA;
            const numB = params.particlesB;
            const radius = 5;
            const speed = 1;

            for (let i = 0; i < numA; i++) {
                particleSystem.particles.push({
                    x: Math.random() * (canvas.width / 2 - radius * 2) + radius,
                    y: Math.random() * (canvas.height - radius * 2) + radius,
                    vx: (Math.random() - 0.5) * 2 * speed,
                    vy: (Math.random() - 0.5) * 2 * speed,
                    radius: radius,
                    color: 'rgba(0, 123, 255, 0.7)' // Blue
                });
            }
            for (let i = 0; i < numB; i++) {
                particleSystem.particles.push({
                    x: Math.random() * (canvas.width / 2 - radius * 2) + canvas.width / 2 + radius,
                    y: Math.random() * (canvas.height - radius * 2) + radius,
                    vx: (Math.random() - 0.5) * 2 * speed,
                    vy: (Math.random() - 0.5) * 2 * speed,
                    radius: radius,
                    color: 'rgba(255, 0, 0, 0.7)' // Red
                });
            }
            
            // Barrier initially, then removed for diffusion
            let barrierPresent = true;
            setTimeout(() => { barrierPresent = false; }, 1000); // Remove barrier after 1s

            function diffusionLoop() {
                drawGasDiffusion(ctx, canvas, particleSystem.particles, barrierPresent);
                currentAnimationId = requestAnimationFrame(diffusionLoop);
            }
            diffusionLoop();
        }

        function drawGasDiffusion(ctx, canvas, particles, barrierPresent) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Draw barrier if present
            if (barrierPresent) {
                ctx.beginPath();
                ctx.moveTo(canvas.width / 2, 0);
                ctx.lineTo(canvas.width / 2, canvas.height);
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                ctx.stroke();
            }

            particles.forEach(p => {
                p.x += p.vx;
                p.y += p.vy;

                // Wall collisions
                if (p.x - p.radius < 0 || p.x + p.radius > canvas.width) p.vx *= -1;
                if (p.y - p.radius < 0 || p.y + p.radius > canvas.height) p.vy *= -1;

                // Barrier collision if present
                if (barrierPresent) {
                    if (p.x + p.radius > canvas.width / 2 && p.x - p.radius < canvas.width / 2) {
                         // If particle was on left and hit right side of barrier
                        if (p.vx > 0 && (p.x - p.vx - p.radius < canvas.width / 2) ) p.vx *= -1;
                        // If particle was on right and hit left side of barrier
                        if (p.vx < 0 && (p.x - p.vx + p.radius > canvas.width / 2) ) p.vx *= -1;
                    }
                }
                
                // Clamp positions to be safe
                p.x = Math.max(p.radius, Math.min(canvas.width - p.radius, p.x));
                p.y = Math.max(p.radius, Math.min(canvas.height - p.radius, p.y));


                ctx.beginPath();
                ctx.arc(p.x, p.y, p.radius, 0, Math.PI * 2);
                ctx.fillStyle = p.color;
                ctx.fill();
            });
        }

        // Biology: Population Growth
        function drawPopulationGrowth(canvas, params) {
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const N0 = params.initialPop;
            const r = params.growthRate;
            const K = 250; // Fixed Carrying Capacity for this sim
            const timeSteps = 100;

            let N_values = [];
            let N = N0;

            for (let t = 0; t < timeSteps; t++) {
                N_values.push(N);
                const dN = r * N * (1 - N / K);
                N += dN;
                if (N > K) N = K; // Cap at K
                if (N < 0) N = 0; // Floor at 0
            }

            // Draw axes
            const padding = 30;
            const graphWidth = canvas.width - 2 * padding;
            const graphHeight = canvas.height - 2 * padding;

            ctx.beginPath();
            ctx.moveTo(padding, padding); // Y-axis
            ctx.lineTo(padding, canvas.height - padding);
            ctx.lineTo(canvas.width - padding, canvas.height - padding); // X-axis
            ctx.strokeStyle = '#ccc';
            ctx.stroke();

            // Plot data
            ctx.beginPath();
            ctx.moveTo(padding, canvas.height - padding - (N_values[0] / K) * graphHeight);
            for (let i = 1; i < N_values.length; i++) {
                const x = padding + (i / timeSteps) * graphWidth;
                const y = canvas.height - padding - (N_values[i] / K) * graphHeight;
                ctx.lineTo(x, y);
            }
            ctx.strokeStyle = '#28a745';
            ctx.lineWidth = 2;
            ctx.stroke();

            // Labels
            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.fillText('Time', canvas.width / 2 - 10, canvas.height - padding/2);
            ctx.save();
            ctx.translate(padding/2, canvas.height/2 + 20);
            ctx.rotate(-Math.PI/2);
            ctx.fillText('Population (N)', 0, 0);
            ctx.restore();
            ctx.fillText(`K = ${K}`, canvas.width - padding - 25, padding + 10);
            ctx.fillText(`N₀ = ${N0.toFixed(0)}`, padding + 5, padding + 10);
            ctx.fillText(`r = ${r.toFixed(2)}`, padding + 5, padding + 25);
        }

        // Earth Science: Plate Tectonics
        function drawPlateTectonics(canvas, params) {
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const speed1 = params.plate1Speed;
            const speed2 = params.plate2Speed;
            const timeFactor = 10; // Arbitrary factor to show movement

            const plateWidth = canvas.width / 3;
            const plateHeight = canvas.height * 0.6;
            const initialGap = 10;

            const yPos = canvas.height / 2 - plateHeight / 2;

            // Plate 1
            let x1 = canvas.width / 2 - plateWidth - initialGap / 2 + speed1 * timeFactor;
            ctx.fillStyle = '#8B4513'; // Brown
            ctx.fillRect(x1, yPos, plateWidth, plateHeight);
            drawArrow(ctx, x1 + plateWidth / 2, yPos - 20, speed1);
            ctx.fillStyle = 'white';
            ctx.fillText('Plate 1', x1 + 10, yPos + 20);


            // Plate 2
            let x2 = canvas.width / 2 + initialGap / 2 + speed2 * timeFactor;
            ctx.fillStyle = '#A0522D'; // Sienna
            ctx.fillRect(x2, yPos, plateWidth, plateHeight);
            drawArrow(ctx, x2 + plateWidth / 2, yPos - 20, speed2);
            ctx.fillStyle = 'white';
            ctx.fillText('Plate 2', x2 + 10, yPos + 20);

            // Boundary type
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            let boundaryType = "Transform (Sliding)";
            if (speed1 > 0 && speed2 < 0 && speed1 !== -speed2) boundaryType = "Convergent";
            else if (speed1 < 0 && speed2 > 0 && speed1 !== -speed2) boundaryType = "Convergent";
            else if (speed1 < 0 && speed2 < 0 || speed1 > 0 && speed2 > 0) {
                if ( (speed1 < 0 && x1 + plateWidth < x2) || (speed1 > 0 && x1 > x2 + plateWidth) ) {
                     // Moving same direction, one faster causing divergence or convergence based on relative positions
                } else {
                     boundaryType = (speed1 < speed2) ? "Divergent" : "Convergent"; // Simplified
                }
            } else if (speed1 === -speed2 && speed1 !== 0) {
                 boundaryType = (speed1 > 0) ? "Convergent" : "Divergent";
            } else if (speed1 === 0 && speed2 === 0) {
                boundaryType = "Static";
            }


            // Simplified boundary logic for text (more complex for actual geology)
            if (speed1 > speed2) { // Plate 1 moving right relative to Plate 2
                if (x1 + plateWidth > x2) boundaryType = "Convergent (1 over 2 or collision)";
                else boundaryType = "Divergent";
            } else if (speed2 > speed1) { // Plate 2 moving right relative to Plate 1
                 if (x2 + plateWidth > x1) boundaryType = "Convergent (2 over 1 or collision)";
                 else boundaryType = "Divergent";
            }
            if (speed1 === speed2 && speed1 !== 0) boundaryType = "Transform (Sliding at same speed)";
            if (speed1 * speed2 < 0) boundaryType = "Convergent/Divergent (Opposite)"; // General term if complex

            ctx.fillText(`Boundary Type (approx): ${boundaryType}`, 20, canvas.height - 20);
        }

        function drawArrow(ctx, x, y, speed) {
            ctx.beginPath();
            ctx.moveTo(x, y);
            const arrowLength = 10 + Math.abs(speed) * 5;
            const direction = speed > 0 ? 1 : (speed < 0 ? -1 : 0);
            
            if (direction === 0) {
                ctx.arc(x,y,3,0,Math.PI*2);
                ctx.fillStyle = "black";
                ctx.fill();
                return;
            }

            ctx.lineTo(x + arrowLength * direction, y);
            ctx.lineTo(x + arrowLength * direction - 5 * direction, y - 5);
            ctx.moveTo(x + arrowLength * direction, y);
            ctx.lineTo(x + arrowLength * direction - 5 * direction, y + 5);
            ctx.strokeStyle = 'black';
            ctx.lineWidth = 2;
            ctx.stroke();
        }

        // Math: Circle Properties
        function drawCircleProperties(canvas, params) {
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const radius = params.radius;
            const outline = params.outline;
            const area = Math.PI * radius * radius;

            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;

            // Draw Circle
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
            ctx.fillStyle = 'rgba(0, 123, 255, 0.3)';
            ctx.fill();
            ctx.strokeStyle = '#007bff';
            ctx.lineWidth = outline;
            ctx.stroke();

            // Display info
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(`Radius: ${radius.toFixed(1)} px`, centerX, centerY - radius - 20);
            ctx.fillText(`Area: ${area.toFixed(2)} px²`, centerX, centerY + radius + 30);
            ctx.textAlign = 'left'; // Reset
        }


        // --- INITIALIZATION ---
        document.addEventListener('DOMContentLoaded', () => {
            renderDomainButtons();

            document.getElementById('submit-expectation').onclick = () => {
                const text = document.getElementById('expectation-textbox').value;
                if (text.trim()) {
                    alert("Your expectation:\n\n" + text);
                } else {
                    alert("Please write down your expectation first.");
                }
            };
            
            // Set default canvas size for display before a sim is loaded
            const canvas = document.getElementById('simulation-canvas');
            canvas.width = CANVAS_WIDTH;
            canvas.height = CANVAS_HEIGHT;
            const ctx = canvas.getContext('2d');
            ctx.fillStyle = '#eee';
            ctx.fillRect(0,0,canvas.width, canvas.height);
            ctx.fillStyle = '#999';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Select a domain and topic to start a simulation.', canvas.width/2, canvas.height/2);
            ctx.textAlign = 'left';
        });

    </script>
</body>
</html>
