<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Projectile Motion Simulation</title>
    
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f0f0;
            text-align: center;
        }
        #controls {
            margin-bottom: 20px;
        }
        #description {
            max-width: 600px;
            margin: 0 auto 20px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        #canvas-container {
            display: flex;
            justify-content: center;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background-color: #007BFF;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        input[type="range"] {
            width: 200px;
            margin: 10px;
        }
    </style>
</head>
<body>
    <div id="description">
        <h2>Projectile Motion Simulation</h2>
        <p>Explore how simulations help you learn by interacting with this projectile motion model. Adjust the angle and initial velocity to see how they affect the trajectory of the ball. Simulations let you experiment like a scientist, observing outcomes and understanding physics concepts in a hands-on way!</p>
    </div>
    <div id="controls">
        <label>Angle (degrees): <span id="angle-value">45</span></label><br>
        <input type="range" id="angle" min="0" max="90" value="45"><br>
        <label>Initial Velocity (m/s): <span id="velocity-value">20</span></label><br>
        <input type="range" id="velocity" min="10" max="50" value="20"><br>
        <button onclick="startSimulation()">Launch Ball</button>
        <button onclick="resetSimulation()">Reset</button>
    </div>
    <div id="canvas-container"></div>

    <script>
        let ball;
        let angle = 45;
        let velocity = 20;
        let isLaunched = false;
        let g = 9.81; // Gravity (m/s^2)
        let t = 0; // Time

        function setup() {
            let canvas = createCanvas(800, 400);
            canvas.parent('canvas-container');
            ball = { x: 0, y: 0, vx: 0, vy: 0 };
            resetSimulation();
        }

        function draw() {
            background(220);
            // Draw ground
            fill(0, 128, 0);
            rect(0, height - 20, width, 20);
            
            if (isLaunched) {
                // Update position using projectile motion equations
                ball.x = ball.vx * t;
                ball.y = height - (ball.vy * t - 0.5 * g * t * t);
                t += 0.05;
                
                // Draw ball
                fill(255, 0, 0);
                ellipse(ball.x, ball.y, 20, 20);
                
                // Stop when ball hits ground
                if (ball.y > height - 10) {
                    isLaunched = false;
                }
            } else {
                // Draw ball at starting position
                fill(255, 0, 0);
                ellipse(ball.x, ball.y, 20, 20);
            }
        }

        function startSimulation() {
            if (!isLaunched) {
                angle = parseFloat(document.getElementById('angle').value);
                velocity = parseFloat(document.getElementById('velocity').value);
                ball.vx = velocity * cos(radians(angle));
                ball.vy = velocity * sin(radians(angle));
                ball.x = 0;
                ball.y = height - 20;
                t = 0;
                isLaunched = true;
            }
        }

        function resetSimulation() {
            ball.x = 0;
            ball.y = height - 20;
            isLaunched = false;
            t = 0;
        }

        // Update displayed values
        document.getElementById('angle').addEventListener('input', function() {
            document.getElementById('angle-value').textContent = this.value;
        });

        document.getElementById('velocity').addEventListener('input', function() {
            document.getElementById('velocity-value').textContent = this.value;
        });
    </script>
</body>
</html>