<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Learning Paths - Physics Virtual Lab</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/components.css">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: var(--font-family-sans);
            margin: 0;
            padding: 0;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .learning-header {
            background: linear-gradient(135deg, var(--primary-600), var(--secondary-600));
            color: white;
            padding: var(--spacing-3xl) 0;
            text-align: center;
        }

        .learning-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: var(--spacing-md);
        }

        .learning-subtitle {
            font-size: 1.25rem;
            opacity: 0.9;
            max-width: 700px;
            margin: 0 auto;
        }

        .main-content {
            flex: 1;
            max-width: 1400px;
            margin: 0 auto;
            padding: var(--spacing-xl);
            width: 100%;
        }

        .path-selector {
            display: flex;
            justify-content: center;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-3xl);
        }

        .path-tab {
            padding: var(--spacing-md) var(--spacing-xl);
            border: 2px solid var(--border-color);
            border-radius: var(--radius-xl);
            background: white;
            color: var(--text-primary);
            font-weight: 600;
            cursor: pointer;
            transition: all var(--transition-fast);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        [data-theme="dark"] .path-tab {
            background: var(--gray-700);
        }

        .path-tab.active {
            background: var(--primary-600);
            border-color: var(--primary-600);
            color: white;
        }

        .path-tab:hover:not(.active) {
            border-color: var(--primary-400);
            transform: translateY(-2px);
        }

        .learning-path {
            display: none;
        }

        .learning-path.active {
            display: block;
        }

        .path-intro {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            padding: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
            text-align: center;
        }

        [data-theme="dark"] .path-intro {
            background: var(--gray-700);
        }

        .path-description {
            font-size: 1.125rem;
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: var(--spacing-lg);
        }

        .path-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--spacing-md);
        }

        .stat-card {
            background: var(--bg-secondary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-md);
            text-align: center;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-600);
            margin-bottom: var(--spacing-xs);
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .experiments-timeline {
            position: relative;
            padding-left: var(--spacing-xl);
        }

        .timeline-line {
            position: absolute;
            left: 20px;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--border-color);
            border-radius: 2px;
        }

        .experiment-step {
            position: relative;
            margin-bottom: var(--spacing-xl);
        }

        .step-marker {
            position: absolute;
            left: -28px;
            top: 20px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: white;
            border: 4px solid var(--primary-600);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            color: var(--primary-600);
            z-index: 1;
        }

        .step-marker.completed {
            background: var(--beginner-color);
            border-color: var(--beginner-color);
            color: white;
        }

        .step-marker.locked {
            background: var(--gray-200);
            border-color: var(--gray-400);
            color: var(--gray-500);
        }

        [data-theme="dark"] .step-marker {
            background: var(--gray-700);
        }

        [data-theme="dark"] .step-marker.locked {
            background: var(--gray-600);
        }

        .experiment-card {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-md);
            padding: var(--spacing-lg);
            transition: all var(--transition-normal);
        }

        [data-theme="dark"] .experiment-card {
            background: var(--gray-700);
        }

        .experiment-card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        .experiment-card.locked {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .experiment-card.locked:hover {
            transform: none;
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .experiment-icon {
            font-size: 2.5rem;
        }

        .experiment-info {
            flex: 1;
        }

        .experiment-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }

        .experiment-meta {
            display: flex;
            gap: var(--spacing-md);
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .difficulty-badge {
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-weight: 500;
            font-size: 0.75rem;
        }

        .difficulty-beginner {
            background: var(--beginner-color);
            color: white;
        }

        .difficulty-intermediate {
            background: var(--intermediate-color);
            color: white;
        }

        .difficulty-advanced {
            background: var(--advanced-color);
            color: white;
        }

        .experiment-description {
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: var(--spacing-lg);
        }

        .experiment-progress {
            margin-bottom: var(--spacing-lg);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--gray-200);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: var(--spacing-sm);
        }

        [data-theme="dark"] .progress-bar {
            background: var(--gray-600);
        }

        .progress-fill {
            height: 100%;
            background: var(--primary-600);
            border-radius: 4px;
            transition: width var(--transition-normal);
        }

        .progress-text {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .experiment-actions {
            display: flex;
            gap: var(--spacing-sm);
        }

        .action-btn {
            flex: 1;
            padding: var(--spacing-sm) var(--spacing-md);
            border: none;
            border-radius: var(--radius-md);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-fast);
            text-decoration: none;
            text-align: center;
        }

        .btn-primary {
            background: var(--primary-600);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-700);
        }

        .btn-secondary {
            background: var(--gray-200);
            color: var(--text-primary);
        }

        [data-theme="dark"] .btn-secondary {
            background: var(--gray-600);
        }

        .btn-secondary:hover {
            background: var(--gray-300);
        }

        [data-theme="dark"] .btn-secondary:hover {
            background: var(--gray-500);
        }

        .btn-disabled {
            background: var(--gray-300);
            color: var(--gray-500);
            cursor: not-allowed;
        }

        .btn-disabled:hover {
            background: var(--gray-300);
        }

        @media (max-width: 768px) {
            .path-selector {
                flex-direction: column;
                align-items: center;
            }
            
            .path-tab {
                width: 100%;
                max-width: 300px;
                justify-content: center;
            }
            
            .experiments-timeline {
                padding-left: var(--spacing-lg);
            }
            
            .step-marker {
                left: -20px;
                width: 32px;
                height: 32px;
            }
            
            .timeline-line {
                left: 12px;
            }
        }
    </style>
</head>
<body>
    <header id="main-header"></header>

    <div class="learning-header animate-fade-in">
        <h1 class="learning-title">🎯 Learning Paths</h1>
        <p class="learning-subtitle">
            Structured learning journeys designed to build your physics knowledge step by step
        </p>
    </div>

    <div class="main-content">
        <div class="path-selector animate-slide-up">
            <button class="path-tab active" data-path="beginner">
                <span>🌱</span> Beginner Path
            </button>
            <button class="path-tab" data-path="intermediate">
                <span>🚀</span> Intermediate Path
            </button>
            <button class="path-tab" data-path="advanced">
                <span>🎓</span> Advanced Path
            </button>
        </div>

        <!-- Beginner Path -->
        <div class="learning-path active" id="beginner-path">
            <div class="path-intro animate-slide-up">
                <h2 style="color: var(--beginner-color); margin-bottom: var(--spacing-md);">🌱 Beginner Learning Path</h2>
                <p class="path-description">
                    Start your physics journey with fundamental concepts and interactive experiments. 
                    Perfect for students new to physics or those looking to refresh their understanding.
                </p>
                <div class="path-stats">
                    <div class="stat-card">
                        <div class="stat-value">4</div>
                        <div class="stat-label">Experiments</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">2-3</div>
                        <div class="stat-label">Hours</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">75%</div>
                        <div class="stat-label">Completion</div>
                    </div>
                </div>
            </div>

            <div class="experiments-timeline">
                <div class="timeline-line"></div>
                
                <div class="experiment-step animate-slide-left animate-stagger-1">
                    <div class="step-marker completed">✓</div>
                    <div class="experiment-card">
                        <div class="card-header">
                            <div class="experiment-icon">🔌</div>
                            <div class="experiment-info">
                                <h3 class="experiment-title">Ohm's Law Explorer</h3>
                                <div class="experiment-meta">
                                    <span class="difficulty-badge difficulty-beginner">Beginner</span>
                                    <span>⏱️ 20 minutes</span>
                                    <span>⚡ Electromagnetism</span>
                                </div>
                            </div>
                        </div>
                        <p class="experiment-description">
                            Learn the fundamental relationship between voltage, current, and resistance through interactive circuit manipulation.
                        </p>
                        <div class="experiment-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 100%"></div>
                            </div>
                            <div class="progress-text">Completed - Score: 85%</div>
                        </div>
                        <div class="experiment-actions">
                            <a href="experiments/ohms-law.html" class="action-btn btn-secondary">Review</a>
                            <a href="assessment.html?topic=ohms-law" class="action-btn btn-primary">Take Quiz</a>
                        </div>
                    </div>
                </div>

                <div class="experiment-step animate-slide-left animate-stagger-2">
                    <div class="step-marker completed">✓</div>
                    <div class="experiment-card">
                        <div class="card-header">
                            <div class="experiment-icon">⚖️</div>
                            <div class="experiment-info">
                                <h3 class="experiment-title">Pendulum Laboratory</h3>
                                <div class="experiment-meta">
                                    <span class="difficulty-badge difficulty-beginner">Beginner</span>
                                    <span>⏱️ 25 minutes</span>
                                    <span>🔄 Mechanics</span>
                                </div>
                            </div>
                        </div>
                        <p class="experiment-description">
                            Explore simple harmonic motion by investigating how pendulum length affects the period of oscillation.
                        </p>
                        <div class="experiment-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 100%"></div>
                            </div>
                            <div class="progress-text">Completed - Score: 92%</div>
                        </div>
                        <div class="experiment-actions">
                            <a href="experiments/pendulum-lab.html" class="action-btn btn-secondary">Review</a>
                            <a href="assessment.html?topic=pendulum" class="action-btn btn-primary">Take Quiz</a>
                        </div>
                    </div>
                </div>

                <div class="experiment-step animate-slide-left animate-stagger-3">
                    <div class="step-marker">3</div>
                    <div class="experiment-card">
                        <div class="card-header">
                            <div class="experiment-icon">🔬</div>
                            <div class="experiment-info">
                                <h3 class="experiment-title">Interactive Science Simulations</h3>
                                <div class="experiment-meta">
                                    <span class="difficulty-badge difficulty-beginner">Beginner</span>
                                    <span>⏱️ 45 minutes</span>
                                    <span>🌐 General</span>
                                </div>
                            </div>
                        </div>
                        <p class="experiment-description">
                            Explore multiple physics concepts through interactive simulations and develop scientific observation skills.
                        </p>
                        <div class="experiment-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 60%"></div>
                            </div>
                            <div class="progress-text">In Progress - 60% complete</div>
                        </div>
                        <div class="experiment-actions">
                            <a href="experiments/science-simulations.html" class="action-btn btn-primary">Continue</a>
                            <button class="action-btn btn-disabled" disabled>Quiz Locked</button>
                        </div>
                    </div>
                </div>

                <div class="experiment-step animate-slide-left animate-stagger-4">
                    <div class="step-marker locked">4</div>
                    <div class="experiment-card locked">
                        <div class="card-header">
                            <div class="experiment-icon">🎓</div>
                            <div class="experiment-info">
                                <h3 class="experiment-title">Beginner Assessment</h3>
                                <div class="experiment-meta">
                                    <span class="difficulty-badge difficulty-beginner">Beginner</span>
                                    <span>⏱️ 30 minutes</span>
                                    <span>📝 Assessment</span>
                                </div>
                            </div>
                        </div>
                        <p class="experiment-description">
                            Comprehensive assessment covering all beginner-level concepts. Complete all experiments to unlock.
                        </p>
                        <div class="experiment-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 0%"></div>
                            </div>
                            <div class="progress-text">Locked - Complete prerequisites first</div>
                        </div>
                        <div class="experiment-actions">
                            <button class="action-btn btn-disabled" disabled>Complete Prerequisites</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Intermediate Path -->
        <div class="learning-path" id="intermediate-path">
            <div class="path-intro">
                <h2 style="color: var(--intermediate-color); margin-bottom: var(--spacing-md);">🚀 Intermediate Learning Path</h2>
                <p class="path-description">
                    Build upon your foundation with more complex physics concepts and advanced simulations.
                    Recommended after completing the beginner path.
                </p>
                <div class="path-stats">
                    <div class="stat-card">
                        <div class="stat-value">5</div>
                        <div class="stat-label">Experiments</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">4-5</div>
                        <div class="stat-label">Hours</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">25%</div>
                        <div class="stat-label">Completion</div>
                    </div>
                </div>
            </div>

            <div class="experiments-timeline">
                <div class="timeline-line"></div>
                
                <div class="experiment-step">
                    <div class="step-marker">1</div>
                    <div class="experiment-card">
                        <div class="card-header">
                            <div class="experiment-icon">⚡</div>
                            <div class="experiment-info">
                                <h3 class="experiment-title">Circuit Simulator</h3>
                                <div class="experiment-meta">
                                    <span class="difficulty-badge difficulty-intermediate">Intermediate</span>
                                    <span>⏱️ 30 minutes</span>
                                    <span>⚡ Electromagnetism</span>
                                </div>
                            </div>
                        </div>
                        <p class="experiment-description">
                            Build and analyze complex electrical circuits with multiple components and understand circuit behavior.
                        </p>
                        <div class="experiment-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 40%"></div>
                            </div>
                            <div class="progress-text">In Progress - 40% complete</div>
                        </div>
                        <div class="experiment-actions">
                            <a href="experiments/circuit-simulator.html" class="action-btn btn-primary">Continue</a>
                            <button class="action-btn btn-disabled" disabled>Quiz Locked</button>
                        </div>
                    </div>
                </div>

                <div class="experiment-step">
                    <div class="step-marker locked">2</div>
                    <div class="experiment-card locked">
                        <div class="card-header">
                            <div class="experiment-icon">🚀</div>
                            <div class="experiment-info">
                                <h3 class="experiment-title">Projectile Motion Lab</h3>
                                <div class="experiment-meta">
                                    <span class="difficulty-badge difficulty-intermediate">Intermediate</span>
                                    <span>⏱️ 30 minutes</span>
                                    <span>🔄 Mechanics</span>
                                </div>
                            </div>
                        </div>
                        <p class="experiment-description">
                            Analyze projectile motion with varying angles and velocities to understand trajectory physics.
                        </p>
                        <div class="experiment-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 0%"></div>
                            </div>
                            <div class="progress-text">Locked - Complete Circuit Simulator first</div>
                        </div>
                        <div class="experiment-actions">
                            <button class="action-btn btn-disabled" disabled>Complete Prerequisites</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Advanced Path -->
        <div class="learning-path" id="advanced-path">
            <div class="path-intro">
                <h2 style="color: var(--advanced-color); margin-bottom: var(--spacing-md);">🎓 Advanced Learning Path</h2>
                <p class="path-description">
                    Master complex physics concepts with challenging experiments and real-world applications.
                    Requires completion of intermediate path.
                </p>
                <div class="path-stats">
                    <div class="stat-card">
                        <div class="stat-value">6</div>
                        <div class="stat-label">Experiments</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">6-8</div>
                        <div class="stat-label">Hours</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">0%</div>
                        <div class="stat-label">Completion</div>
                    </div>
                </div>
            </div>

            <div class="experiments-timeline">
                <div class="timeline-line"></div>
                
                <div class="experiment-step">
                    <div class="step-marker locked">1</div>
                    <div class="experiment-card locked">
                        <div class="card-header">
                            <div class="experiment-icon">🌊</div>
                            <div class="experiment-info">
                                <h3 class="experiment-title">Wave Mechanics</h3>
                                <div class="experiment-meta">
                                    <span class="difficulty-badge difficulty-advanced">Advanced</span>
                                    <span>⏱️ 45 minutes</span>
                                    <span>🌊 Waves</span>
                                </div>
                            </div>
                        </div>
                        <p class="experiment-description">
                            Explore wave interference, diffraction, and complex wave phenomena in advanced simulations.
                        </p>
                        <div class="experiment-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 0%"></div>
                            </div>
                            <div class="progress-text">Locked - Complete intermediate path first</div>
                        </div>
                        <div class="experiment-actions">
                            <button class="action-btn btn-disabled" disabled>Complete Prerequisites</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer id="main-footer-container"></footer>

    <script type="module" src="js/main.js"></script>
    <script>
        // Learning Paths Controller
        document.addEventListener('DOMContentLoaded', () => {
            const pathTabs = document.querySelectorAll('.path-tab');
            const learningPaths = document.querySelectorAll('.learning-path');

            pathTabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    const pathId = tab.dataset.path;
                    
                    // Update active tab
                    pathTabs.forEach(t => t.classList.remove('active'));
                    tab.classList.add('active');
                    
                    // Update active path
                    learningPaths.forEach(path => path.classList.remove('active'));
                    document.getElementById(`${pathId}-path`).classList.add('active');
                });
            });

            // Load user progress
            loadUserProgress();
        });

        function loadUserProgress() {
            // This would typically load from localStorage or a server
            const progress = JSON.parse(localStorage.getItem('learningProgress') || '{}');
            
            // Update progress bars and completion status based on stored data
            updateProgressDisplay(progress);
        }

        function updateProgressDisplay(progress) {
            // Update progress bars, completion markers, and unlock status
            // This is a simplified version - in a real app, you'd have more complex logic
        }
    </script>
</body>
</html>
