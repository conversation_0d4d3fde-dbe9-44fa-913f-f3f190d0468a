// Physics Virtual Lab - Video Library System
// Author: Dr. <PERSON>, SUST-BME
// Copyright: +249912867327, +966538076790
// Institution: Sudan University of Science and Technology - Biomedical Engineering

class VideoLibrary {
    constructor() {
        this.videos = this.loadVideoData();
        this.filteredVideos = [...this.videos];
        this.currentFilters = {
            category: 'all',
            difficulty: 'all',
            search: ''
        };
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.renderVideos();
        this.createVideoModal();
    }

    loadVideoData() {
        return [
            {
                id: 'ohms-law-intro',
                title: "Understanding Ohm's Law: The Foundation of Electronics",
                description: "Explore the fundamental relationship between voltage, current, and resistance through beautiful animations.",
                category: 'electromagnetism',
                difficulty: 'beginner',
                duration: '8:45',
                thumbnail: '⚡',
                videoUrl: 'https://www.youtube.com/embed/HsLLq6Rm5tU', // Educational Ohm's Law video
                experimentUrl: 'experiments/ohms-law.html',
                concepts: ['Voltage', 'Current', 'Resistance', 'Power'],
                learningObjectives: [
                    'Understand the relationship V = IR',
                    'Calculate power in electrical circuits',
                    'Apply Ohm\'s Law to real-world scenarios'
                ]
            },
            {
                id: 'pendulum-motion',
                title: "Simple Harmonic Motion: The Dancing Pendulum",
                description: "Discover how pendulums swing and the physics behind their rhythmic motion.",
                category: 'mechanics',
                difficulty: 'beginner',
                duration: '6:30',
                thumbnail: '⚖️',
                videoUrl: 'https://www.youtube.com/embed/yVkdfJ9PkRQ', // Educational pendulum video
                experimentUrl: 'experiments/pendulum-lab.html',
                concepts: ['Period', 'Frequency', 'Amplitude', 'Gravity'],
                learningObjectives: [
                    'Understand simple harmonic motion',
                    'Learn the pendulum period formula',
                    'Analyze factors affecting pendulum motion'
                ]
            },
            {
                id: 'projectile-motion',
                title: "Projectile Motion: Physics in Flight",
                description: "Follow the path of projectiles and understand the forces that shape their trajectories.",
                category: 'mechanics',
                difficulty: 'intermediate',
                duration: '10:15',
                thumbnail: '🚀',
                videoUrl: 'https://www.youtube.com/embed/R-WXIZHpGNs', // Educational projectile motion video
                experimentUrl: 'experiments/projectile-motion.html',
                concepts: ['Trajectory', 'Range', 'Maximum Height', 'Launch Angle'],
                learningObjectives: [
                    'Understand 2D motion principles',
                    'Calculate range and maximum height',
                    'Optimize launch angles for different goals'
                ]
            },
            {
                id: 'circuit-analysis',
                title: "Circuit Analysis: Building Electronic Pathways",
                description: "Learn how to analyze complex circuits and understand current flow through different components.",
                category: 'electromagnetism',
                difficulty: 'intermediate',
                duration: '12:20',
                thumbnail: '🔌',
                videoUrl: 'https://www.youtube.com/embed/VV6tZ3Aqfuc', // Educational circuit analysis video
                experimentUrl: 'experiments/circuit-simulator.html',
                concepts: ['Series Circuits', 'Parallel Circuits', 'Kirchhoff\'s Laws', 'Power Distribution'],
                learningObjectives: [
                    'Analyze series and parallel circuits',
                    'Apply Kirchhoff\'s voltage and current laws',
                    'Calculate power distribution in circuits'
                ]
            },
            {
                id: 'wave-interference',
                title: "Wave Interference: When Waves Meet",
                description: "Explore the fascinating world of wave interference and discover how waves interact.",
                category: 'waves',
                difficulty: 'intermediate',
                duration: '9:40',
                thumbnail: '🌊',
                videoUrl: 'https://www.youtube.com/embed/Iuv6hY6zsd0', // Educational wave interference video
                experimentUrl: 'experiments/science-simulations.html',
                concepts: ['Constructive Interference', 'Destructive Interference', 'Standing Waves', 'Superposition'],
                learningObjectives: [
                    'Understand wave superposition principle',
                    'Predict interference patterns',
                    'Analyze standing wave formation'
                ]
            },
            {
                id: 'electromagnetic-induction',
                title: "Electromagnetic Induction: The Magic of Moving Magnets",
                description: "Discover how changing magnetic fields create electricity and power our modern world.",
                category: 'electromagnetism',
                difficulty: 'advanced',
                duration: '11:55',
                thumbnail: '🧲',
                videoUrl: 'https://www.youtube.com/embed/fv-V2sOFzpI', // Educational electromagnetic induction video
                experimentUrl: '#coming-soon',
                concepts: ['Faraday\'s Law', 'Lenz\'s Law', 'Induced EMF', 'Generators'],
                learningObjectives: [
                    'Understand Faraday\'s law of induction',
                    'Apply Lenz\'s law to predict current direction',
                    'Explain how generators and motors work'
                ]
            },
            {
                id: 'quantum-basics',
                title: "Quantum Physics: The Weird World of the Very Small",
                description: "Enter the strange realm of quantum mechanics where particles behave like waves.",
                category: 'modern',
                difficulty: 'advanced',
                duration: '14:30',
                thumbnail: '⚛️',
                videoUrl: 'https://www.youtube.com/embed/JhHMJCUmq28', // Educational quantum physics video
                experimentUrl: '#coming-soon',
                concepts: ['Wave-Particle Duality', 'Uncertainty Principle', 'Quantum States', 'Superposition'],
                learningObjectives: [
                    'Understand wave-particle duality',
                    'Learn about quantum uncertainty',
                    'Explore quantum superposition'
                ]
            },
            {
                id: 'thermodynamics-intro',
                title: "Thermodynamics: The Science of Heat and Energy",
                description: "Explore the laws that govern heat, temperature, and energy transfer in our universe.",
                category: 'thermodynamics',
                difficulty: 'intermediate',
                duration: '13:15',
                thumbnail: '🔥',
                videoUrl: 'https://www.youtube.com/embed/NyOYW07-L5g', // Educational thermodynamics video
                experimentUrl: '#coming-soon',
                concepts: ['Heat Transfer', 'Entropy', 'Thermodynamic Laws', 'Phase Changes'],
                learningObjectives: [
                    'Understand the laws of thermodynamics',
                    'Analyze heat transfer mechanisms',
                    'Calculate energy efficiency'
                ]
            }
        ];
    }

    setupEventListeners() {
        document.getElementById('categoryFilter').addEventListener('change', (e) => {
            this.currentFilters.category = e.target.value;
            this.applyFilters();
        });

        document.getElementById('difficultyFilter').addEventListener('change', (e) => {
            this.currentFilters.difficulty = e.target.value;
            this.applyFilters();
        });

        document.getElementById('searchBox').addEventListener('input', (e) => {
            this.currentFilters.search = e.target.value.toLowerCase();
            this.applyFilters();
        });
    }

    applyFilters() {
        this.filteredVideos = this.videos.filter(video => {
            const matchesCategory = this.currentFilters.category === 'all' || 
                                  video.category === this.currentFilters.category;
            
            const matchesDifficulty = this.currentFilters.difficulty === 'all' || 
                                    video.difficulty === this.currentFilters.difficulty;
            
            const matchesSearch = this.currentFilters.search === '' ||
                                video.title.toLowerCase().includes(this.currentFilters.search) ||
                                video.description.toLowerCase().includes(this.currentFilters.search) ||
                                video.concepts.some(concept => 
                                    concept.toLowerCase().includes(this.currentFilters.search));

            return matchesCategory && matchesDifficulty && matchesSearch;
        });

        this.renderVideos();
    }

    renderVideos() {
        const videoGrid = document.getElementById('videoGrid');
        
        if (this.filteredVideos.length === 0) {
            videoGrid.innerHTML = `
                <div style="grid-column: 1 / -1; text-align: center; padding: 3rem;">
                    <div style="font-size: 3rem; margin-bottom: 1rem;">🔍</div>
                    <h3>No videos found</h3>
                    <p>Try adjusting your filters or search terms.</p>
                </div>
            `;
            return;
        }

        videoGrid.innerHTML = this.filteredVideos.map(video => `
            <div class="video-card animate-slide-up" style="animation-delay: ${Math.random() * 0.5}s">
                <div class="video-thumbnail ${video.category}">
                    <div class="thumbnail-icon">${video.thumbnail}</div>
                    <div class="play-button" onclick="videoLibrary.openVideoModal('${video.id}')">
                        <div class="play-icon">▶</div>
                    </div>
                    <div class="video-duration">${video.duration}</div>
                </div>
                <div class="video-info">
                    <h3 class="video-title-card">${video.title}</h3>
                    <p class="video-description">${video.description}</p>
                    <div class="video-meta">
                        <span class="meta-tag difficulty-${video.difficulty}">${this.capitalizeFirst(video.difficulty)}</span>
                        <span class="meta-tag category-tag">${this.capitalizeFirst(video.category)}</span>
                    </div>
                    <div class="video-actions">
                        <button class="action-btn btn-watch" onclick="videoLibrary.openVideoModal('${video.id}')">
                            Watch Video
                        </button>
                        ${video.experimentUrl !== '#coming-soon' ? 
                            `<a href="${video.experimentUrl}" class="action-btn btn-experiment">Try Experiment</a>` :
                            `<button class="action-btn btn-experiment" disabled>Coming Soon</button>`
                        }
                    </div>
                </div>
            </div>
        `).join('');
    }

    createVideoModal() {
        const modal = document.createElement('div');
        modal.id = 'videoModal';
        modal.className = 'video-modal';
        modal.innerHTML = `
            <div class="video-modal-content">
                <div class="video-modal-header">
                    <h2 id="modalVideoTitle">Video Title</h2>
                    <button class="modal-close" onclick="videoLibrary.closeVideoModal()">&times;</button>
                </div>
                <div class="video-container">
                    <iframe id="modalVideoFrame" width="100%" height="400" frameborder="0" allowfullscreen></iframe>
                </div>
                <div class="video-details">
                    <div class="video-concepts">
                        <h4>Key Concepts:</h4>
                        <div id="modalConcepts"></div>
                    </div>
                    <div class="learning-objectives">
                        <h4>Learning Objectives:</h4>
                        <ul id="modalObjectives"></ul>
                    </div>
                    <div class="modal-actions">
                        <button id="modalExperimentBtn" class="action-btn btn-experiment">Try Related Experiment</button>
                        <button class="action-btn btn-watch" onclick="videoLibrary.markAsWatched()">Mark as Watched</button>
                    </div>
                </div>
            </div>
        `;

        // Add modal styles
        const style = document.createElement('style');
        style.textContent = `
            .video-modal {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.8);
                display: none;
                align-items: center;
                justify-content: center;
                z-index: 2000;
                padding: 1rem;
            }

            .video-modal-content {
                background: white;
                border-radius: var(--radius-xl);
                max-width: 900px;
                width: 100%;
                max-height: 90vh;
                overflow-y: auto;
                position: relative;
            }

            [data-theme="dark"] .video-modal-content {
                background: var(--gray-800);
            }

            .video-modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: var(--spacing-lg);
                border-bottom: 1px solid var(--border-color);
            }

            .modal-close {
                background: none;
                border: none;
                font-size: 2rem;
                cursor: pointer;
                color: var(--text-secondary);
                padding: 0;
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: all var(--transition-fast);
            }

            .modal-close:hover {
                background: var(--bg-secondary);
                color: var(--text-primary);
            }

            .video-container {
                position: relative;
                padding-bottom: 56.25%;
                height: 0;
                overflow: hidden;
            }

            .video-container iframe {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
            }

            .video-details {
                padding: var(--spacing-lg);
            }

            .video-concepts h4,
            .learning-objectives h4 {
                color: var(--text-primary);
                margin-bottom: var(--spacing-sm);
            }

            .modal-actions {
                display: flex;
                gap: var(--spacing-md);
                margin-top: var(--spacing-lg);
                flex-wrap: wrap;
            }

            @media (max-width: 768px) {
                .video-modal {
                    padding: 0.5rem;
                }
                
                .modal-actions {
                    flex-direction: column;
                }
            }
        `;

        document.head.appendChild(style);
        document.body.appendChild(modal);

        // Close modal when clicking outside
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeVideoModal();
            }
        });
    }

    openVideoModal(videoId) {
        const video = this.videos.find(v => v.id === videoId);
        if (!video) return;

        const modal = document.getElementById('videoModal');
        const title = document.getElementById('modalVideoTitle');
        const frame = document.getElementById('modalVideoFrame');
        const concepts = document.getElementById('modalConcepts');
        const objectives = document.getElementById('modalObjectives');
        const experimentBtn = document.getElementById('modalExperimentBtn');

        title.textContent = video.title;
        frame.src = video.videoUrl;
        
        concepts.innerHTML = video.concepts.map(concept => 
            `<span class="meta-tag category-tag" style="margin-right: 0.5rem; margin-bottom: 0.5rem; display: inline-block;">${concept}</span>`
        ).join('');

        objectives.innerHTML = video.learningObjectives.map(objective => 
            `<li>${objective}</li>`
        ).join('');

        if (video.experimentUrl !== '#coming-soon') {
            experimentBtn.style.display = 'block';
            experimentBtn.onclick = () => window.open(video.experimentUrl, '_blank');
        } else {
            experimentBtn.style.display = 'none';
        }

        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';

        // Track video view
        this.trackVideoView(videoId);
    }

    closeVideoModal() {
        const modal = document.getElementById('videoModal');
        const frame = document.getElementById('modalVideoFrame');
        
        modal.style.display = 'none';
        frame.src = '';
        document.body.style.overflow = '';
    }

    markAsWatched() {
        // Implementation for marking video as watched
        // This would typically save to localStorage or send to a server
        console.log('Video marked as watched');
        
        // Show success message
        if (window.app && window.app.showNotification) {
            window.app.showNotification('Video marked as watched! 🎉', 'success');
        }
    }

    trackVideoView(videoId) {
        // Track video views for analytics
        const viewData = JSON.parse(localStorage.getItem('videoViews') || '{}');
        viewData[videoId] = (viewData[videoId] || 0) + 1;
        localStorage.setItem('videoViews', JSON.stringify(viewData));
    }

    capitalizeFirst(str) {
        return str.charAt(0).toUpperCase() + str.slice(1);
    }
}

// Global function for modal access
function openVideoModal(videoId) {
    if (window.videoLibrary) {
        window.videoLibrary.openVideoModal(videoId);
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.videoLibrary = new VideoLibrary();
});
