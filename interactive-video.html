<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Video Player - Physics Virtual Lab</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/components.css">
    <style>
        body {
            font-family: var(--font-family-sans);
            margin: 0;
            padding: 0;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .video-player-container {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 350px;
            gap: var(--spacing-lg);
            padding: var(--spacing-lg);
            max-width: 1400px;
            margin: 0 auto;
            width: 100%;
        }

        .main-video-section {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
        }

        .video-header {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            padding: var(--spacing-lg);
        }

        [data-theme="dark"] .video-header {
            background: var(--gray-700);
        }

        .video-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
        }

        .video-meta {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-md);
            align-items: center;
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .video-player {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            position: relative;
        }

        [data-theme="dark"] .video-player {
            background: var(--gray-700);
        }

        .video-frame {
            width: 100%;
            height: 400px;
            border: none;
            display: block;
        }

        .video-controls {
            padding: var(--spacing-md);
            background: var(--bg-secondary);
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .play-pause-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--primary-600);
            color: white;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .play-pause-btn:hover {
            background: var(--primary-700);
            transform: scale(1.05);
        }

        .progress-container {
            flex: 1;
            height: 6px;
            background: var(--gray-200);
            border-radius: 3px;
            cursor: pointer;
            position: relative;
        }

        [data-theme="dark"] .progress-container {
            background: var(--gray-600);
        }

        .progress-bar {
            height: 100%;
            background: var(--primary-600);
            border-radius: 3px;
            transition: width 0.1s ease;
        }

        .time-display {
            font-size: 0.875rem;
            color: var(--text-secondary);
            min-width: 80px;
            text-align: center;
        }

        .interactive-sidebar {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
        }

        .sidebar-section {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            padding: var(--spacing-lg);
        }

        [data-theme="dark"] .sidebar-section {
            background: var(--gray-700);
        }

        .section-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .quiz-question {
            background: var(--accent-50);
            border: 1px solid var(--accent-200);
            border-radius: var(--radius-lg);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-md);
            display: none;
        }

        [data-theme="dark"] .quiz-question {
            background: var(--accent-900);
            border-color: var(--accent-700);
        }

        .quiz-question.active {
            display: block;
            animation: slideUp 0.3s ease-out;
        }

        .question-text {
            font-weight: 500;
            margin-bottom: var(--spacing-md);
            color: var(--text-primary);
        }

        .quiz-options {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .quiz-option {
            padding: var(--spacing-sm);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            cursor: pointer;
            transition: all var(--transition-fast);
            background: var(--bg-primary);
        }

        .quiz-option:hover {
            border-color: var(--accent-500);
            background: var(--accent-50);
        }

        [data-theme="dark"] .quiz-option:hover {
            background: var(--accent-900);
        }

        .quiz-option.selected {
            border-color: var(--accent-600);
            background: var(--accent-100);
        }

        [data-theme="dark"] .quiz-option.selected {
            background: var(--accent-800);
        }

        .quiz-option.correct {
            border-color: var(--beginner-color);
            background: #f0fdf4;
        }

        [data-theme="dark"] .quiz-option.correct {
            background: #064e3b;
        }

        .quiz-option.incorrect {
            border-color: var(--advanced-color);
            background: #fef2f2;
        }

        [data-theme="dark"] .quiz-option.incorrect {
            background: #7f1d1d;
        }

        .notes-section textarea {
            width: 100%;
            min-height: 120px;
            padding: var(--spacing-md);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            font-family: var(--font-family-sans);
            background: var(--bg-primary);
            color: var(--text-primary);
            resize: vertical;
        }

        .notes-section textarea:focus {
            outline: none;
            border-color: var(--primary-500);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .concept-highlight {
            background: var(--primary-50);
            border: 1px solid var(--primary-200);
            border-radius: var(--radius-md);
            padding: var(--spacing-sm);
            margin-bottom: var(--spacing-sm);
            font-size: 0.875rem;
        }

        [data-theme="dark"] .concept-highlight {
            background: var(--primary-900);
            border-color: var(--primary-700);
        }

        .engagement-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
        }

        .stat-item {
            text-align: center;
            padding: var(--spacing-sm);
            background: var(--bg-secondary);
            border-radius: var(--radius-md);
        }

        .stat-value {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary-600);
        }

        .stat-label {
            font-size: 0.75rem;
            color: var(--text-secondary);
        }

        .interactive-elements {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
        }

        .hotspot {
            position: absolute;
            width: 30px;
            height: 30px;
            background: var(--accent-600);
            border-radius: 50%;
            cursor: pointer;
            pointer-events: all;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            animation: pulse 2s infinite;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .hotspot.visible {
            opacity: 1;
        }

        .hotspot:hover {
            transform: scale(1.2);
        }

        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: var(--spacing-sm);
            border-radius: var(--radius-md);
            font-size: 0.875rem;
            max-width: 200px;
            z-index: 1000;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
        }

        .tooltip.show {
            opacity: 1;
        }

        @media (max-width: 768px) {
            .video-player-container {
                grid-template-columns: 1fr;
                padding: var(--spacing-md);
            }
            
            .video-frame {
                height: 250px;
            }
            
            .video-controls {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <header id="main-header"></header>

    <div class="video-player-container">
        <div class="main-video-section">
            <div class="video-header animate-fade-in">
                <h1 class="video-title" id="videoTitle">Understanding Ohm's Law: The Foundation of Electronics</h1>
                <div class="video-meta">
                    <span>⚡ Electromagnetism</span>
                    <span>🎯 Beginner</span>
                    <span>⏱️ 8:45</span>
                    <span>👁️ 1,234 views</span>
                </div>
            </div>

            <div class="video-player animate-slide-up">
                <div class="video-frame-container" style="position: relative;">
                    <iframe 
                        id="videoFrame" 
                        class="video-frame"
                        src="https://www.youtube.com/embed/HsLLq6Rm5tU?enablejsapi=1"
                        allowfullscreen>
                    </iframe>
                    <div class="interactive-elements" id="interactiveElements">
                        <!-- Interactive hotspots will be added here -->
                    </div>
                </div>
                <div class="video-controls">
                    <button class="play-pause-btn" id="playPauseBtn">
                        <span id="playIcon">▶</span>
                    </button>
                    <div class="progress-container" id="progressContainer">
                        <div class="progress-bar" id="progressBar"></div>
                    </div>
                    <div class="time-display" id="timeDisplay">0:00 / 8:45</div>
                </div>
            </div>
        </div>

        <div class="interactive-sidebar">
            <div class="sidebar-section animate-slide-left">
                <h3 class="section-title">🧠 Interactive Quiz</h3>
                <div id="quizContainer">
                    <div class="quiz-question" data-time="120">
                        <div class="question-text">What does the 'V' represent in Ohm's Law?</div>
                        <div class="quiz-options">
                            <div class="quiz-option" data-correct="true">Voltage</div>
                            <div class="quiz-option">Volume</div>
                            <div class="quiz-option">Velocity</div>
                            <div class="quiz-option">Viscosity</div>
                        </div>
                    </div>
                    <div class="quiz-question" data-time="300">
                        <div class="question-text">If voltage doubles and resistance stays the same, what happens to current?</div>
                        <div class="quiz-options">
                            <div class="quiz-option">Current halves</div>
                            <div class="quiz-option" data-correct="true">Current doubles</div>
                            <div class="quiz-option">Current stays the same</div>
                            <div class="quiz-option">Current quadruples</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="sidebar-section animate-slide-left animate-stagger-1">
                <h3 class="section-title">📝 Your Notes</h3>
                <div class="notes-section">
                    <textarea 
                        id="videoNotes" 
                        placeholder="Take notes while watching the video...

Key points:
- 
- 
- 

Questions:
- 
- "></textarea>
                </div>
            </div>

            <div class="sidebar-section animate-slide-left animate-stagger-2">
                <h3 class="section-title">💡 Key Concepts</h3>
                <div id="conceptsContainer">
                    <div class="concept-highlight">
                        <strong>Ohm's Law:</strong> V = I × R
                    </div>
                    <div class="concept-highlight">
                        <strong>Voltage (V):</strong> Electric potential difference
                    </div>
                    <div class="concept-highlight">
                        <strong>Current (I):</strong> Flow of electric charge
                    </div>
                    <div class="concept-highlight">
                        <strong>Resistance (R):</strong> Opposition to current flow
                    </div>
                </div>
            </div>

            <div class="sidebar-section animate-slide-left animate-stagger-3">
                <h3 class="section-title">📊 Your Progress</h3>
                <div class="engagement-stats">
                    <div class="stat-item">
                        <div class="stat-value" id="watchProgress">0%</div>
                        <div class="stat-label">Watched</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="quizScore">0/2</div>
                        <div class="stat-label">Quiz Score</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="notesCount">0</div>
                        <div class="stat-label">Notes</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="engagementScore">0%</div>
                        <div class="stat-label">Engagement</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="tooltip" id="tooltip"></div>

    <footer id="main-footer-container"></footer>

    <script type="module" src="js/main.js"></script>
    <script>
        // Interactive Video Player
        class InteractiveVideoPlayer {
            constructor() {
                this.currentTime = 0;
                this.duration = 525; // 8:45 in seconds
                this.isPlaying = false;
                this.quizAnswered = [];
                this.notesCount = 0;
                this.watchedPercentage = 0;
                
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.createHotspots();
                this.startProgressTracking();
                this.loadUserNotes();
            }

            setupEventListeners() {
                // Play/Pause button
                document.getElementById('playPauseBtn').addEventListener('click', () => {
                    this.togglePlayPause();
                });

                // Progress bar
                document.getElementById('progressContainer').addEventListener('click', (e) => {
                    this.seekTo(e);
                });

                // Quiz options
                document.querySelectorAll('.quiz-option').forEach(option => {
                    option.addEventListener('click', (e) => {
                        this.handleQuizAnswer(e);
                    });
                });

                // Notes textarea
                document.getElementById('videoNotes').addEventListener('input', (e) => {
                    this.updateNotesCount();
                    this.saveNotes();
                });

                // Keyboard shortcuts
                document.addEventListener('keydown', (e) => {
                    if (e.target.tagName !== 'TEXTAREA') {
                        this.handleKeyboard(e);
                    }
                });
            }

            createHotspots() {
                const hotspots = [
                    { time: 60, x: '30%', y: '40%', content: 'This is voltage (V) - the driving force of electricity' },
                    { time: 180, x: '60%', y: '30%', content: 'Current (I) flows through the circuit like water through a pipe' },
                    { time: 240, x: '45%', y: '60%', content: 'Resistance (R) opposes the flow of current' }
                ];

                const container = document.getElementById('interactiveElements');
                
                hotspots.forEach((hotspot, index) => {
                    const element = document.createElement('div');
                    element.className = 'hotspot';
                    element.style.left = hotspot.x;
                    element.style.top = hotspot.y;
                    element.textContent = index + 1;
                    element.dataset.time = hotspot.time;
                    element.dataset.content = hotspot.content;
                    
                    element.addEventListener('mouseenter', (e) => {
                        this.showTooltip(e, hotspot.content);
                    });
                    
                    element.addEventListener('mouseleave', () => {
                        this.hideTooltip();
                    });
                    
                    container.appendChild(element);
                });
            }

            startProgressTracking() {
                setInterval(() => {
                    this.updateProgress();
                    this.checkQuizTriggers();
                    this.updateHotspots();
                    this.calculateEngagement();
                }, 1000);
            }

            updateProgress() {
                if (this.isPlaying) {
                    this.currentTime += 1;
                    if (this.currentTime > this.duration) {
                        this.currentTime = this.duration;
                        this.isPlaying = false;
                        this.updatePlayButton();
                    }
                }

                // Update progress bar
                const progress = (this.currentTime / this.duration) * 100;
                document.getElementById('progressBar').style.width = `${progress}%`;

                // Update time display
                const currentMin = Math.floor(this.currentTime / 60);
                const currentSec = this.currentTime % 60;
                const totalMin = Math.floor(this.duration / 60);
                const totalSec = this.duration % 60;
                
                document.getElementById('timeDisplay').textContent = 
                    `${currentMin}:${currentSec.toString().padStart(2, '0')} / ${totalMin}:${totalSec.toString().padStart(2, '0')}`;

                // Update watch progress
                this.watchedPercentage = Math.round(progress);
                document.getElementById('watchProgress').textContent = `${this.watchedPercentage}%`;
            }

            checkQuizTriggers() {
                document.querySelectorAll('.quiz-question').forEach(question => {
                    const triggerTime = parseInt(question.dataset.time);
                    if (this.currentTime >= triggerTime && !question.classList.contains('active') && !question.dataset.answered) {
                        question.classList.add('active');
                        this.pauseVideo();
                    }
                });
            }

            updateHotspots() {
                document.querySelectorAll('.hotspot').forEach(hotspot => {
                    const triggerTime = parseInt(hotspot.dataset.time);
                    const endTime = triggerTime + 30; // Show for 30 seconds
                    
                    if (this.currentTime >= triggerTime && this.currentTime <= endTime) {
                        hotspot.classList.add('visible');
                    } else {
                        hotspot.classList.remove('visible');
                    }
                });
            }

            togglePlayPause() {
                this.isPlaying = !this.isPlaying;
                this.updatePlayButton();
            }

            updatePlayButton() {
                const icon = document.getElementById('playIcon');
                icon.textContent = this.isPlaying ? '⏸️' : '▶';
            }

            pauseVideo() {
                this.isPlaying = false;
                this.updatePlayButton();
            }

            seekTo(e) {
                const container = e.currentTarget;
                const rect = container.getBoundingClientRect();
                const clickX = e.clientX - rect.left;
                const percentage = clickX / rect.width;
                this.currentTime = Math.floor(this.duration * percentage);
            }

            handleQuizAnswer(e) {
                const option = e.target;
                const question = option.closest('.quiz-question');
                const options = question.querySelectorAll('.quiz-option');
                
                // Clear previous selections
                options.forEach(opt => opt.classList.remove('selected', 'correct', 'incorrect'));
                
                // Mark selected option
                option.classList.add('selected');
                
                // Show correct/incorrect
                setTimeout(() => {
                    options.forEach(opt => {
                        if (opt.dataset.correct === 'true') {
                            opt.classList.add('correct');
                        } else if (opt.classList.contains('selected')) {
                            opt.classList.add('incorrect');
                        }
                    });
                    
                    // Mark as answered and update score
                    question.dataset.answered = 'true';
                    if (option.dataset.correct === 'true') {
                        this.quizAnswered.push(true);
                    } else {
                        this.quizAnswered.push(false);
                    }
                    
                    this.updateQuizScore();
                    
                    // Resume video after 3 seconds
                    setTimeout(() => {
                        question.classList.remove('active');
                        this.isPlaying = true;
                        this.updatePlayButton();
                    }, 3000);
                }, 500);
            }

            updateQuizScore() {
                const correct = this.quizAnswered.filter(answer => answer).length;
                const total = this.quizAnswered.length;
                document.getElementById('quizScore').textContent = `${correct}/${total}`;
            }

            updateNotesCount() {
                const notes = document.getElementById('videoNotes').value;
                this.notesCount = notes.split('\n').filter(line => line.trim().length > 0).length;
                document.getElementById('notesCount').textContent = this.notesCount;
            }

            calculateEngagement() {
                // Calculate engagement based on watch time, quiz participation, and notes
                const watchScore = this.watchedPercentage;
                const quizScore = this.quizAnswered.length > 0 ? (this.quizAnswered.filter(a => a).length / this.quizAnswered.length) * 100 : 0;
                const notesScore = Math.min(this.notesCount * 10, 100);
                
                const engagement = Math.round((watchScore + quizScore + notesScore) / 3);
                document.getElementById('engagementScore').textContent = `${engagement}%`;
            }

            showTooltip(e, content) {
                const tooltip = document.getElementById('tooltip');
                tooltip.textContent = content;
                tooltip.style.left = e.pageX + 10 + 'px';
                tooltip.style.top = e.pageY - 10 + 'px';
                tooltip.classList.add('show');
            }

            hideTooltip() {
                document.getElementById('tooltip').classList.remove('show');
            }

            handleKeyboard(e) {
                switch(e.code) {
                    case 'Space':
                        e.preventDefault();
                        this.togglePlayPause();
                        break;
                    case 'ArrowLeft':
                        this.currentTime = Math.max(0, this.currentTime - 10);
                        break;
                    case 'ArrowRight':
                        this.currentTime = Math.min(this.duration, this.currentTime + 10);
                        break;
                }
            }

            saveNotes() {
                const notes = document.getElementById('videoNotes').value;
                localStorage.setItem('video-notes-ohms-law', notes);
            }

            loadUserNotes() {
                const savedNotes = localStorage.getItem('video-notes-ohms-law');
                if (savedNotes) {
                    document.getElementById('videoNotes').value = savedNotes;
                    this.updateNotesCount();
                }
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new InteractiveVideoPlayer();
        });
    </script>
</body>
</html>
