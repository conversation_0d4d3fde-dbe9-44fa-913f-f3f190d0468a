<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Physics Simulations</title>
  
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f0f0;
            text-align: center;
        }
        #description {
            max-width: 600px;
            margin: 0 auto 20px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        #tabs {
            margin-bottom: 20px;
        }
        .tab-button {
            padding: 10px 20px;
            margin: 5px;
            background-color: #007BFF;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .tab-button:hover {
            background-color: #0056b3;
        }
        .tab-button.active {
            background-color: #003087;
        }
        #controls {
            margin-bottom: 20px;
        }
        input[type="range"] {
            width: 200px;
            margin: 10px;
        }
        #canvas-container {
            display: flex;
            justify-content: center;
        }
        .control-panel {
            display: none;
        }
        .control-panel.active {
            display: block;
        }
    </style>
</head>
<body>
    <div id="description">
        <h2>Interactive Physics Simulations</h2>
        <p>Explore physics concepts through interactive simulations. Adjust parameters in each experiment to see how they affect outcomes, just like a scientist! These simulations help you understand free fall, harmonic motion, and inclined planes by experimenting in a virtual environment.</p>
    </div>
    <div id="tabs">
        <button class="tab-button active" onclick="switchTab('freeFall')">Free Fall</button>
        <button class="tab-button" onclick="switchTab('spring')">Spring Motion</button>
        <button class="tab-button" onclick="switchTab('inclinedPlane')">Inclined Plane</button>
    </div>
    <div id="controls">
        <div id="freeFall-controls" class="control-panel active">
            <label>Initial Height (m): <span id="height-value">50</span></label><br>
            <input type="range" id="height" min="10" max="100" value="50"><br>
            <button onclick="startFreeFall()">Drop Object</button>
            <button onclick="resetFreeFall()">Reset</button>
        </div>
        <div id="spring-controls" class="control-panel">
            <label>Mass (kg): <span id="mass-value">1</span></label><br>
            <input type="range" id="mass" min="0.5" max="5" value="1"><br>
            <label>Spring Constant (N/m): <span id="k-value">100</span></label><br>
            <input type="range" id="k" min="50" max="200" value="100"><br>
            <button onclick="startSpring()">Start Oscillation</button>
            <button onclick="resetSpring()">Reset</button>
        </div>
        <div id="inclinedPlane-controls" class="control-panel">
            <label>Incline Angle (degrees): <span id="incline-angle-value">30</span></label><br>
            <input type="range" id="incline-angle" min="0" max="60" value="30"><br>
            <button onclick="startInclinedPlane()">Slide Object</button>
            <button onclick="resetInclinedPlane()">Reset</button>
        </div>
    </div>
    <div id="canvas-container"></div>

    <script>
        let currentTab = 'freeFall';
        let g = 9.81; // Gravity (m/s^2)
        let canvas;

        // Free Fall variables
        let ffObject = { y: 0, vy: 0, height: 50 };
        let ffTime = 0;
        let ffIsFalling = false;

        // Spring variables
        let spring = { y: 0, vy: 0, mass: 1, k: 100 };
        let springTime = 0;
        let springIsMoving = false;

        // Inclined Plane variables
        let ipObject = { x: 0, y: 0, vx: 0, angle: 30 };
        let ipTime = 0;
        let ipIsSliding = false;

        function setup() {
            canvas = createCanvas(800, 400);
            canvas.parent('canvas-container');
            resetFreeFall();
        }

        function draw() {
            background(220);
            fill(0, 128, 0);
            rect(0, height - 20, width, 20);

            if (currentTab === 'freeFall') {
                drawFreeFall();
            } else if (currentTab === 'spring') {
                drawSpring();
            } else if (currentTab === 'inclinedPlane') {
                drawInclinedPlane();
            }
        }

        function drawFreeFall() {
            if (ffIsFalling) {
                ffObject.y = height - (ffObject.height - 0.5 * g * ffTime * ffTime);
                ffTime += 0.05;
                if (ffObject.y > height - 10) {
                    ffIsFalling = false;
                    ffObject.y = height - 10;
                }
            }
            fill(255, 0, 0);
            ellipse(400, ffObject.y, 20, 20);
        }

        function drawSpring() {
            let anchorY = 100;
            let equilibrium = height - 200;
            if (springIsMoving) {
                let omega = Math.sqrt(spring.k / spring.mass);
                spring.y = equilibrium + 50 * Math.cos(omega * springTime);
                springTime += 0.05;
            }
            stroke(0);
            line(400, anchorY, 400, spring.y);
            fill(255, 0, 0);
            ellipse(400, spring.y, 20, 20);
        }

        function drawInclinedPlane() {
            let inclineLength = 400;
            let inclineHeight = inclineLength * Math.sin(radians(ipObject.angle));
            let startX = 200, startY = height - 20;
            let endX = startX + inclineLength * Math.cos(radians(ipObject.angle));
            let endY = startY - inclineHeight;
            stroke(0);
            line(startX, startY, endX, endY);
            if (ipIsSliding) {
                let a = g * Math.sin(radians(ipObject.angle));
                ipObject.x = startX + 0.5 * a * ipTime * ipTime;
                ipObject.y = startY - (ipObject.x - startX) * Math.tan(radians(ipObject.angle));
                ipTime += 0.05;
                if (ipObject.x > endX) {
                    ipIsSliding = false;
                    ipObject.x = endX;
                    ipObject.y = endY;
                }
            }
            fill(255, 0, 0);
            ellipse(ipObject.x, ipObject.y, 20, 20);
        }

        function startFreeFall() {
            if (!ffIsFalling) {
                ffObject.height = parseFloat(document.getElementById('height').value);
                ffObject.y = height - ffObject.height;
                ffTime = 0;
                ffIsFalling = true;
            }
        }

        function resetFreeFall() {
            ffObject.y = height - parseFloat(document.getElementById('height').value);
            ffIsFalling = false;
            ffTime = 0;
        }

        function startSpring() {
            if (!springIsMoving) {
                spring.mass = parseFloat(document.getElementById('mass').value);
                spring.k = parseFloat(document.getElementById('k').value);
                springTime = 0;
                springIsMoving = true;
            }
        }

        function resetSpring() {
            spring.y = height - 200;
            springIsMoving = false;
            springTime = 0;
        }

        function startInclinedPlane() {
            if (!ipIsSliding) {
                ipObject.angle = parseFloat(document.getElementById('incline-angle').value);
                ipObject.x = 200;
                ipObject.y = height - 20;
                ipTime = 0;
                ipIsSliding = true;
            }
        }

        function resetInclinedPlane() {
            ipObject.x = 200;
            ipObject.y = height - 20;
            ipIsSliding = false;
            ipTime = 0;
        }

        function switchTab(tab) {
            currentTab = tab;
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.control-panel').forEach(panel => panel.classList.remove('active'));
            document.getElementById(tab + '-controls').classList.add('active');
            document.querySelector(`button[onclick="switchTab('${tab}')"]`).classList.add('active');
            resetFreeFall();
            resetSpring();
            resetInclinedPlane();
        }

        // Update displayed values
        document.getElementById('height').addEventListener('input', function() {
            document.getElementById('height-value').textContent = this.value;
        });
        document.getElementById('mass').addEventListener('input', function() {
            document.getElementById('mass-value').textContent = this.value;
        });
        document.getElementById('k').addEventListener('input', function() {
            document.getElementById('k-value').textContent = this.value;
        });
        document.getElementById('incline-angle').addEventListener('input', function() {
            document.getElementById('incline-angle-value').textContent = this.value;
        });
    </script>
</body>
</html>