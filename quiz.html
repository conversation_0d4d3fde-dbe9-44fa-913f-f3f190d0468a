<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Physics Quiz - Physics Virtual Lab</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/components.css">
    <style>
        body {
            font-family: var(--font-family-sans);
            margin: 0;
            padding: 0;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .quiz-header {
            background: var(--assessment-color);
            color: white;
            padding: var(--spacing-lg);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .quiz-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
        }

        .quiz-progress {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .progress-bar {
            width: 200px;
            height: 8px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: white;
            border-radius: 4px;
            transition: width var(--transition-normal);
        }

        .timer {
            font-weight: 600;
            font-size: 1.125rem;
        }

        .quiz-container {
            flex: 1;
            max-width: 800px;
            margin: 0 auto;
            padding: var(--spacing-xl);
            width: 100%;
        }

        .question-card {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            padding: var(--spacing-xl);
            margin-bottom: var(--spacing-lg);
        }

        [data-theme="dark"] .question-card {
            background: var(--gray-700);
        }

        .question-number {
            color: var(--assessment-color);
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
        }

        .question-text {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-lg);
            line-height: 1.6;
        }

        .question-image {
            max-width: 100%;
            height: auto;
            border-radius: var(--radius-lg);
            margin: var(--spacing-lg) 0;
        }

        .options-container {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }

        .option {
            background: var(--bg-secondary);
            border: 2px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: var(--spacing-md);
            cursor: pointer;
            transition: all var(--transition-fast);
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .option:hover {
            border-color: var(--assessment-color);
            background: var(--accent-50);
        }

        [data-theme="dark"] .option:hover {
            background: var(--accent-900);
        }

        .option.selected {
            border-color: var(--assessment-color);
            background: var(--accent-100);
        }

        [data-theme="dark"] .option.selected {
            background: var(--accent-800);
        }

        .option.correct {
            border-color: var(--beginner-color);
            background: #f0fdf4;
        }

        [data-theme="dark"] .option.correct {
            background: #064e3b;
        }

        .option.incorrect {
            border-color: var(--advanced-color);
            background: #fef2f2;
        }

        [data-theme="dark"] .option.incorrect {
            background: #7f1d1d;
        }

        .option-letter {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--assessment-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            flex-shrink: 0;
        }

        .option-text {
            flex: 1;
            color: var(--text-primary);
        }

        .quiz-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: var(--spacing-xl);
        }

        .btn {
            padding: var(--spacing-md) var(--spacing-xl);
            border: none;
            border-radius: var(--radius-lg);
            font-weight: 600;
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .btn-primary {
            background: var(--assessment-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--accent-700);
        }

        .btn-secondary {
            background: var(--gray-200);
            color: var(--text-primary);
        }

        [data-theme="dark"] .btn-secondary {
            background: var(--gray-600);
        }

        .btn-secondary:hover {
            background: var(--gray-300);
        }

        [data-theme="dark"] .btn-secondary:hover {
            background: var(--gray-500);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .explanation {
            background: var(--accent-50);
            border: 1px solid var(--accent-200);
            border-radius: var(--radius-lg);
            padding: var(--spacing-md);
            margin-top: var(--spacing-md);
            display: none;
        }

        [data-theme="dark"] .explanation {
            background: var(--accent-900);
            border-color: var(--accent-700);
        }

        .explanation.show {
            display: block;
        }

        .explanation-title {
            font-weight: 600;
            color: var(--accent-700);
            margin-bottom: var(--spacing-sm);
        }

        [data-theme="dark"] .explanation-title {
            color: var(--accent-300);
        }

        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .results-content {
            background: white;
            border-radius: var(--radius-xl);
            padding: var(--spacing-2xl);
            max-width: 500px;
            width: 90%;
            text-align: center;
        }

        [data-theme="dark"] .results-content {
            background: var(--gray-800);
        }

        .results-score {
            font-size: 3rem;
            font-weight: 700;
            color: var(--assessment-color);
            margin-bottom: var(--spacing-md);
        }

        .results-message {
            font-size: 1.25rem;
            color: var(--text-primary);
            margin-bottom: var(--spacing-lg);
        }

        .results-details {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .result-stat {
            background: var(--bg-secondary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-md);
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-600);
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        @media (max-width: 768px) {
            .quiz-progress {
                flex-direction: column;
                gap: var(--spacing-sm);
            }
            
            .progress-bar {
                width: 150px;
            }
            
            .quiz-actions {
                flex-direction: column;
                gap: var(--spacing-md);
            }
            
            .results-details {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="quiz-header">
        <h1 class="quiz-title" id="quizTitle">Physics Quiz</h1>
        <div class="quiz-progress">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 0%"></div>
            </div>
            <div class="timer" id="timer">20:00</div>
        </div>
    </div>

    <div class="quiz-container">
        <div class="question-card animate-fade-in" id="questionCard">
            <div class="question-number" id="questionNumber">Question 1 of 10</div>
            <div class="question-text" id="questionText">Loading question...</div>
            <div class="options-container" id="optionsContainer">
                <!-- Options will be dynamically generated -->
            </div>
            <div class="explanation" id="explanation">
                <div class="explanation-title">Explanation:</div>
                <div id="explanationText"></div>
            </div>
        </div>

        <div class="quiz-actions">
            <button class="btn btn-secondary" id="prevButton" disabled>Previous</button>
            <div id="questionInfo">Question 1 of 10</div>
            <button class="btn btn-primary" id="nextButton">Next</button>
        </div>
    </div>

    <!-- Results Modal -->
    <div class="results-modal" id="resultsModal">
        <div class="results-content animate-scale-in">
            <div class="results-score" id="finalScore">85%</div>
            <div class="results-message" id="resultsMessage">Great job!</div>
            <div class="results-details">
                <div class="result-stat">
                    <div class="stat-value" id="correctAnswers">8</div>
                    <div class="stat-label">Correct</div>
                </div>
                <div class="result-stat">
                    <div class="stat-value" id="totalQuestions">10</div>
                    <div class="stat-label">Total</div>
                </div>
                <div class="result-stat">
                    <div class="stat-value" id="timeSpent">15:30</div>
                    <div class="stat-label">Time</div>
                </div>
            </div>
            <div style="display: flex; gap: var(--spacing-md); justify-content: center;">
                <button class="btn btn-secondary" onclick="reviewQuiz()">Review Answers</button>
                <button class="btn btn-primary" onclick="returnToDashboard()">Return to Dashboard</button>
            </div>
        </div>
    </div>

    <script type="module" src="js/main.js"></script>
    <script>
        // Quiz System
        class PhysicsQuiz {
            constructor() {
                this.currentQuestion = 0;
                this.questions = [];
                this.userAnswers = [];
                this.timeRemaining = 1200; // 20 minutes in seconds
                this.timerInterval = null;
                this.quizType = this.getQuizType();
                
                this.initializeQuiz();
            }

            getQuizType() {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get('type') || 'mechanics';
            }

            initializeQuiz() {
                this.loadQuestions();
                this.setupEventListeners();
                this.startTimer();
                this.displayQuestion();
            }

            loadQuestions() {
                // Sample questions - in a real app, these would come from a database
                const questionSets = {
                    mechanics: [
                        {
                            question: "What is the formula for calculating the period of a simple pendulum?",
                            options: [
                                "T = 2π√(L/g)",
                                "T = 2π√(g/L)",
                                "T = π√(L/g)",
                                "T = √(L/g)"
                            ],
                            correct: 0,
                            explanation: "The period of a simple pendulum is T = 2π√(L/g), where L is the length and g is gravitational acceleration."
                        },
                        {
                            question: "In projectile motion, what happens to the horizontal velocity component?",
                            options: [
                                "It increases due to gravity",
                                "It decreases due to air resistance",
                                "It remains constant (ignoring air resistance)",
                                "It varies with the angle of projection"
                            ],
                            correct: 2,
                            explanation: "In ideal projectile motion (no air resistance), the horizontal velocity component remains constant throughout the flight."
                        }
                    ],
                    electromagnetism: [
                        {
                            question: "According to Ohm's Law, if voltage doubles and resistance stays the same, what happens to current?",
                            options: [
                                "Current halves",
                                "Current doubles",
                                "Current stays the same",
                                "Current quadruples"
                            ],
                            correct: 1,
                            explanation: "According to Ohm's Law (V = IR), if voltage doubles and resistance remains constant, current must also double."
                        }
                    ]
                };

                this.questions = questionSets[this.quizType] || questionSets.mechanics;
                document.getElementById('quizTitle').textContent = `${this.quizType.charAt(0).toUpperCase() + this.quizType.slice(1)} Quiz`;
            }

            setupEventListeners() {
                document.getElementById('nextButton').addEventListener('click', () => this.nextQuestion());
                document.getElementById('prevButton').addEventListener('click', () => this.prevQuestion());
            }

            startTimer() {
                this.timerInterval = setInterval(() => {
                    this.timeRemaining--;
                    this.updateTimer();
                    
                    if (this.timeRemaining <= 0) {
                        this.finishQuiz();
                    }
                }, 1000);
            }

            updateTimer() {
                const minutes = Math.floor(this.timeRemaining / 60);
                const seconds = this.timeRemaining % 60;
                document.getElementById('timer').textContent = 
                    `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }

            displayQuestion() {
                const question = this.questions[this.currentQuestion];
                
                document.getElementById('questionNumber').textContent = 
                    `Question ${this.currentQuestion + 1} of ${this.questions.length}`;
                document.getElementById('questionText').textContent = question.question;
                document.getElementById('questionInfo').textContent = 
                    `Question ${this.currentQuestion + 1} of ${this.questions.length}`;

                // Update progress
                const progress = ((this.currentQuestion + 1) / this.questions.length) * 100;
                document.getElementById('progressFill').style.width = `${progress}%`;

                // Generate options
                const optionsContainer = document.getElementById('optionsContainer');
                optionsContainer.innerHTML = '';

                question.options.forEach((option, index) => {
                    const optionElement = document.createElement('div');
                    optionElement.className = 'option';
                    optionElement.innerHTML = `
                        <div class="option-letter">${String.fromCharCode(65 + index)}</div>
                        <div class="option-text">${option}</div>
                    `;
                    
                    optionElement.addEventListener('click', () => this.selectOption(index));
                    
                    if (this.userAnswers[this.currentQuestion] === index) {
                        optionElement.classList.add('selected');
                    }
                    
                    optionsContainer.appendChild(optionElement);
                });

                // Update navigation buttons
                document.getElementById('prevButton').disabled = this.currentQuestion === 0;
                document.getElementById('nextButton').textContent = 
                    this.currentQuestion === this.questions.length - 1 ? 'Finish Quiz' : 'Next';

                // Hide explanation
                document.getElementById('explanation').classList.remove('show');
            }

            selectOption(index) {
                this.userAnswers[this.currentQuestion] = index;
                
                // Update visual selection
                document.querySelectorAll('.option').forEach((option, i) => {
                    option.classList.toggle('selected', i === index);
                });
            }

            nextQuestion() {
                if (this.currentQuestion === this.questions.length - 1) {
                    this.finishQuiz();
                } else {
                    this.currentQuestion++;
                    this.displayQuestion();
                }
            }

            prevQuestion() {
                if (this.currentQuestion > 0) {
                    this.currentQuestion--;
                    this.displayQuestion();
                }
            }

            finishQuiz() {
                clearInterval(this.timerInterval);
                this.calculateResults();
                this.showResults();
            }

            calculateResults() {
                let correct = 0;
                this.questions.forEach((question, index) => {
                    if (this.userAnswers[index] === question.correct) {
                        correct++;
                    }
                });

                this.results = {
                    correct: correct,
                    total: this.questions.length,
                    percentage: Math.round((correct / this.questions.length) * 100),
                    timeSpent: 1200 - this.timeRemaining
                };
            }

            showResults() {
                const modal = document.getElementById('resultsModal');
                const { correct, total, percentage, timeSpent } = this.results;

                document.getElementById('finalScore').textContent = `${percentage}%`;
                document.getElementById('correctAnswers').textContent = correct;
                document.getElementById('totalQuestions').textContent = total;
                
                const minutes = Math.floor(timeSpent / 60);
                const seconds = timeSpent % 60;
                document.getElementById('timeSpent').textContent = 
                    `${minutes}:${seconds.toString().padStart(2, '0')}`;

                // Set message based on score
                let message = '';
                if (percentage >= 90) message = 'Excellent work!';
                else if (percentage >= 80) message = 'Great job!';
                else if (percentage >= 70) message = 'Good effort!';
                else if (percentage >= 60) message = 'Keep practicing!';
                else message = 'Review the material and try again.';

                document.getElementById('resultsMessage').textContent = message;
                modal.style.display = 'flex';

                // Save results to localStorage
                this.saveResults();
            }

            saveResults() {
                const assessmentData = JSON.parse(localStorage.getItem('assessmentProgress') || '{}');
                assessmentData[this.quizType] = {
                    score: this.results.percentage,
                    completed: true,
                    date: new Date().toISOString()
                };
                localStorage.setItem('assessmentProgress', JSON.stringify(assessmentData));
            }
        }

        // Global functions for modal buttons
        function reviewQuiz() {
            document.getElementById('resultsModal').style.display = 'none';
            // Show review mode with correct/incorrect answers
            quiz.showReview();
        }

        function returnToDashboard() {
            window.location.href = 'dashboard.html';
        }

        // Initialize quiz when page loads
        let quiz;
        document.addEventListener('DOMContentLoaded', () => {
            quiz = new PhysicsQuiz();
        });
    </script>
</body>
</html>
