<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Projectile Motion Lab - Physics Virtual Lab</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/animations.css">
    <link rel="stylesheet" href="../css/components.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.7.0/p5.min.js"></script>
    <style>
        body {
            font-family: var(--font-family-sans);
            margin: 0;
            padding: 0;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .experiment-header {
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--border-color);
            padding: var(--spacing-md);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .experiment-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }
        
        .back-button {
            background: var(--primary-600);
            color: white;
            border: none;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-md);
            text-decoration: none;
            font-weight: 500;
            transition: all var(--transition-fast);
        }
        
        .back-button:hover {
            background: var(--primary-700);
            transform: translateY(-2px);
        }

        .main-content {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-lg);
            padding: var(--spacing-lg);
            max-width: 1400px;
            margin: 0 auto;
            width: 100%;
        }

        .left-panel, .right-panel {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
        }

        .panel-section {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            padding: var(--spacing-lg);
        }

        [data-theme="dark"] .panel-section {
            background: var(--gray-700);
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .simulation-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-md);
        }

        .control-group {
            margin-bottom: var(--spacing-md);
        }

        .control-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 500;
            color: var(--text-primary);
        }

        .control-slider {
            width: 100%;
            height: 8px;
            border-radius: 4px;
            background: var(--gray-200);
            outline: none;
            cursor: pointer;
            margin-bottom: var(--spacing-sm);
            accent-color: var(--primary-600);
        }

        [data-theme="dark"] .control-slider {
            background: var(--gray-600);
        }

        .control-value {
            font-weight: 600;
            color: var(--primary-600);
            text-align: center;
            padding: var(--spacing-sm);
            background: var(--bg-secondary);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
        }

        .control-buttons {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--spacing-md);
        }

        .btn {
            flex: 1;
            padding: var(--spacing-sm) var(--spacing-md);
            border: none;
            border-radius: var(--radius-md);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .btn-primary {
            background: var(--primary-600);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-700);
        }

        .btn-secondary {
            background: var(--gray-200);
            color: var(--text-primary);
        }

        [data-theme="dark"] .btn-secondary {
            background: var(--gray-600);
        }

        .btn-secondary:hover {
            background: var(--gray-300);
        }

        [data-theme="dark"] .btn-secondary:hover {
            background: var(--gray-500);
        }

        .physics-info {
            background: var(--accent-50);
            border: 2px solid var(--accent-200);
            border-radius: var(--radius-lg);
            padding: var(--spacing-md);
            margin-top: var(--spacing-md);
        }

        [data-theme="dark"] .physics-info {
            background: var(--accent-900);
            border-color: var(--accent-700);
        }

        .formula {
            font-size: 1.125rem;
            font-weight: 700;
            color: var(--accent-700);
            margin-bottom: var(--spacing-sm);
            text-align: center;
        }

        [data-theme="dark"] .formula {
            color: var(--accent-300);
        }

        .data-display {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: var(--spacing-sm);
            margin-top: var(--spacing-md);
        }

        .data-card {
            background: var(--bg-primary);
            border-radius: var(--radius-md);
            padding: var(--spacing-sm);
            text-align: center;
            border: 1px solid var(--border-color);
        }

        .data-label {
            font-size: 0.75rem;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-xs);
        }

        .data-value {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--primary-600);
        }

        .trajectory-info {
            background: var(--bg-secondary);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            margin-top: var(--spacing-md);
        }

        .trajectory-info h4 {
            margin: 0 0 var(--spacing-sm) 0;
            color: var(--text-primary);
        }

        .trajectory-info ul {
            margin: 0;
            padding-left: var(--spacing-lg);
            color: var(--text-secondary);
        }

        .trajectory-info li {
            margin-bottom: var(--spacing-xs);
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                padding: var(--spacing-md);
            }
            
            .control-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="experiment-header">
        <h1 class="experiment-title">🚀 Projectile Motion Laboratory</h1>
        <a href="../dashboard.html" class="back-button">← Back to Dashboard</a>
    </div>

    <div class="main-content">
        <div class="left-panel">
            <div class="panel-section animate-fade-in">
                <h2 class="section-title">🎯 Launch Simulation</h2>
                <div class="simulation-container">
                    <div id="simulation-canvas"></div>
                    
                    <div class="control-group">
                        <label for="angleSlider" class="control-label">Launch Angle:</label>
                        <input type="range" id="angleSlider" class="control-slider" min="0" max="90" value="45">
                        <div class="control-value">
                            Angle: <span id="angleValue">45</span>°
                        </div>
                    </div>

                    <div class="control-group">
                        <label for="velocitySlider" class="control-label">Initial Velocity:</label>
                        <input type="range" id="velocitySlider" class="control-slider" min="10" max="50" value="20">
                        <div class="control-value">
                            Velocity: <span id="velocityValue">20</span> m/s
                        </div>
                    </div>

                    <div class="control-buttons">
                        <button id="launchButton" class="btn btn-primary">Launch Projectile</button>
                        <button id="resetButton" class="btn btn-secondary">Reset</button>
                    </div>
                </div>

                <div class="physics-info">
                    <div class="formula">x = v₀cos(θ)t</div>
                    <div class="formula">y = v₀sin(θ)t - ½gt²</div>
                    <p style="text-align: center; font-size: 0.875rem; color: var(--text-secondary); margin: 0;">
                        Projectile motion equations
                    </p>
                </div>
            </div>
        </div>

        <div class="right-panel">
            <div class="panel-section animate-slide-up">
                <h2 class="section-title">📊 Flight Data</h2>
                <div class="data-display">
                    <div class="data-card">
                        <div class="data-label">Range</div>
                        <div class="data-value"><span id="rangeValue">0.0</span> m</div>
                    </div>
                    <div class="data-card">
                        <div class="data-label">Max Height</div>
                        <div class="data-value"><span id="heightValue">0.0</span> m</div>
                    </div>
                    <div class="data-card">
                        <div class="data-label">Flight Time</div>
                        <div class="data-value"><span id="timeValue">0.0</span> s</div>
                    </div>
                    <div class="data-card">
                        <div class="data-label">Impact Velocity</div>
                        <div class="data-value"><span id="impactValue">0.0</span> m/s</div>
                    </div>
                </div>

                <div class="trajectory-info">
                    <h4>Understanding Projectile Motion</h4>
                    <ul>
                        <li><strong>45° angle</strong> gives maximum range for level ground</li>
                        <li><strong>Higher angles</strong> increase flight time and height</li>
                        <li><strong>Lower angles</strong> reduce flight time but may increase range</li>
                        <li><strong>Air resistance</strong> is ignored in this simulation</li>
                        <li><strong>Gravity</strong> acts downward at 9.81 m/s²</li>
                    </ul>
                </div>
            </div>

            <div class="panel-section animate-slide-up animate-stagger-1">
                <h2 class="section-title">🎓 Learning Objectives</h2>
                <div style="color: var(--text-secondary); line-height: 1.6;">
                    <p><strong>After this experiment, you should understand:</strong></p>
                    <ul>
                        <li>How launch angle affects projectile trajectory</li>
                        <li>The relationship between velocity and range</li>
                        <li>How gravity influences projectile motion</li>
                        <li>The independence of horizontal and vertical motion</li>
                        <li>Real-world applications of projectile motion</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script type="module" src="../js/main.js"></script>
    <script>
        // Projectile Motion Simulation using p5.js
        let ball;
        let angle = 45;
        let velocity = 20;
        let isLaunched = false;
        let g = 9.81;
        let t = 0;
        let trajectoryPoints = [];
        let scale = 8; // pixels per meter

        function setup() {
            let canvas = createCanvas(600, 400);
            canvas.parent('simulation-canvas');
            ball = { x: 0, y: 0, vx: 0, vy: 0 };
            resetSimulation();
        }

        function draw() {
            background(135, 206, 235); // Sky blue
            
            // Draw ground
            fill(34, 139, 34);
            rect(0, height - 40, width, 40);
            
            // Draw trajectory path
            if (trajectoryPoints.length > 1) {
                stroke(255, 255, 255, 150);
                strokeWeight(2);
                noFill();
                beginShape();
                for (let point of trajectoryPoints) {
                    vertex(point.x, point.y);
                }
                endShape();
            }
            
            if (isLaunched) {
                // Update position
                ball.x = ball.vx * t * scale;
                ball.y = height - 40 - (ball.vy * t - 0.5 * g * t * t) * scale;
                
                // Store trajectory point
                trajectoryPoints.push({x: ball.x, y: ball.y});
                
                t += 0.02;
                
                // Check if ball hits ground
                if (ball.y >= height - 40) {
                    ball.y = height - 40;
                    isLaunched = false;
                    updateFlightData();
                }
                
                // Check if ball goes off screen
                if (ball.x > width) {
                    isLaunched = false;
                    updateFlightData();
                }
            }
            
            // Draw ball
            fill(255, 69, 0);
            noStroke();
            ellipse(ball.x + 20, ball.y, 16, 16);
            
            // Draw launch indicator
            if (!isLaunched) {
                stroke(255, 0, 0);
                strokeWeight(3);
                let indicatorLength = 50;
                let endX = 20 + indicatorLength * cos(radians(angle));
                let endY = height - 40 - indicatorLength * sin(radians(angle));
                line(20, height - 40, endX, endY);
            }
        }

        function startSimulation() {
            if (!isLaunched) {
                angle = parseFloat(document.getElementById('angleSlider').value);
                velocity = parseFloat(document.getElementById('velocitySlider').value);
                
                ball.vx = velocity * cos(radians(angle));
                ball.vy = velocity * sin(radians(angle));
                ball.x = 0;
                ball.y = height - 40;
                t = 0;
                trajectoryPoints = [];
                isLaunched = true;
                
                // Clear previous data
                document.getElementById('rangeValue').textContent = '0.0';
                document.getElementById('heightValue').textContent = '0.0';
                document.getElementById('timeValue').textContent = '0.0';
                document.getElementById('impactValue').textContent = '0.0';
            }
        }

        function resetSimulation() {
            ball.x = 0;
            ball.y = height - 40;
            isLaunched = false;
            t = 0;
            trajectoryPoints = [];
            
            // Reset data display
            document.getElementById('rangeValue').textContent = '0.0';
            document.getElementById('heightValue').textContent = '0.0';
            document.getElementById('timeValue').textContent = '0.0';
            document.getElementById('impactValue').textContent = '0.0';
        }

        function updateFlightData() {
            // Calculate theoretical values
            const angleRad = radians(angle);
            const range = (velocity * velocity * sin(2 * angleRad)) / g;
            const maxHeight = (velocity * velocity * sin(angleRad) * sin(angleRad)) / (2 * g);
            const flightTime = (2 * velocity * sin(angleRad)) / g;
            const impactVelocity = velocity; // In vacuum, impact velocity equals initial velocity
            
            document.getElementById('rangeValue').textContent = range.toFixed(1);
            document.getElementById('heightValue').textContent = maxHeight.toFixed(1);
            document.getElementById('timeValue').textContent = flightTime.toFixed(1);
            document.getElementById('impactValue').textContent = impactVelocity.toFixed(1);
        }

        // Event listeners
        document.getElementById('angleSlider').addEventListener('input', function() {
            document.getElementById('angleValue').textContent = this.value;
            if (!isLaunched) {
                angle = parseFloat(this.value);
            }
        });

        document.getElementById('velocitySlider').addEventListener('input', function() {
            document.getElementById('velocityValue').textContent = this.value;
            if (!isLaunched) {
                velocity = parseFloat(this.value);
            }
        });

        document.getElementById('launchButton').addEventListener('click', startSimulation);
        document.getElementById('resetButton').addEventListener('click', resetSimulation);
    </script>
</body>
</html>
