<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>'s Law Experiment</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
            color: #333;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        header {
            background: #333;
            color: #fff;
            padding: 1rem 0;
            text-align: center;
        }

        main {
            flex: 1;
            max-width: 900px;
            margin: 20px auto;
            padding: 20px;
            background: #fff;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        h1, h2, h3 {
            color: #333;
        }
        
        h2#stepTitle {
            color: #007bff;
        }

        #labArea {
            display: flex;
            flex-wrap: wrap;
            align-items: flex-start; /* Align items to the top */
            gap: 20px;
        }

        #gasContainerWrapper {
            flex: 1 1 200px; /* Grow, shrink, basis 200px */
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 150px; /* Ensure it doesn't get too small */
        }
        
        #gasContainerDescription {
            text-align: center;
            margin-bottom: 10px;
            font-size: 0.9em;
            color: #555;
        }

        #gasContainer {
            width: 100px;
            height: 200px; /* Represents MAX_VOLUME visually */
            border: 2px solid black;
            position: relative;
            background-color: #e0e0e0; /* Cylinder background */
            overflow: hidden; /* Ensure piston doesn't visually escape */
        }

        #gasVolumeVisual {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            background-color: #add8e6; /* Light blue for gas */
            transition: height 0.1s ease-out;
        }

        #piston {
            position: absolute;
            left: 0;
            width: 100%;
            height: 10px; /* PISTON_HEIGHT_PX */
            background-color: #888; /* Grey for piston */
            border-top: 2px solid #555;
            transition: bottom 0.1s ease-out;
        }

        #controls {
            flex: 2 1 300px; /* Grow, shrink, basis 300px */
            padding-top: 20px; /* Align controls better with centered container */
        }
        
        #controls label, #controls p {
            display: block;
            margin-bottom: 10px;
        }

        #volumeSlider {
            width: 100%;
            max-width: 300px;
        }

        #recordDataButton, #prevStepButton, #nextStepButton {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }

        #recordDataButton:disabled, #prevStepButton:disabled, #nextStepButton:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        #recordDataButton {
            background-color: #28a745; /* Green */
        }
        #recordDataButton:disabled {
            background-color: #ccc;
        }


        #dataTable {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        #dataTable th, #dataTable td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }

        #dataTable th {
            background-color: #f0f0f0;
        }

        #pvGraph {
            width: 100%;
            /* height will be set in JS for aspect ratio, or fixed here e.g. height: 300px; */
            border: 1px solid #ccc;
            background-color: #f9f9f9;
        }

        #navigation {
            text-align: right;
        }

        footer {
            text-align: center;
            padding: 1rem 0;
            background: #333;
            color: #fff;
            font-size: 0.9em;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            main {
                margin: 10px;
                padding: 15px;
            }
            #labArea {
                flex-direction: column;
                align-items: center; /* Center items when stacked */
            }
            #gasContainerWrapper, #controls {
                flex-basis: auto; /* Reset basis for column layout */
                width: 100%; /* Take full width */
            }
            #controls {
                padding-top: 10px;
            }
        }
    </style>
</head>
<body>
    <header>
        <h1>Understanding Scientific Discovery: Boyle's Law</h1>
    </header>

    <main>
        <section id="instructionsArea">
            <h2 id="stepTitle"></h2>
            <p id="stepInstructions"></p>
        </section>

        <section id="labArea">
            <div id="gasContainerWrapper">
                <p id="gasContainerDescription">Gas Cylinder with Movable Piston</p>
                <div id="gasContainer">
                    <div id="gasVolumeVisual"></div>
                    <div id="piston"></div>
                </div>
            </div>
            <div id="controls">
                <label for="volumeSlider">Volume: <span id="volumeValue">50</span> L</label>
                <input type="range" id="volumeSlider" min="10" max="100" value="50" step="1">
                <p>Pressure: <span id="pressureValue">100.0</span> kPa</p>
            </div>
        </section>

        <section id="dataSection">
            <button id="recordDataButton">Record Data</button>
            <h3>Recorded Data</h3>
            <table id="dataTable">
                <thead>
                    <tr>
                        <th>Volume (L)</th>
                        <th>Pressure (kPa)</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data rows will be inserted here -->
                </tbody>
            </table>
        </section>

        <section id="graphSection">
            <h3>Pressure vs. Volume Graph</h3>
            <canvas id="pvGraph"></canvas>
        </section>

        <section id="navigation">
            <button id="prevStepButton">Previous Step</button>
            <button id="nextStepButton">Next Step</button>
        </section>
    </main>

    <footer>
        <p>Interactive Boyle's Law Experiment &copy; 2023</p>
    </footer>

    <script>
        // Constants for Boyle's Law and simulation
        const PV_CONSTANT = 5000; // P*V = k (e.g., 50 L * 100 kPa = 5000 kPa*L)
        const MIN_VOLUME = 10; // Liters
        const MAX_VOLUME = 100; // Liters
        const CONTAINER_VISUAL_HEIGHT_PX = 200; // Visual height of the gas container in pixels
        const PISTON_HEIGHT_PX = 10; // Visual height of the piston in pixels

        // DOM Elements
        const stepTitleElement = document.getElementById('stepTitle');
        const stepInstructionsElement = document.getElementById('stepInstructions');
        
        const volumeSlider = document.getElementById('volumeSlider');
        const volumeValueElement = document.getElementById('volumeValue');
        const pressureValueElement = document.getElementById('pressureValue');
        
        const gasContainerElement = document.getElementById('gasContainer');
        const gasVolumeVisualElement = document.getElementById('gasVolumeVisual');
        const pistonElement = document.getElementById('piston');
        
        const recordDataButton = document.getElementById('recordDataButton');
        const dataTableBody = document.querySelector('#dataTable tbody');
        
        const pvGraphCanvas = document.getElementById('pvGraph');
        const ctx = pvGraphCanvas.getContext('2d');
        
        const prevStepButton = document.getElementById('prevStepButton');
        const nextStepButton = document.getElementById('nextStepButton');

        // State Variables
        let currentVolume = parseFloat(volumeSlider.value);
        let currentPressure = PV_CONSTANT / currentVolume;
        let recordedData = [];
        let currentStep = 1;

        const stepConfig = {
            1: {
                title: "STEP 1: Set up the experiment",
                instructions: "Adjust the initial volume of the gas in the container using the slider. Observe how the pressure changes. When you are ready to proceed, click 'Next Step'.",
                enableRecord: false,
                enableSlider: true,
            },
            2: {
                title: "STEP 2: Conduct the experiment",
                instructions: "Now, systematically change the volume using the slider. For each volume setting you want to measure, click 'Record Data'. Try to collect at least 5-7 data points across a range of volumes. Then, click 'Next Step'.",
                enableRecord: true,
                enableSlider: true,
            },
            3: {
                title: "STEP 3: Analyze the data",
                instructions: "Observe the data table and the graph of Pressure vs. Volume. What pattern do you notice? As volume decreases, pressure increases, and vice-versa. This inverse relationship is known as Boyle's Law (P₁V₁ = P₂V₂ at constant temperature and amount of gas). You can still adjust the volume to see its effect, but new data cannot be recorded in this step.",
                enableRecord: false,
                enableSlider: true, // Allow further exploration
            }
        };
        
        // --- Initialization ---
        function init() {
            volumeSlider.min = MIN_VOLUME;
            volumeSlider.max = MAX_VOLUME;
            volumeSlider.value = currentVolume;
            
            updateVolume(currentVolume);
            updateUIForStep();
            setupCanvas();
            drawGraph(); // Initial empty graph

            volumeSlider.addEventListener('input', handleVolumeChange);
            recordDataButton.addEventListener('click', handleRecordData);
            nextStepButton.addEventListener('click', handleNextStep);
            prevStepButton.addEventListener('click', handlePrevStep);
            window.addEventListener('resize', () => {
                setupCanvas(); // Adjust canvas size
                drawGraph();   // Redraw graph on resize
            });
        }

        // --- Core Logic Functions ---
        function updateVolume(newVolume) {
            currentVolume = parseFloat(newVolume);
            currentPressure = PV_CONSTANT / currentVolume;

            volumeValueElement.textContent = currentVolume.toFixed(0);
            pressureValueElement.textContent = currentPressure.toFixed(1);

            updatePistonVisual(currentVolume);
        }

        function updatePistonVisual(volume) {
            const gasHeightPercentage = (volume - MIN_VOLUME) / (MAX_VOLUME - MIN_VOLUME);
            let gasVisualHeight = gasHeightPercentage * CONTAINER_VISUAL_HEIGHT_PX;
            
            // Ensure gas height is not less than 0 if MIN_VOLUME is the base
            gasVisualHeight = Math.max(0, gasVisualHeight); 
            // Ensure gas height does not exceed container height
            gasVisualHeight = Math.min(CONTAINER_VISUAL_HEIGHT_PX, gasVisualHeight);


            // A more direct calculation if 0 volume means 0 height and MAX_VOLUME means CONTAINER_VISUAL_HEIGHT_PX
            const visualVolumeRatio = currentVolume / MAX_VOLUME;
            gasVisualHeight = visualVolumeRatio * CONTAINER_VISUAL_HEIGHT_PX;
            
            gasVolumeVisualElement.style.height = `${gasVisualHeight}px`;
            pistonElement.style.bottom = `${gasVisualHeight}px`;
        }

        function handleVolumeChange(event) {
            updateVolume(event.target.value);
             // If we want a "live point" on the graph, we'd call drawGraph here too,
             // potentially with an extra argument for the live point.
             // For simplicity, graph only updates on recorded data.
        }

        function handleRecordData() {
            if (currentStep !== 2) return; // Only record in Step 2

            // Avoid duplicate entries for the exact same volume
            if (!recordedData.some(data => data.volume === currentVolume)) {
                 recordedData.push({ volume: currentVolume, pressure: currentPressure });
                 recordedData.sort((a, b) => a.volume - b.volume); // Keep data sorted by volume
                 updateDataTable();
                 drawGraph();
            } else {
                alert("Data for this volume already recorded. Please choose a different volume.");
            }
        }

        function updateDataTable() {
            dataTableBody.innerHTML = ''; // Clear existing rows
            recordedData.forEach(data => {
                const row = dataTableBody.insertRow();
                const cellVolume = row.insertCell();
                const cellPressure = row.insertCell();
                cellVolume.textContent = data.volume.toFixed(0);
                cellPressure.textContent = data.pressure.toFixed(1);
            });
        }

        // --- Step Navigation ---
        function updateUIForStep() {
            const config = stepConfig[currentStep];
            stepTitleElement.textContent = config.title;
            stepInstructionsElement.innerHTML = config.instructions; // Use innerHTML if instructions contain HTML like <b>

            recordDataButton.disabled = !config.enableRecord;
            volumeSlider.disabled = !config.enableSlider;

            prevStepButton.disabled = (currentStep === 1);
            nextStepButton.disabled = (currentStep === Object.keys(stepConfig).length);
        }

        function handleNextStep() {
            if (currentStep < Object.keys(stepConfig).length) {
                currentStep++;
                updateUIForStep();
            }
        }

        function handlePrevStep() {
            if (currentStep > 1) {
                currentStep--;
                updateUIForStep();
            }
        }

        // --- Canvas and Graphing ---
        const GRAPH_PADDING = 50; // Increased padding for labels
        let MAX_CALCULATED_PRESSURE, MIN_CALCULATED_PRESSURE;
        let MAX_Y_AXIS_VALUE;

        function setupCanvas() {
            // Make canvas drawing surface match its CSS-defined size for responsiveness
            pvGraphCanvas.width = pvGraphCanvas.offsetWidth;
            // Set a fixed aspect ratio, e.g., 3:2 or 4:3
            pvGraphCanvas.height = pvGraphCanvas.offsetWidth * 0.65; 
            
            // Calculate pressure range for graph scaling
            MAX_CALCULATED_PRESSURE = PV_CONSTANT / MIN_VOLUME;
            MIN_CALCULATED_PRESSURE = PV_CONSTANT / MAX_VOLUME;
            // Round max Y axis value to the next 50 or 100 for nicer ticks
            MAX_Y_AXIS_VALUE = Math.ceil(MAX_CALCULATED_PRESSURE / 50) * 50 + 50; // Add some headroom
        }


        function mapRange(value, inMin, inMax, outMin, outMax) {
            return (value - inMin) * (outMax - outMin) / (inMax - inMin) + outMin;
        }

        function drawGraph() {
            ctx.clearRect(0, 0, pvGraphCanvas.width, pvGraphCanvas.height);

            const graphWidth = pvGraphCanvas.width - 2 * GRAPH_PADDING;
            const graphHeight = pvGraphCanvas.height - 2 * GRAPH_PADDING;

            // Draw Axes
            ctx.beginPath();
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 1;
            // Y-axis
            ctx.moveTo(GRAPH_PADDING, GRAPH_PADDING);
            ctx.lineTo(GRAPH_PADDING, GRAPH_PADDING + graphHeight);
            // X-axis
            ctx.moveTo(GRAPH_PADDING, GRAPH_PADDING + graphHeight);
            ctx.lineTo(GRAPH_PADDING + graphWidth, GRAPH_PADDING + graphHeight);
            ctx.stroke();

            // Axis Labels
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'top';
            ctx.fillText('Volume (L)', GRAPH_PADDING + graphWidth / 2, GRAPH_PADDING + graphHeight + 25);
            
            ctx.save();
            ctx.translate(GRAPH_PADDING - 30, GRAPH_PADDING + graphHeight / 2);
            ctx.rotate(-Math.PI / 2);
            ctx.textAlign = 'center';
            ctx.textBaseline = 'bottom';
            ctx.fillText('Pressure (kPa)', 0, 0);
            ctx.restore();

            // Draw Ticks and Grid Lines
            // X-axis (Volume)
            const xTickCount = 10; // Approx number of ticks
            const xTickInterval = Math.max(1, Math.ceil((MAX_VOLUME - MIN_VOLUME) / xTickCount / 10) * 10); // e.g. 10L
            ctx.textAlign = 'center';
            ctx.textBaseline = 'top';
            for (let v = MIN_VOLUME; v <= MAX_VOLUME; v += xTickInterval) {
                const x = mapRange(v, MIN_VOLUME, MAX_VOLUME, GRAPH_PADDING, GRAPH_PADDING + graphWidth);
                ctx.moveTo(x, GRAPH_PADDING + graphHeight);
                ctx.lineTo(x, GRAPH_PADDING + graphHeight + 5);
                ctx.fillText(v.toFixed(0), x, GRAPH_PADDING + graphHeight + 8);
                // Optional grid line
                ctx.beginPath();
                ctx.strokeStyle = '#e0e0e0';
                ctx.moveTo(x, GRAPH_PADDING);
                ctx.lineTo(x, GRAPH_PADDING + graphHeight);
                ctx.stroke();
                ctx.strokeStyle = '#333'; // Reset for ticks
            }

            // Y-axis (Pressure)
            const yTickCount = 6; // Approx number of ticks
            const yTickInterval = Math.ceil(MAX_Y_AXIS_VALUE / yTickCount / 50) * 50; // e.g. 50 or 100 kPa
            ctx.textAlign = 'right';
            ctx.textBaseline = 'middle';
            for (let p = 0; p <= MAX_Y_AXIS_VALUE; p += yTickInterval) {
                if (p > MAX_Y_AXIS_VALUE && p !== 0) continue; // Avoid drawing tick beyond max if interval makes it so
                const y = mapRange(p, 0, MAX_Y_AXIS_VALUE, GRAPH_PADDING + graphHeight, GRAPH_PADDING);
                ctx.moveTo(GRAPH_PADDING, y);
                ctx.lineTo(GRAPH_PADDING - 5, y);
                ctx.fillText(p.toFixed(0), GRAPH_PADDING - 8, y);
                // Optional grid line
                ctx.beginPath();
                ctx.strokeStyle = '#e0e0e0';
                ctx.moveTo(GRAPH_PADDING, y);
                ctx.lineTo(GRAPH_PADDING + graphWidth, y);
                ctx.stroke();
                ctx.strokeStyle = '#333'; // Reset for ticks
            }
            ctx.strokeStyle = '#333'; // Ensure it's reset

            // Plot Data Points
            ctx.fillStyle = '#007bff'; // Blue for data points
            recordedData.forEach(data => {
                const x = mapRange(data.volume, MIN_VOLUME, MAX_VOLUME, GRAPH_PADDING, GRAPH_PADDING + graphWidth);
                const y = mapRange(data.pressure, 0, MAX_Y_AXIS_VALUE, GRAPH_PADDING + graphHeight, GRAPH_PADDING);
                
                ctx.beginPath();
                ctx.arc(x, y, 4, 0, 2 * Math.PI); // Draw a small circle
                ctx.fill();
            });

            // Optionally, draw a line connecting points if sorted by volume (which they are)
            if (recordedData.length > 1) {
                ctx.beginPath();
                ctx.strokeStyle = '#28a745'; // Green line
                ctx.lineWidth = 2;
                recordedData.forEach((data, index) => {
                    const x = mapRange(data.volume, MIN_VOLUME, MAX_VOLUME, GRAPH_PADDING, GRAPH_PADDING + graphWidth);
                    const y = mapRange(data.pressure, 0, MAX_Y_AXIS_VALUE, GRAPH_PADDING + graphHeight, GRAPH_PADDING);
                    if (index === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                });
                ctx.stroke();
            }
        }

        // --- Start the app ---
        document.addEventListener('DOMContentLoaded', init);

    </script>
</body>
</html>

