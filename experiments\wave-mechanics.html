<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wave Mechanics Laboratory - Physics Virtual Lab</title>
    <meta name="author" content="Dr<PERSON> <PERSON>, SUST-BME">
    <meta name="copyright" content="Dr. <PERSON>il - +249912867327, +966538076790">
    <meta name="institution" content="Sudan University of Science and Technology - Biomedical Engineering">
    <meta name="description" content="Interactive wave mechanics simulation for studying wave properties, interference, and wave behavior">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/animations.css">
    <link rel="stylesheet" href="../css/components.css">
    <style>
        body {
            font-family: var(--font-family-sans);
            margin: 0;
            padding: 0;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .experiment-header {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            color: white;
            padding: var(--spacing-lg);
            text-align: center;
        }

        .experiment-title {
            font-size: 2rem;
            font-weight: 700;
            margin: 0 0 var(--spacing-sm) 0;
        }

        .experiment-subtitle {
            font-size: 1.125rem;
            opacity: 0.9;
            margin: 0;
        }

        .header-actions {
            display: flex;
            justify-content: center;
            gap: var(--spacing-md);
            margin-top: var(--spacing-lg);
        }

        .nav-button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-md);
            text-decoration: none;
            font-weight: 500;
            transition: all var(--transition-fast);
        }

        .nav-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .main-content {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 350px;
            gap: var(--spacing-lg);
            padding: var(--spacing-lg);
            max-width: 1400px;
            margin: 0 auto;
            width: 100%;
        }

        .simulation-panel {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
        }

        .controls-panel {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
        }

        .panel-section {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            padding: var(--spacing-lg);
        }

        [data-theme="dark"] .panel-section {
            background: var(--gray-700);
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .wave-canvas {
            width: 100%;
            height: 400px;
            border: 2px solid var(--border-color);
            border-radius: var(--radius-lg);
            background: var(--bg-primary);
        }

        .control-group {
            margin-bottom: var(--spacing-md);
        }

        .control-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 500;
            color: var(--text-primary);
        }

        .control-slider {
            width: 100%;
            height: 8px;
            border-radius: 4px;
            background: var(--gray-200);
            outline: none;
            cursor: pointer;
            margin-bottom: var(--spacing-sm);
            accent-color: var(--primary-600);
        }

        [data-theme="dark"] .control-slider {
            background: var(--gray-600);
        }

        .control-value {
            font-weight: 600;
            color: var(--primary-600);
            text-align: center;
            padding: var(--spacing-sm);
            background: var(--bg-secondary);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
        }

        .wave-type-selector {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
        }

        .wave-type-btn {
            padding: var(--spacing-sm);
            border: 2px solid var(--border-color);
            border-radius: var(--radius-md);
            background: var(--bg-secondary);
            color: var(--text-primary);
            cursor: pointer;
            transition: all var(--transition-fast);
            text-align: center;
            font-weight: 500;
        }

        .wave-type-btn.active {
            border-color: var(--primary-600);
            background: var(--primary-100);
            color: var(--primary-700);
        }

        [data-theme="dark"] .wave-type-btn.active {
            background: var(--primary-900);
            color: var(--primary-300);
        }

        .wave-type-btn:hover {
            border-color: var(--primary-500);
        }

        .control-buttons {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--spacing-md);
        }

        .btn {
            flex: 1;
            padding: var(--spacing-sm) var(--spacing-md);
            border: none;
            border-radius: var(--radius-md);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .btn-primary {
            background: var(--primary-600);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-700);
        }

        .btn-secondary {
            background: var(--gray-200);
            color: var(--text-primary);
        }

        [data-theme="dark"] .btn-secondary {
            background: var(--gray-600);
        }

        .btn-secondary:hover {
            background: var(--gray-300);
        }

        [data-theme="dark"] .btn-secondary:hover {
            background: var(--gray-500);
        }

        .wave-properties {
            background: var(--accent-50);
            border: 1px solid var(--accent-200);
            border-radius: var(--radius-lg);
            padding: var(--spacing-md);
        }

        [data-theme="dark"] .wave-properties {
            background: var(--accent-900);
            border-color: var(--accent-700);
        }

        .property-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: var(--spacing-sm);
        }

        .property-label {
            font-weight: 500;
            color: var(--text-primary);
        }

        .property-value {
            font-weight: 600;
            color: var(--accent-600);
        }

        [data-theme="dark"] .property-value {
            color: var(--accent-400);
        }

        .formula-section {
            background: var(--primary-50);
            border: 1px solid var(--primary-200);
            border-radius: var(--radius-lg);
            padding: var(--spacing-md);
            margin-top: var(--spacing-md);
        }

        [data-theme="dark"] .formula-section {
            background: var(--primary-900);
            border-color: var(--primary-700);
        }

        .formula {
            font-size: 1.125rem;
            font-weight: 700;
            color: var(--primary-700);
            text-align: center;
            margin-bottom: var(--spacing-sm);
        }

        [data-theme="dark"] .formula {
            color: var(--primary-300);
        }

        .formula-description {
            font-size: 0.875rem;
            color: var(--text-secondary);
            text-align: center;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                padding: var(--spacing-md);
            }
            
            .header-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .wave-type-selector {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header id="main-header"></header>
    
    <div class="experiment-header animate-fade-in">
        <h1 class="experiment-title">🌊 Wave Mechanics Laboratory</h1>
        <p class="experiment-subtitle">Explore wave properties, interference, and wave behavior</p>
        <div class="header-actions">
            <a href="../experiments-hub.html" class="nav-button">← Experiments Hub</a>
            <a href="../video-library.html?topic=waves" class="nav-button">📹 Watch Video</a>
            <a href="../assessment.html?topic=waves" class="nav-button">🎓 Take Quiz</a>
            <a href="../dashboard.html" class="nav-button">📊 Dashboard</a>
        </div>
    </div>

    <div class="main-content">
        <div class="simulation-panel">
            <div class="panel-section animate-slide-up">
                <h2 class="section-title">🌊 Wave Simulation</h2>
                <canvas id="waveCanvas" class="wave-canvas"></canvas>
                
                <div class="control-buttons">
                    <button type="button" id="playPauseBtn" class="btn btn-primary">Play</button>
                    <button type="button" id="resetBtn" class="btn btn-secondary">Reset</button>
                    <button type="button" id="interferenceBtn" class="btn btn-secondary">Add Interference</button>
                </div>
            </div>
        </div>

        <div class="controls-panel">
            <div class="panel-section animate-slide-left">
                <h2 class="section-title">⚙️ Wave Controls</h2>
                
                <div class="wave-type-selector">
                    <button type="button" class="wave-type-btn active" data-type="sine">Sine Wave</button>
                    <button type="button" class="wave-type-btn" data-type="square">Square Wave</button>
                    <button type="button" class="wave-type-btn" data-type="triangle">Triangle Wave</button>
                    <button type="button" class="wave-type-btn" data-type="sawtooth">Sawtooth Wave</button>
                </div>

                <div class="control-group">
                    <label for="amplitudeSlider" class="control-label">Amplitude:</label>
                    <input type="range" id="amplitudeSlider" class="control-slider" min="10" max="100" value="50">
                    <div class="control-value">
                        A = <span id="amplitudeValue">50</span> units
                    </div>
                </div>

                <div class="control-group">
                    <label for="frequencySlider" class="control-label">Frequency:</label>
                    <input type="range" id="frequencySlider" class="control-slider" min="1" max="10" value="2">
                    <div class="control-value">
                        f = <span id="frequencyValue">2</span> Hz
                    </div>
                </div>

                <div class="control-group">
                    <label for="wavelengthSlider" class="control-label">Wavelength:</label>
                    <input type="range" id="wavelengthSlider" class="control-slider" min="50" max="300" value="150">
                    <div class="control-value">
                        λ = <span id="wavelengthValue">150</span> units
                    </div>
                </div>
            </div>

            <div class="panel-section animate-slide-left animate-stagger-1">
                <h2 class="section-title">📊 Wave Properties</h2>
                <div class="wave-properties">
                    <div class="property-item">
                        <span class="property-label">Period (T):</span>
                        <span class="property-value" id="periodValue">0.50 s</span>
                    </div>
                    <div class="property-item">
                        <span class="property-label">Wave Speed (v):</span>
                        <span class="property-value" id="speedValue">300 units/s</span>
                    </div>
                    <div class="property-item">
                        <span class="property-label">Angular Frequency (ω):</span>
                        <span class="property-value" id="angularFreqValue">12.57 rad/s</span>
                    </div>
                    <div class="property-item">
                        <span class="property-label">Wave Number (k):</span>
                        <span class="property-value" id="waveNumberValue">0.042 rad/unit</span>
                    </div>
                </div>

                <div class="formula-section">
                    <div class="formula">v = fλ = ω/k</div>
                    <div class="formula-description">
                        Wave speed equals frequency times wavelength
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer id="main-footer-container"></footer>

    <script type="module" src="../js/main.js"></script>
    <script>
        // Wave Mechanics Laboratory JavaScript
        document.addEventListener('DOMContentLoaded', () => {
            // DOM Elements
            const canvas = document.getElementById('waveCanvas');
            const ctx = canvas.getContext('2d');
            const playPauseBtn = document.getElementById('playPauseBtn');
            const resetBtn = document.getElementById('resetBtn');
            const interferenceBtn = document.getElementById('interferenceBtn');
            
            // Sliders
            const amplitudeSlider = document.getElementById('amplitudeSlider');
            const frequencySlider = document.getElementById('frequencySlider');
            const wavelengthSlider = document.getElementById('wavelengthSlider');
            
            // Value displays
            const amplitudeValue = document.getElementById('amplitudeValue');
            const frequencyValue = document.getElementById('frequencyValue');
            const wavelengthValue = document.getElementById('wavelengthValue');
            const periodValue = document.getElementById('periodValue');
            const speedValue = document.getElementById('speedValue');
            const angularFreqValue = document.getElementById('angularFreqValue');
            const waveNumberValue = document.getElementById('waveNumberValue');

            // Wave type buttons
            const waveTypeButtons = document.querySelectorAll('.wave-type-btn');

            // Animation state
            let isPlaying = false;
            let animationId = null;
            let time = 0;
            let currentWaveType = 'sine';
            let showInterference = false;

            // Initialize canvas
            function initCanvas() {
                canvas.width = canvas.offsetWidth;
                canvas.height = canvas.offsetHeight;
            }

            // Get wave parameters
            function getWaveParams() {
                const amplitude = parseInt(amplitudeSlider.value);
                const frequency = parseInt(frequencySlider.value);
                const wavelength = parseInt(wavelengthSlider.value);
                
                return {
                    amplitude,
                    frequency,
                    wavelength,
                    period: 1 / frequency,
                    speed: frequency * wavelength,
                    angularFreq: 2 * Math.PI * frequency,
                    waveNumber: 2 * Math.PI / wavelength
                };
            }

            // Update displays
            function updateDisplays() {
                const params = getWaveParams();
                
                amplitudeValue.textContent = params.amplitude;
                frequencyValue.textContent = params.frequency;
                wavelengthValue.textContent = params.wavelength;
                periodValue.textContent = params.period.toFixed(2) + ' s';
                speedValue.textContent = params.speed.toFixed(0) + ' units/s';
                angularFreqValue.textContent = params.angularFreq.toFixed(2) + ' rad/s';
                waveNumberValue.textContent = params.waveNumber.toFixed(3) + ' rad/unit';
            }

            // Generate wave function
            function getWaveFunction(type) {
                switch(type) {
                    case 'sine':
                        return Math.sin;
                    case 'square':
                        return (x) => Math.sign(Math.sin(x));
                    case 'triangle':
                        return (x) => (2 / Math.PI) * Math.asin(Math.sin(x));
                    case 'sawtooth':
                        return (x) => (2 / Math.PI) * (x - Math.PI * Math.floor(x / Math.PI + 0.5));
                    default:
                        return Math.sin;
                }
            }

            // Draw wave
            function drawWave() {
                const params = getWaveParams();
                const waveFunc = getWaveFunction(currentWaveType);
                
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // Draw grid
                ctx.strokeStyle = '#e5e7eb';
                ctx.lineWidth = 1;
                
                // Horizontal grid lines
                for (let y = 0; y < canvas.height; y += 40) {
                    ctx.beginPath();
                    ctx.moveTo(0, y);
                    ctx.lineTo(canvas.width, y);
                    ctx.stroke();
                }
                
                // Vertical grid lines
                for (let x = 0; x < canvas.width; x += 40) {
                    ctx.beginPath();
                    ctx.moveTo(x, 0);
                    ctx.lineTo(x, canvas.height);
                    ctx.stroke();
                }
                
                // Draw center line
                ctx.strokeStyle = '#6b7280';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(0, canvas.height / 2);
                ctx.lineTo(canvas.width, canvas.height / 2);
                ctx.stroke();
                
                // Draw main wave
                ctx.strokeStyle = '#3b82f6';
                ctx.lineWidth = 3;
                ctx.beginPath();
                
                for (let x = 0; x < canvas.width; x++) {
                    const phase = params.waveNumber * x - params.angularFreq * time;
                    const y = canvas.height / 2 - params.amplitude * waveFunc(phase);
                    
                    if (x === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                }
                ctx.stroke();
                
                // Draw interference wave if enabled
                if (showInterference) {
                    ctx.strokeStyle = '#ef4444';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    
                    for (let x = 0; x < canvas.width; x++) {
                        const phase = params.waveNumber * x - params.angularFreq * time + Math.PI / 4;
                        const y = canvas.height / 2 - (params.amplitude * 0.7) * waveFunc(phase);
                        
                        if (x === 0) {
                            ctx.moveTo(x, y);
                        } else {
                            ctx.lineTo(x, y);
                        }
                    }
                    ctx.stroke();
                    
                    // Draw resultant wave
                    ctx.strokeStyle = '#10b981';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    
                    for (let x = 0; x < canvas.width; x++) {
                        const phase1 = params.waveNumber * x - params.angularFreq * time;
                        const phase2 = params.waveNumber * x - params.angularFreq * time + Math.PI / 4;
                        const y1 = params.amplitude * waveFunc(phase1);
                        const y2 = (params.amplitude * 0.7) * waveFunc(phase2);
                        const y = canvas.height / 2 - (y1 + y2);
                        
                        if (x === 0) {
                            ctx.moveTo(x, y);
                        } else {
                            ctx.lineTo(x, y);
                        }
                    }
                    ctx.stroke();
                }
            }

            // Animation loop
            function animate() {
                if (!isPlaying) return;
                
                time += 0.02;
                drawWave();
                animationId = requestAnimationFrame(animate);
            }

            // Event listeners
            playPauseBtn.addEventListener('click', () => {
                if (isPlaying) {
                    isPlaying = false;
                    cancelAnimationFrame(animationId);
                    playPauseBtn.textContent = 'Play';
                } else {
                    isPlaying = true;
                    playPauseBtn.textContent = 'Pause';
                    animate();
                }
            });

            resetBtn.addEventListener('click', () => {
                isPlaying = false;
                cancelAnimationFrame(animationId);
                time = 0;
                playPauseBtn.textContent = 'Play';
                drawWave();
            });

            interferenceBtn.addEventListener('click', () => {
                showInterference = !showInterference;
                interferenceBtn.textContent = showInterference ? 'Remove Interference' : 'Add Interference';
                drawWave();
            });

            // Slider event listeners
            [amplitudeSlider, frequencySlider, wavelengthSlider].forEach(slider => {
                slider.addEventListener('input', () => {
                    updateDisplays();
                    drawWave();
                });
            });

            // Wave type selection
            waveTypeButtons.forEach(btn => {
                btn.addEventListener('click', () => {
                    waveTypeButtons.forEach(b => b.classList.remove('active'));
                    btn.classList.add('active');
                    currentWaveType = btn.dataset.type;
                    drawWave();
                });
            });

            // Initialize
            initCanvas();
            updateDisplays();
            drawWave();

            // Handle window resize
            window.addEventListener('resize', () => {
                initCanvas();
                drawWave();
            });
        });
    </script>
</body>
</html>
