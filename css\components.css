/* Physics Virtual Lab - Component Styles */

/* Hero Section */
.hero {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 50%, var(--secondary-600) 100%);
  color: white;
  padding: 6rem 0 4rem;
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
}

.hero-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  margin-bottom: 1.5rem;
  line-height: 1.1;
}

.hero-subtitle {
  font-size: clamp(1.125rem, 2vw, 1.5rem);
  margin-bottom: 2rem;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

@media (min-width: 640px) {
  .hero-actions {
    flex-direction: row;
    justify-content: center;
  }
}

/* Quick Access Cards */
.quick-access {
  padding: 3rem 0;
  background-color: var(--bg-secondary);
}

.quick-access-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.quick-access-card {
  background: white;
  border-radius: var(--radius-xl);
  padding: 2rem;
  text-align: center;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  text-decoration: none;
  color: inherit;
}

[data-theme="dark"] .quick-access-card {
  background: var(--gray-700);
}

.quick-access-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.quick-access-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
  transition: transform var(--transition-normal);
}

.quick-access-card:hover .quick-access-icon {
  transform: scale(1.1);
}

.quick-access-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

/* Experiment Cards */
.experiment-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.experiment-card {
  background: white;
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
}

[data-theme="dark"] .experiment-card {
  background: var(--gray-700);
}

.experiment-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.experiment-card-header {
  height: 200px;
  background: linear-gradient(135deg, var(--primary-400), var(--primary-600));
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.experiment-card-header.circuit {
  background: linear-gradient(135deg, var(--secondary-400), var(--secondary-600));
}

.experiment-card-header.mechanics {
  background: linear-gradient(135deg, var(--accent-400), var(--accent-600));
}

.experiment-card-header.biomedical {
  background: linear-gradient(135deg, #10b981, #059669);
}

.experiment-card-icon {
  font-size: 4rem;
  color: white;
  z-index: 2;
}

.experiment-card-body {
  padding: 1.5rem;
}

.experiment-card-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: var(--text-primary);
}

.experiment-card-description {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.experiment-card-link {
  color: var(--primary-600);
  font-weight: 500;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: color var(--transition-fast);
}

.experiment-card-link:hover {
  color: var(--primary-700);
}

/* Feature Section */
.features {
  padding: 5rem 0;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 3rem;
  margin-top: 3rem;
}

.feature-item {
  text-align: center;
}

.feature-icon {
  width: 4rem;
  height: 4rem;
  background: var(--primary-100);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  font-size: 2rem;
  transition: all var(--transition-normal);
}

[data-theme="dark"] .feature-icon {
  background: var(--primary-900);
}

.feature-item:hover .feature-icon {
  transform: scale(1.1);
  background: var(--primary-600);
  color: white;
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.feature-description {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Technology Stack */
.tech-stack {
  padding: 4rem 0;
  background: var(--bg-secondary);
}

.tech-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.tech-item {
  text-align: center;
  transition: transform var(--transition-normal);
}

.tech-item:hover {
  transform: translateY(-4px);
}

.tech-icon {
  width: 4rem;
  height: 4rem;
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  font-size: 2rem;
  transition: all var(--transition-normal);
}

.tech-item:hover .tech-icon {
  transform: scale(1.1);
}

.tech-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
}

/* Call to Action */
.cta {
  background: linear-gradient(135deg, var(--primary-600), var(--secondary-600));
  color: white;
  padding: 5rem 0;
  text-align: center;
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.cta-description {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

@media (min-width: 640px) {
  .cta-actions {
    flex-direction: row;
    justify-content: center;
  }
}

/* Dashboard Components */
.dashboard-header {
  background: white;
  border-radius: var(--radius-xl);
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-md);
}

[data-theme="dark"] .dashboard-header {
  background: var(--gray-700);
}

.dashboard-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.dashboard-subtitle {
  color: var(--text-secondary);
  font-size: 1.125rem;
}

.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow-md);
  text-align: center;
}

[data-theme="dark"] .stat-card {
  background: var(--gray-700);
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-600);
  margin-bottom: 0.5rem;
}

.stat-label {
  color: var(--text-secondary);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Progress Components */
.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--gray-200);
  border-radius: 4px;
  overflow: hidden;
}

[data-theme="dark"] .progress-bar {
  background: var(--gray-600);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
  border-radius: 4px;
  transition: width var(--transition-normal);
}

.progress-text {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-top: 0.5rem;
}

/* Alert Components */
.alert {
  padding: 1rem 1.5rem;
  border-radius: var(--radius-lg);
  margin-bottom: 1rem;
  border-left: 4px solid;
}

.alert-success {
  background: #f0fdf4;
  border-color: #22c55e;
  color: #166534;
}

.alert-warning {
  background: #fffbeb;
  border-color: #f59e0b;
  color: #92400e;
}

.alert-error {
  background: #fef2f2;
  border-color: #ef4444;
  color: #991b1b;
}

.alert-info {
  background: #eff6ff;
  border-color: var(--primary-500);
  color: var(--primary-800);
}

[data-theme="dark"] .alert-success {
  background: #064e3b;
  color: #6ee7b7;
}

[data-theme="dark"] .alert-warning {
  background: #78350f;
  color: #fbbf24;
}

[data-theme="dark"] .alert-error {
  background: #7f1d1d;
  color: #fca5a5;
}

[data-theme="dark"] .alert-info {
  background: var(--primary-900);
  color: var(--primary-300);
}

/* Modal Components */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: var(--radius-xl);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-xl);
}

[data-theme="dark"] .modal-content {
  background: var(--gray-800);
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.modal-close:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  padding: 1.5rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero {
    padding: 4rem 0 3rem;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
  
  .experiment-grid {
    grid-template-columns: 1fr;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .tech-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .cta-title {
    font-size: 2rem;
  }
}
