<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Science Simulations - Physics Virtual Lab</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/animations.css">
    <link rel="stylesheet" href="../css/components.css">
    <style>
        body {
            font-family: var(--font-family-sans);
            margin: 0;
            padding: 0;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .experiment-header {
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--border-color);
            padding: var(--spacing-md);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .experiment-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }
        
        .back-button {
            background: var(--primary-600);
            color: white;
            border: none;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-md);
            text-decoration: none;
            font-weight: 500;
            transition: all var(--transition-fast);
        }
        
        .back-button:hover {
            background: var(--primary-700);
            transform: translateY(-2px);
        }

        .main-container {
            flex: 1;
            max-width: 1400px;
            margin: 0 auto;
            padding: var(--spacing-lg);
            width: 100%;
        }

        .intro-section {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            padding: var(--spacing-xl);
            margin-bottom: var(--spacing-lg);
            text-align: center;
        }

        [data-theme="dark"] .intro-section {
            background: var(--gray-700);
        }

        .intro-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
        }

        .intro-text {
            font-size: 1.125rem;
            color: var(--text-secondary);
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
        }

        .concept-selector {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }

        [data-theme="dark"] .concept-selector {
            background: var(--gray-700);
        }

        .concept-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
            margin-top: var(--spacing-md);
        }

        .concept-button {
            background: var(--primary-100);
            border: 2px solid var(--primary-200);
            border-radius: var(--radius-lg);
            padding: var(--spacing-md);
            cursor: pointer;
            transition: all var(--transition-fast);
            text-align: center;
            font-weight: 500;
            color: var(--primary-700);
        }

        [data-theme="dark"] .concept-button {
            background: var(--primary-900);
            border-color: var(--primary-700);
            color: var(--primary-300);
        }

        .concept-button:hover {
            background: var(--primary-200);
            border-color: var(--primary-400);
            transform: translateY(-2px);
        }

        [data-theme="dark"] .concept-button:hover {
            background: var(--primary-800);
            border-color: var(--primary-500);
        }

        .concept-button.active {
            background: var(--primary-600);
            border-color: var(--primary-600);
            color: white;
        }

        .concept-icon {
            font-size: 2rem;
            margin-bottom: var(--spacing-sm);
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-lg);
        }

        .content-panel {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            padding: var(--spacing-lg);
            min-height: 500px;
        }

        [data-theme="dark"] .content-panel {
            background: var(--gray-700);
        }

        .panel-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .notes-textarea {
            width: 100%;
            min-height: 300px;
            padding: var(--spacing-md);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            font-family: var(--font-family-sans);
            font-size: 1rem;
            line-height: 1.6;
            background: var(--bg-primary);
            color: var(--text-primary);
            resize: vertical;
        }

        .notes-textarea:focus {
            outline: none;
            border-color: var(--primary-500);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .simulation-area {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-md);
        }

        .simulation-placeholder {
            width: 100%;
            min-height: 300px;
            background: var(--bg-secondary);
            border: 2px dashed var(--border-color);
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            font-style: italic;
            text-align: center;
            padding: var(--spacing-lg);
        }

        .simulation-controls {
            width: 100%;
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .control-label {
            min-width: 120px;
            font-weight: 500;
            color: var(--text-primary);
        }

        .control-slider {
            flex: 1;
            height: 8px;
            border-radius: 4px;
            background: var(--gray-200);
            outline: none;
            cursor: pointer;
            accent-color: var(--primary-600);
        }

        [data-theme="dark"] .control-slider {
            background: var(--gray-600);
        }

        .control-value {
            min-width: 60px;
            text-align: right;
            font-weight: 600;
            color: var(--primary-600);
        }

        .simulation-canvas {
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            background: white;
            display: none;
        }

        [data-theme="dark"] .simulation-canvas {
            background: var(--gray-800);
        }

        .concept-description {
            background: var(--accent-50);
            border: 1px solid var(--accent-200);
            border-radius: var(--radius-lg);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-md);
            color: var(--text-secondary);
        }

        [data-theme="dark"] .concept-description {
            background: var(--accent-900);
            border-color: var(--accent-700);
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .concept-buttons {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .control-group {
                flex-direction: column;
                align-items: stretch;
            }
            
            .control-label {
                min-width: auto;
            }
            
            .control-value {
                text-align: left;
            }
        }

        @media (max-width: 480px) {
            .concept-buttons {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="experiment-header">
        <h1 class="experiment-title">🔬 Interactive Science Simulations</h1>
        <a href="../dashboard.html" class="back-button">← Back to Dashboard</a>
    </div>

    <div class="main-container">
        <div class="intro-section animate-fade-in">
            <h1 class="intro-title">Understanding Science with Simulations</h1>
            <p class="intro-text">
                Use this tool to explore scientific concepts through interactive simulations. First, jot down your thoughts, 
                questions, or observations about a concept in the "Real World" section. Then, interact with the simulation 
                to see the concept in action. Compare your initial thoughts with the simulated behavior.
            </p>
        </div>

        <div class="concept-selector animate-slide-up">
            <h2 class="panel-title">📚 Select a Physics Concept</h2>
            <div class="concept-buttons">
                <button class="concept-button" data-concept="pendulum">
                    <div class="concept-icon">⚖️</div>
                    <div>Pendulum Motion</div>
                </button>
                <button class="concept-button" data-concept="projectile">
                    <div class="concept-icon">🚀</div>
                    <div>Projectile Motion</div>
                </button>
                <button class="concept-button" data-concept="shm">
                    <div class="concept-icon">🌊</div>
                    <div>Simple Harmonic Motion</div>
                </button>
                <button class="concept-button" data-concept="ohms">
                    <div class="concept-icon">⚡</div>
                    <div>Ohm's Law</div>
                </button>
                <button class="concept-button" data-concept="wave">
                    <div class="concept-icon">〰️</div>
                    <div>Wave Interference</div>
                </button>
                <button class="concept-button" data-concept="rolling">
                    <div class="concept-icon">⚽</div>
                    <div>Rolling Motion</div>
                </button>
            </div>
        </div>

        <div class="main-content">
            <div class="content-panel animate-slide-left">
                <h2 class="panel-title">🌍 Real World Observations</h2>
                <div class="concept-description">
                    <p>Your observations, questions, and thoughts about <strong id="current-concept-name">the selected concept</strong>:</p>
                </div>
                <textarea 
                    id="notesTextarea" 
                    class="notes-textarea" 
                    placeholder="Type your observations, questions, or predictions here...

For example:
- What do you think will happen?
- Have you seen this in real life?
- What factors might affect the outcome?
- What questions do you have?"
                ></textarea>
            </div>

            <div class="content-panel animate-slide-right">
                <h2 class="panel-title">🔬 Simulation: <span id="current-simulation-name">Select a concept</span></h2>
                <div class="simulation-area">
                    <div id="simulationPlaceholder" class="simulation-placeholder">
                        <div>
                            <div style="font-size: 3rem; margin-bottom: 1rem;">🔬</div>
                            <div>Select a concept above to load the interactive simulation</div>
                        </div>
                    </div>
                    <div id="simulationControls" class="simulation-controls"></div>
                    <canvas id="simulationCanvas" class="simulation-canvas" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script type="module" src="../js/main.js"></script>
    <script>
        // Science Simulations Controller
        class ScienceSimulations {
            constructor() {
                this.currentSimulation = null;
                this.animationId = null;
                this.canvas = document.getElementById('simulationCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.setupEventListeners();
            }

            setupEventListeners() {
                const conceptButtons = document.querySelectorAll('.concept-button');
                conceptButtons.forEach(button => {
                    button.addEventListener('click', () => {
                        const concept = button.dataset.concept;
                        const conceptName = button.textContent.trim();
                        this.selectConcept(concept, conceptName, button);
                    });
                });
            }

            selectConcept(concept, conceptName, button) {
                // Update UI
                document.querySelectorAll('.concept-button').forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
                
                document.getElementById('current-concept-name').textContent = conceptName.toLowerCase();
                document.getElementById('current-simulation-name').textContent = conceptName;
                
                // Clear previous simulation
                this.clearSimulation();
                
                // Load new simulation
                this.loadSimulation(concept);
            }

            clearSimulation() {
                if (this.animationId) {
                    cancelAnimationFrame(this.animationId);
                    this.animationId = null;
                }
                
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                document.getElementById('simulationControls').innerHTML = '';
                document.getElementById('simulationPlaceholder').style.display = 'flex';
                this.canvas.style.display = 'none';
            }

            loadSimulation(concept) {
                document.getElementById('simulationPlaceholder').style.display = 'none';
                this.canvas.style.display = 'block';
                
                switch (concept) {
                    case 'pendulum':
                        this.setupPendulumSimulation();
                        break;
                    case 'projectile':
                        this.setupProjectileSimulation();
                        break;
                    case 'shm':
                        this.setupSHMSimulation();
                        break;
                    case 'ohms':
                        this.setupOhmsLawSimulation();
                        break;
                    case 'wave':
                        this.setupWaveSimulation();
                        break;
                    case 'rolling':
                        this.setupRollingSimulation();
                        break;
                }
            }

            createControl(label, id, min, max, value, unit = '') {
                const controlsContainer = document.getElementById('simulationControls');
                const controlGroup = document.createElement('div');
                controlGroup.className = 'control-group';
                
                controlGroup.innerHTML = `
                    <label class="control-label">${label}:</label>
                    <input type="range" class="control-slider" id="${id}" min="${min}" max="${max}" value="${value}">
                    <span class="control-value" id="${id}Value">${value}${unit}</span>
                `;
                
                controlsContainer.appendChild(controlGroup);
                
                const slider = document.getElementById(id);
                const valueDisplay = document.getElementById(id + 'Value');
                
                slider.addEventListener('input', () => {
                    valueDisplay.textContent = slider.value + unit;
                });
                
                return slider;
            }

            setupPendulumSimulation() {
                const lengthSlider = this.createControl('Length', 'pendulumLength', 0.5, 2, 1, ' m');
                const angleSlider = this.createControl('Initial Angle', 'pendulumAngle', -45, 45, 15, '°');
                
                let angle = 15 * Math.PI / 180;
                let angularVelocity = 0;
                const g = 9.81;
                
                const animate = () => {
                    const length = parseFloat(lengthSlider.value);
                    const initialAngle = parseFloat(angleSlider.value) * Math.PI / 180;
                    
                    // Simple pendulum physics
                    const angularAcceleration = -(g / length) * Math.sin(angle);
                    angularVelocity += angularAcceleration * 0.016;
                    angularVelocity *= 0.999; // Damping
                    angle += angularVelocity * 0.016;
                    
                    // Draw pendulum
                    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                    
                    const pivotX = this.canvas.width / 2;
                    const pivotY = 50;
                    const pendulumLength = length * 100;
                    const bobX = pivotX + pendulumLength * Math.sin(angle);
                    const bobY = pivotY + pendulumLength * Math.cos(angle);
                    
                    // Draw string
                    this.ctx.beginPath();
                    this.ctx.moveTo(pivotX, pivotY);
                    this.ctx.lineTo(bobX, bobY);
                    this.ctx.strokeStyle = '#333';
                    this.ctx.lineWidth = 2;
                    this.ctx.stroke();
                    
                    // Draw pivot
                    this.ctx.beginPath();
                    this.ctx.arc(pivotX, pivotY, 5, 0, 2 * Math.PI);
                    this.ctx.fillStyle = '#666';
                    this.ctx.fill();
                    
                    // Draw bob
                    this.ctx.beginPath();
                    this.ctx.arc(bobX, bobY, 12, 0, 2 * Math.PI);
                    this.ctx.fillStyle = '#e74c3c';
                    this.ctx.fill();
                    
                    this.animationId = requestAnimationFrame(animate);
                };
                
                // Reset when sliders change
                lengthSlider.addEventListener('input', () => {
                    angle = parseFloat(angleSlider.value) * Math.PI / 180;
                    angularVelocity = 0;
                });
                
                angleSlider.addEventListener('input', () => {
                    angle = parseFloat(angleSlider.value) * Math.PI / 180;
                    angularVelocity = 0;
                });
                
                animate();
            }

            setupProjectileSimulation() {
                const velocitySlider = this.createControl('Initial Velocity', 'projVelocity', 5, 50, 25, ' m/s');
                const angleSlider = this.createControl('Launch Angle', 'projAngle', 0, 90, 45, '°');
                
                let projectiles = [];
                const g = 9.81;
                const scale = 5;
                
                const animate = () => {
                    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                    
                    // Draw ground
                    this.ctx.fillStyle = '#2ecc71';
                    this.ctx.fillRect(0, this.canvas.height - 20, this.canvas.width, 20);
                    
                    // Update and draw projectiles
                    projectiles = projectiles.filter(proj => {
                        proj.t += 0.02;
                        proj.x = proj.v0x * proj.t * scale;
                        proj.y = this.canvas.height - 20 - (proj.v0y * proj.t - 0.5 * g * proj.t * proj.t) * scale;
                        
                        if (proj.y < this.canvas.height - 20 && proj.x < this.canvas.width) {
                            this.ctx.beginPath();
                            this.ctx.arc(proj.x + 20, proj.y, 6, 0, 2 * Math.PI);
                            this.ctx.fillStyle = '#e74c3c';
                            this.ctx.fill();
                            return true;
                        }
                        return false;
                    });
                    
                    this.animationId = requestAnimationFrame(animate);
                };
                
                // Launch projectile on double click
                this.canvas.addEventListener('dblclick', () => {
                    const v0 = parseFloat(velocitySlider.value);
                    const angle = parseFloat(angleSlider.value) * Math.PI / 180;
                    
                    projectiles.push({
                        v0x: v0 * Math.cos(angle),
                        v0y: v0 * Math.sin(angle),
                        t: 0,
                        x: 0,
                        y: this.canvas.height - 20
                    });
                });
                
                animate();
            }

            setupSHMSimulation() {
                const amplitudeSlider = this.createControl('Amplitude', 'shmAmplitude', 10, 100, 50, ' px');
                const frequencySlider = this.createControl('Frequency', 'shmFrequency', 0.1, 2, 0.5, ' Hz');
                
                let time = 0;
                
                const animate = () => {
                    const amplitude = parseFloat(amplitudeSlider.value);
                    const frequency = parseFloat(frequencySlider.value);
                    
                    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                    
                    // Draw equilibrium line
                    this.ctx.beginPath();
                    this.ctx.moveTo(0, this.canvas.height / 2);
                    this.ctx.lineTo(this.canvas.width, this.canvas.height / 2);
                    this.ctx.strokeStyle = '#bdc3c7';
                    this.ctx.setLineDash([5, 5]);
                    this.ctx.stroke();
                    this.ctx.setLineDash([]);
                    
                    // Calculate position
                    const y = this.canvas.height / 2 + amplitude * Math.sin(2 * Math.PI * frequency * time);
                    
                    // Draw oscillating object
                    this.ctx.beginPath();
                    this.ctx.arc(this.canvas.width / 2, y, 15, 0, 2 * Math.PI);
                    this.ctx.fillStyle = '#3498db';
                    this.ctx.fill();
                    
                    time += 0.02;
                    this.animationId = requestAnimationFrame(animate);
                };
                
                animate();
            }

            setupOhmsLawSimulation() {
                const voltageSlider = this.createControl('Voltage', 'ohmsVoltage', 1, 12, 6, ' V');
                const resistanceSlider = this.createControl('Resistance', 'ohmsResistance', 1, 100, 10, ' Ω');
                
                const updateDisplay = () => {
                    const voltage = parseFloat(voltageSlider.value);
                    const resistance = parseFloat(resistanceSlider.value);
                    const current = voltage / resistance;
                    const power = voltage * current;
                    
                    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                    
                    // Draw circuit diagram
                    this.ctx.strokeStyle = '#333';
                    this.ctx.lineWidth = 3;
                    
                    // Battery
                    this.ctx.fillStyle = '#f39c12';
                    this.ctx.fillRect(50, 100, 30, 60);
                    this.ctx.strokeRect(50, 100, 30, 60);
                    
                    // Resistor
                    this.ctx.beginPath();
                    this.ctx.moveTo(150, 130);
                    for (let i = 0; i < 6; i++) {
                        this.ctx.lineTo(160 + i * 20, 130 + (i % 2 ? -10 : 10));
                    }
                    this.ctx.stroke();
                    
                    // Wires
                    this.ctx.beginPath();
                    this.ctx.moveTo(80, 110, 150, 110);
                    this.ctx.lineTo(150, 110);
                    this.ctx.lineTo(150, 130);
                    this.ctx.moveTo(270, 130);
                    this.ctx.lineTo(320, 130);
                    this.ctx.lineTo(320, 200);
                    this.ctx.lineTo(50, 200);
                    this.ctx.lineTo(50, 160);
                    this.ctx.stroke();
                    
                    // Display values
                    this.ctx.fillStyle = '#2c3e50';
                    this.ctx.font = '16px Arial';
                    this.ctx.fillText(`V = ${voltage.toFixed(1)} V`, 20, 250);
                    this.ctx.fillText(`R = ${resistance.toFixed(1)} Ω`, 120, 250);
                    this.ctx.fillText(`I = ${current.toFixed(2)} A`, 220, 250);
                    this.ctx.fillText(`P = ${power.toFixed(2)} W`, 320, 250);
                };
                
                voltageSlider.addEventListener('input', updateDisplay);
                resistanceSlider.addEventListener('input', updateDisplay);
                updateDisplay();
            }

            setupWaveSimulation() {
                const amplitudeSlider = this.createControl('Amplitude', 'waveAmplitude', 10, 50, 30, ' px');
                const frequencySlider = this.createControl('Frequency', 'waveFrequency', 0.1, 2, 0.5, ' Hz');
                
                let time = 0;
                
                const animate = () => {
                    const amplitude = parseFloat(amplitudeSlider.value);
                    const frequency = parseFloat(frequencySlider.value);
                    
                    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                    
                    // Draw wave
                    this.ctx.beginPath();
                    this.ctx.strokeStyle = '#3498db';
                    this.ctx.lineWidth = 3;
                    
                    for (let x = 0; x < this.canvas.width; x++) {
                        const y = this.canvas.height / 2 + amplitude * Math.sin((x * 0.02) + (2 * Math.PI * frequency * time));
                        if (x === 0) {
                            this.ctx.moveTo(x, y);
                        } else {
                            this.ctx.lineTo(x, y);
                        }
                    }
                    this.ctx.stroke();
                    
                    time += 0.02;
                    this.animationId = requestAnimationFrame(animate);
                };
                
                animate();
            }

            setupRollingSimulation() {
                const radiusSlider = this.createControl('Radius', 'rollRadius', 10, 50, 25, ' px');
                const speedSlider = this.createControl('Speed', 'rollSpeed', 0.5, 5, 2, ' m/s');
                
                let position = 0;
                let angle = 0;
                
                const animate = () => {
                    const radius = parseFloat(radiusSlider.value);
                    const speed = parseFloat(speedSlider.value);
                    
                    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                    
                    // Draw ground
                    this.ctx.fillStyle = '#2ecc71';
                    this.ctx.fillRect(0, this.canvas.height - 20, this.canvas.width, 20);
                    
                    // Update position and angle
                    position += speed;
                    angle += speed / radius;
                    
                    if (position > this.canvas.width + radius) {
                        position = -radius;
                    }
                    
                    // Draw rolling circle
                    const centerY = this.canvas.height - 20 - radius;
                    
                    this.ctx.beginPath();
                    this.ctx.arc(position, centerY, radius, 0, 2 * Math.PI);
                    this.ctx.strokeStyle = '#e74c3c';
                    this.ctx.lineWidth = 3;
                    this.ctx.stroke();
                    
                    // Draw radius line to show rotation
                    this.ctx.beginPath();
                    this.ctx.moveTo(position, centerY);
                    this.ctx.lineTo(
                        position + radius * Math.cos(angle),
                        centerY + radius * Math.sin(angle)
                    );
                    this.ctx.stroke();
                    
                    this.animationId = requestAnimationFrame(animate);
                };
                
                animate();
            }
        }

        // Initialize the application
        document.addEventListener('DOMContentLoaded', () => {
            new ScienceSimulations();
        });
    </script>
</body>
</html>
