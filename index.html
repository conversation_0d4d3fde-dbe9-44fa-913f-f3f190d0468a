<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Virtual Physics & Biomedical Physics LMS - Advanced Learning Platform</title>
    <meta name="description" content="Explore physics and biomedical physics through interactive simulations, virtual labs, and AI-powered learning experiences.">
    <meta name="author" content="Dr. <PERSON>, SUST-BME">
    <meta name="copyright" content="Dr. <PERSON> - +249912867327, +966538076790">
    <meta name="institution" content="Sudan University of Science and Technology - Biomedical Engineering">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/components.css">
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: {"50":"#eff6ff","100":"#dbeafe","200":"#bfdbfe","300":"#93c5fd","400":"#60a5fa","500":"#3b82f6","600":"#2563eb","700":"#1d4ed8","800":"#1e40af","900":"#1e3a8a","950":"#172554"},
              secondary: {"50":"#f0f9ff","100":"#e0f2fe","200":"#bae6fd","300":"#7dd3fc","400":"#38bdf8","500":"#0ea5e9","600":"#0284c7","700":"#0369a1","800":"#075985","900":"#0c4a6e","950":"#082f49"},
              accent: {"50":"#f5f3ff","100":"#ede9fe","200":"#ddd6fe","300":"#c4b5fd","400":"#a78bfa","500":"#8b5cf6","600":"#7c3aed","700":"#6d28d9","800":"#5b21b6","900":"#4c1d95","950":"#2e1065"}
            }
          }
        }
      }
    </script>
    <script type="importmap">
{
  "imports": {
    "@google/genai": "https://esm.sh/@google/genai@^1.3.0",
    "react": "https://esm.sh/react@^19.1.0",
    "react-router-dom": "https://esm.sh/react-router-dom@^7.6.1",
    "react/": "https://esm.sh/react@^19.1.0/"
  }
}
</script>
  <link rel="stylesheet" href="/index.css">
</head>
  <body class="bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-gray-100 flex flex-col min-h-screen">
    <header id="main-header" class="fixed w-full z-50 top-0">
      <!-- Navbar will be injected here by js/main.js -->
    </header>

    <main class="flex-grow pt-16">
      <!-- Hero Section -->
      <section class="relative bg-gradient-to-br from-primary-600 via-primary-700 to-secondary-600 text-white py-24 overflow-hidden">
        <div class="absolute inset-0 bg-black opacity-10"></div>
        <div class="absolute inset-0">
          <div class="absolute top-10 left-10 w-20 h-20 bg-white opacity-10 rounded-full animate-pulse-slow"></div>
          <div class="absolute top-32 right-20 w-16 h-16 bg-accent-300 opacity-20 rounded-full animate-pulse-slow" style="animation-delay: 1s;"></div>
          <div class="absolute bottom-20 left-1/4 w-12 h-12 bg-secondary-300 opacity-15 rounded-full animate-pulse-slow" style="animation-delay: 2s;"></div>
        </div>
        <div class="container mx-auto px-6 text-center relative z-10">
          <h1 class="text-5xl md:text-7xl font-bold mb-6 animate-fade-in">
            Virtual Physics &
            <span class="text-transparent bg-clip-text bg-gradient-to-r from-accent-300 to-secondary-300">
              Biomedical Physics
            </span>
            <br>Learning Platform
          </h1>
          <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto animate-slide-up opacity-90">
            Experience cutting-edge physics education through interactive simulations, AI-powered learning, and virtual laboratory experiments.
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center items-center animate-slide-up" style="animation-delay: 0.3s;">
            <a href="dashboard.html" class="bg-white text-primary-600 font-bold py-4 px-8 rounded-xl hover:bg-primary-50 transition duration-300 text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1">
              🚀 Start Learning
            </a>
            <a href="#features" class="border-2 border-white text-white font-bold py-4 px-8 rounded-xl hover:bg-white hover:text-primary-600 transition duration-300 text-lg">
              🔬 Explore Features
            </a>
          </div>
        </div>
      </section>

      <!-- Quick Access Section -->
      <section class="py-12 bg-gray-50 dark:bg-gray-800">
        <div class="container mx-auto px-6">
          <h2 class="text-3xl font-bold text-center text-gray-800 dark:text-gray-200 mb-8">Quick Access</h2>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <a href="dashboard.html" class="group bg-white dark:bg-gray-700 p-6 rounded-xl shadow-md hover:shadow-lg transition duration-300 text-center">
              <div class="text-3xl mb-2 group-hover:scale-110 transition-transform duration-300">📊</div>
              <h3 class="font-semibold text-gray-800 dark:text-gray-200">Dashboard</h3>
            </a>
            <a href="experiments-hub.html" class="group bg-white dark:bg-gray-700 p-6 rounded-xl shadow-md hover:shadow-lg transition duration-300 text-center">
              <div class="text-3xl mb-2 group-hover:scale-110 transition-transform duration-300">🧪</div>
              <h3 class="font-semibold text-gray-800 dark:text-gray-200">Experiments Hub</h3>
            </a>
            <a href="video-library.html" class="group bg-white dark:bg-gray-700 p-6 rounded-xl shadow-md hover:shadow-lg transition duration-300 text-center">
              <div class="text-3xl mb-2 group-hover:scale-110 transition-transform duration-300">�</div>
              <h3 class="font-semibold text-gray-800 dark:text-gray-200">Video Library</h3>
            </a>
            <a href="assessment.html" class="group bg-white dark:bg-gray-700 p-6 rounded-xl shadow-md hover:shadow-lg transition duration-300 text-center">
              <div class="text-3xl mb-2 group-hover:scale-110 transition-transform duration-300">🎓</div>
              <h3 class="font-semibold text-gray-800 dark:text-gray-200">Assessment</h3>
            </a>
          </div>
        </div>
      </section>

      <!-- Features Section -->
      <section id="features" class="py-20">
        <div class="container mx-auto px-6">
          <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-gray-800 dark:text-gray-200 mb-4">Platform Features</h2>
            <p class="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Discover advanced learning tools designed for modern physics education
            </p>
          </div>
          <div class="grid md:grid-cols-3 gap-8">
            <div class="group bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg hover:shadow-2xl transition duration-300 transform hover:-translate-y-2">
              <div class="flex items-center justify-center h-16 w-16 bg-gradient-to-br from-primary-500 to-primary-600 text-white rounded-xl mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-8 h-8">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25" />
                </svg>
              </div>
              <h3 class="text-2xl font-semibold text-center mb-4 text-gray-800 dark:text-gray-200">Interactive Courses</h3>
              <p class="text-gray-600 dark:text-gray-400 text-center mb-4">
                Engage with dynamic modules covering Classical Mechanics, Electromagnetism, and Biomedical Physics with real-time simulations.
              </p>
              <div class="text-center">
                <span class="inline-block bg-primary-100 dark:bg-primary-900 text-primary-600 dark:text-primary-300 px-3 py-1 rounded-full text-sm font-medium">
                  Physics 101 • Biomedical 201
                </span>
              </div>
            </div>

            <div class="group bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg hover:shadow-2xl transition duration-300 transform hover:-translate-y-2">
              <div class="flex items-center justify-center h-16 w-16 bg-gradient-to-br from-secondary-500 to-secondary-600 text-white rounded-xl mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-8 h-8">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9.75 3.104v5.714a2.25 2.25 0 01-.659 1.591L5 14.5M9.75 3.104c-.251.023-.501.05-.75.082m.75-.082a24.301 24.301 0 014.5 0m0 0v5.714c0 .597.237 1.17.659 1.591L19.8 15.3M14.25 3.104c.251.023.501.05.75.082M19.8 15.3l-1.57.393A9.065 9.065 0 0112 15a9.065 9.065 0 00-6.23-.693L5 14.5m14.8.8l1.402 1.402c1.232 1.232.65 3.318-1.067 3.611A48.309 48.309 0 0112 21c-2.773 0-5.491-.235-8.135-.687-1.718-.293-2.3-2.379-1.067-3.611L5 14.5" />
                </svg>
              </div>
              <h3 class="text-2xl font-semibold text-center mb-4 text-gray-800 dark:text-gray-200">Virtual Laboratory</h3>
              <p class="text-gray-600 dark:text-gray-400 text-center mb-4">
                Conduct sophisticated experiments including circuit analysis, electromagnetic field visualization, and biomedical signal processing.
              </p>
              <div class="text-center">
                <span class="inline-block bg-secondary-100 dark:bg-secondary-900 text-secondary-600 dark:text-secondary-300 px-3 py-1 rounded-full text-sm font-medium">
                  Circuit Simulator • Field Analysis
                </span>
              </div>
            </div>

            <div class="group bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg hover:shadow-2xl transition duration-300 transform hover:-translate-y-2">
              <div class="flex items-center justify-center h-16 w-16 bg-gradient-to-br from-accent-500 to-accent-600 text-white rounded-xl mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-8 h-8">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z" />
                </svg>
              </div>
              <h3 class="text-2xl font-semibold text-center mb-4 text-gray-800 dark:text-gray-200">AI-Powered Learning</h3>
              <p class="text-gray-600 dark:text-gray-400 text-center mb-4">
                Experience personalized learning with AI assistance, adaptive quizzes, and intelligent progress tracking powered by Gemini AI.
              </p>
              <div class="text-center">
                <span class="inline-block bg-accent-100 dark:bg-accent-900 text-accent-600 dark:text-accent-300 px-3 py-1 rounded-full text-sm font-medium">
                  Gemini AI • Smart Analytics
                </span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Available Simulations Section -->
      <section id="experiments" class="py-20 bg-gray-50 dark:bg-gray-800">
        <div class="container mx-auto px-6">
          <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-gray-800 dark:text-gray-200 mb-4">Interactive Simulations</h2>
            <p class="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Explore physics concepts through hands-on virtual experiments
            </p>
          </div>
          <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Electromagnetism Experiments -->
            <div class="bg-white dark:bg-gray-700 rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition duration-300 transform hover:-translate-y-1">
              <div class="h-48 bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center">
                <div class="text-6xl text-white">⚡</div>
              </div>
              <div class="p-6">
                <h3 class="text-xl font-semibold mb-2 text-gray-800 dark:text-gray-200">Circuit Simulator</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-4">
                  Build and analyze electrical circuits with interactive components, voltage sources, and real-time measurements.
                </p>
                <a href="experiments/circuit-simulator.html" class="inline-flex items-center text-primary-600 dark:text-primary-400 font-medium hover:text-primary-700 dark:hover:text-primary-300">
                  Launch Simulation →
                </a>
              </div>
            </div>

            <div class="bg-white dark:bg-gray-700 rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition duration-300 transform hover:-translate-y-1">
              <div class="h-48 bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center">
                <div class="text-6xl text-white">🔌</div>
              </div>
              <div class="p-6">
                <h3 class="text-xl font-semibold mb-2 text-gray-800 dark:text-gray-200">Ohm's Law Explorer</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-4">
                  Investigate the relationship between voltage, current, and resistance with interactive controls and visualizations.
                </p>
                <a href="experiments/ohms-law.html" class="inline-flex items-center text-primary-600 dark:text-primary-400 font-medium hover:text-primary-700 dark:hover:text-primary-300">
                  Launch Simulation →
                </a>
              </div>
            </div>

            <div class="bg-white dark:bg-gray-700 rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition duration-300 transform hover:-translate-y-1">
              <div class="h-48 bg-gradient-to-br from-indigo-400 to-indigo-600 flex items-center justify-center">
                <div class="text-6xl text-white">🔬</div>
              </div>
              <div class="p-6">
                <h3 class="text-xl font-semibold mb-2 text-gray-800 dark:text-gray-200">Advanced Circuit Lab</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-4">
                  Advanced circuit analysis with complex components and comprehensive measurement tools.
                </p>
                <a href="Ohm's Law Circuit Simulator.html" class="inline-flex items-center text-primary-600 dark:text-primary-400 font-medium hover:text-primary-700 dark:hover:text-primary-300">
                  Launch Simulation →
                </a>
              </div>
            </div>

            <!-- Mechanics Experiments -->
            <div class="bg-white dark:bg-gray-700 rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition duration-300 transform hover:-translate-y-1">
              <div class="h-48 bg-gradient-to-br from-purple-400 to-purple-600 flex items-center justify-center">
                <div class="text-6xl text-white">⚖️</div>
              </div>
              <div class="p-6">
                <h3 class="text-xl font-semibold mb-2 text-gray-800 dark:text-gray-200">Pendulum Laboratory</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-4">
                  Explore simple harmonic motion and investigate how pendulum length affects the period of oscillation.
                </p>
                <a href="experiments/pendulum-lab.html" class="inline-flex items-center text-primary-600 dark:text-primary-400 font-medium hover:text-primary-700 dark:hover:text-primary-300">
                  Launch Simulation →
                </a>
              </div>
            </div>

            <div class="bg-white dark:bg-gray-700 rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition duration-300 transform hover:-translate-y-1">
              <div class="h-48 bg-gradient-to-br from-red-400 to-red-600 flex items-center justify-center">
                <div class="text-6xl text-white">🚀</div>
              </div>
              <div class="p-6">
                <h3 class="text-xl font-semibold mb-2 text-gray-800 dark:text-gray-200">Projectile Motion Lab</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-4">
                  Analyze projectile motion with varying angles and velocities to understand trajectory physics.
                </p>
                <a href="experiments/projectile-motion.html" class="inline-flex items-center text-primary-600 dark:text-primary-400 font-medium hover:text-primary-700 dark:hover:text-primary-300">
                  Launch Simulation →
                </a>
              </div>
            </div>

            <div class="bg-white dark:bg-gray-700 rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition duration-300 transform hover:-translate-y-1">
              <div class="h-48 bg-gradient-to-br from-pink-400 to-pink-600 flex items-center justify-center">
                <div class="text-6xl text-white">⚙️</div>
              </div>
              <div class="p-6">
                <h3 class="text-xl font-semibold mb-2 text-gray-800 dark:text-gray-200">Physics Simulations</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-4">
                  Comprehensive physics simulations covering multiple mechanics concepts and principles.
                </p>
                <a href="Physics Simulations.html" class="inline-flex items-center text-primary-600 dark:text-primary-400 font-medium hover:text-primary-700 dark:hover:text-primary-300">
                  Launch Simulation →
                </a>
              </div>
            </div>

            <!-- Thermodynamics Experiments -->
            <div class="bg-white dark:bg-gray-700 rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition duration-300 transform hover:-translate-y-1">
              <div class="h-48 bg-gradient-to-br from-orange-400 to-orange-600 flex items-center justify-center">
                <div class="text-6xl text-white">🌡️</div>
              </div>
              <div class="p-6">
                <h3 class="text-xl font-semibold mb-2 text-gray-800 dark:text-gray-200">Boyle's Law Simulation</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-4">
                  Explore the relationship between pressure and volume in gases through interactive simulations.
                </p>
                <a href="Boyle's Law Simulation.html" class="inline-flex items-center text-primary-600 dark:text-primary-400 font-medium hover:text-primary-700 dark:hover:text-primary-300">
                  Launch Simulation →
                </a>
              </div>
            </div>

            <div class="bg-white dark:bg-gray-700 rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition duration-300 transform hover:-translate-y-1">
              <div class="h-48 bg-gradient-to-br from-yellow-400 to-yellow-600 flex items-center justify-center">
                <div class="text-6xl text-white">📊</div>
              </div>
              <div class="p-6">
                <h3 class="text-xl font-semibold mb-2 text-gray-800 dark:text-gray-200">Gas Laws Laboratory</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-4">
                  Investigate volume-pressure relationships and understand fundamental gas law principles.
                </p>
                <a href="volume, pressure Relationship.html" class="inline-flex items-center text-primary-600 dark:text-primary-400 font-medium hover:text-primary-700 dark:hover:text-primary-300">
                  Launch Simulation →
                </a>
              </div>
            </div>

            <!-- General Science Experiments -->
            <div class="bg-white dark:bg-gray-700 rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition duration-300 transform hover:-translate-y-1">
              <div class="h-48 bg-gradient-to-br from-teal-400 to-teal-600 flex items-center justify-center">
                <div class="text-6xl text-white">🔬</div>
              </div>
              <div class="p-6">
                <h3 class="text-xl font-semibold mb-2 text-gray-800 dark:text-gray-200">Interactive Science Simulations</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-4">
                  Explore multiple physics concepts through interactive simulations and develop scientific observation skills.
                </p>
                <a href="experiments/science-simulations.html" class="inline-flex items-center text-primary-600 dark:text-primary-400 font-medium hover:text-primary-700 dark:hover:text-primary-300">
                  Launch Simulation →
                </a>
              </div>
            </div>

            <div class="bg-white dark:bg-gray-700 rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition duration-300 transform hover:-translate-y-1">
              <div class="h-48 bg-gradient-to-br from-cyan-400 to-cyan-600 flex items-center justify-center">
                <div class="text-6xl text-white">🎲</div>
              </div>
              <div class="p-6">
                <h3 class="text-xl font-semibold mb-2 text-gray-800 dark:text-gray-200">Probability Experiment</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-4">
                  Understand probability concepts through interactive experiments and statistical analysis.
                </p>
                <a href="Probability Experiment.html" class="inline-flex items-center text-primary-600 dark:text-primary-400 font-medium hover:text-primary-700 dark:hover:text-primary-300">
                  Launch Simulation →
                </a>
              </div>
            </div>

            <div class="bg-white dark:bg-gray-700 rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition duration-300 transform hover:-translate-y-1">
              <div class="h-48 bg-gradient-to-br from-emerald-400 to-emerald-600 flex items-center justify-center">
                <div class="text-6xl text-white">🧠</div>
              </div>
              <div class="p-6">
                <h3 class="text-xl font-semibold mb-2 text-gray-800 dark:text-gray-200">Science Explorer</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-4">
                  Comprehensive science exploration platform with multiple interactive learning modules.
                </p>
                <a href="Understanding Science Through Simulations.html" class="inline-flex items-center text-primary-600 dark:text-primary-400 font-medium hover:text-primary-700 dark:hover:text-primary-300">
                  Launch Simulation →
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Technology Stack Section -->
      <section class="py-20">
        <div class="container mx-auto px-6">
          <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-gray-800 dark:text-gray-200 mb-4">Built with Modern Technology</h2>
            <p class="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Powered by cutting-edge web technologies and AI
            </p>
          </div>
          <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8">
            <div class="text-center group">
              <div class="w-16 h-16 mx-auto mb-4 bg-orange-100 dark:bg-orange-900 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <span class="text-2xl">⚛️</span>
              </div>
              <h3 class="font-semibold text-gray-800 dark:text-gray-200">React</h3>
            </div>
            <div class="text-center group">
              <div class="w-16 h-16 mx-auto mb-4 bg-blue-100 dark:bg-blue-900 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <span class="text-2xl">🎨</span>
              </div>
              <h3 class="font-semibold text-gray-800 dark:text-gray-200">Tailwind CSS</h3>
            </div>
            <div class="text-center group">
              <div class="w-16 h-16 mx-auto mb-4 bg-blue-100 dark:bg-blue-900 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <span class="text-2xl">📜</span>
              </div>
              <h3 class="font-semibold text-gray-800 dark:text-gray-200">TypeScript</h3>
            </div>
            <div class="text-center group">
              <div class="w-16 h-16 mx-auto mb-4 bg-purple-100 dark:bg-purple-900 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <span class="text-2xl">⚡</span>
              </div>
              <h3 class="font-semibold text-gray-800 dark:text-gray-200">Vite</h3>
            </div>
            <div class="text-center group">
              <div class="w-16 h-16 mx-auto mb-4 bg-green-100 dark:bg-green-900 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <span class="text-2xl">🤖</span>
              </div>
              <h3 class="font-semibold text-gray-800 dark:text-gray-200">Gemini AI</h3>
            </div>
            <div class="text-center group">
              <div class="w-16 h-16 mx-auto mb-4 bg-yellow-100 dark:bg-yellow-900 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <span class="text-2xl">🌐</span>
              </div>
              <h3 class="font-semibold text-gray-800 dark:text-gray-200">Modern Web</h3>
            </div>
          </div>
        </div>
      </section>

      <!-- Call to Action Section -->
      <section class="py-20 bg-gradient-to-r from-primary-600 to-secondary-600 text-white">
        <div class="container mx-auto px-6 text-center">
          <h2 class="text-4xl font-bold mb-4">Ready to Start Your Physics Journey?</h2>
          <p class="text-xl mb-8 max-w-2xl mx-auto opacity-90">
            Join thousands of students exploring the fascinating world of physics through interactive learning.
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <a href="dashboard.html" class="bg-white text-primary-600 font-bold py-4 px-8 rounded-xl hover:bg-gray-100 transition duration-300 text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1">
              🚀 Access Dashboard
            </a>
            <a href="#features" class="border-2 border-white text-white font-bold py-4 px-8 rounded-xl hover:bg-white hover:text-primary-600 transition duration-300 text-lg">
              📚 Learn More
            </a>
          </div>
        </div>
      </section>
    </main>

    <footer id="main-footer-container" class="bg-gray-200 dark:bg-gray-800 text-gray-700 dark:text-gray-300 py-6 text-center shadow-inner">
      <!-- Footer will be injected here by js/main.js -->
    </footer>

    <script type="module" src="js/main.js"></script>
  <script type="module" src="/index.tsx"></script>
</body>
</html>
