<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Animated Video Library - Physics Virtual Lab</title>
    <meta name="author" content="<PERSON><PERSON> <PERSON>, SUST-BME">
    <meta name="copyright" content="Dr. <PERSON>il - +249912867327, +966538076790">
    <meta name="institution" content="Sudan University of Science and Technology - Biomedical Engineering">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/components.css">
    <style>
        body {
            font-family: var(--font-family-sans);
            margin: 0;
            padding: 0;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .video-header {
            background: linear-gradient(135deg, #ff6b6b, #4ecdc4, #45b7d1);
            color: white;
            padding: var(--spacing-3xl) 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .video-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.1);
            z-index: 1;
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        .video-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: var(--spacing-md);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .video-subtitle {
            font-size: 1.25rem;
            opacity: 0.9;
            max-width: 700px;
            margin: 0 auto;
        }

        .floating-icons {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .floating-icon {
            position: absolute;
            font-size: 2rem;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }

        .floating-icon:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
        .floating-icon:nth-child(2) { top: 20%; right: 15%; animation-delay: 1s; }
        .floating-icon:nth-child(3) { bottom: 30%; left: 20%; animation-delay: 2s; }
        .floating-icon:nth-child(4) { bottom: 20%; right: 10%; animation-delay: 3s; }

        .main-content {
            flex: 1;
            max-width: 1400px;
            margin: 0 auto;
            padding: var(--spacing-xl);
            width: 100%;
        }

        .filter-section {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-md);
            align-items: center;
        }

        [data-theme="dark"] .filter-section {
            background: var(--gray-700);
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .filter-label {
            font-weight: 600;
            color: var(--text-primary);
        }

        .filter-select {
            padding: var(--spacing-sm) var(--spacing-md);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: 0.875rem;
        }

        .search-box {
            flex: 1;
            min-width: 200px;
            padding: var(--spacing-sm) var(--spacing-md);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: 1rem;
        }

        .search-box:focus {
            outline: none;
            border-color: var(--primary-500);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: var(--spacing-xl);
        }

        .video-card {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            transition: all var(--transition-normal);
            position: relative;
        }

        [data-theme="dark"] .video-card {
            background: var(--gray-700);
        }

        .video-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-xl);
        }

        .video-thumbnail {
            position: relative;
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, var(--primary-400), var(--secondary-400));
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .video-thumbnail.mechanics {
            background: linear-gradient(135deg, #22c55e, #16a34a);
        }

        .video-thumbnail.electromagnetism {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        }

        .video-thumbnail.waves {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
        }

        .video-thumbnail.thermodynamics {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }

        .video-thumbnail.modern {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }

        .thumbnail-icon {
            font-size: 4rem;
            color: white;
            opacity: 0.8;
        }

        .play-button {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .play-button:hover {
            background: white;
            transform: translate(-50%, -50%) scale(1.1);
        }

        .play-icon {
            font-size: 1.5rem;
            color: var(--primary-600);
            margin-left: 4px;
        }

        .video-duration {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-size: 0.75rem;
            font-weight: 500;
        }

        .video-info {
            padding: var(--spacing-lg);
        }

        .video-title-card {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
            line-height: 1.4;
        }

        .video-description {
            color: var(--text-secondary);
            font-size: 0.875rem;
            line-height: 1.5;
            margin-bottom: var(--spacing-md);
        }

        .video-meta {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
        }

        .meta-tag {
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-size: 0.75rem;
            font-weight: 500;
        }

        .difficulty-beginner {
            background: var(--beginner-color);
            color: white;
        }

        .difficulty-intermediate {
            background: var(--intermediate-color);
            color: white;
        }

        .difficulty-advanced {
            background: var(--advanced-color);
            color: white;
        }

        .category-tag {
            background: var(--accent-100);
            color: var(--accent-700);
        }

        [data-theme="dark"] .category-tag {
            background: var(--accent-900);
            color: var(--accent-300);
        }

        .video-actions {
            display: flex;
            gap: var(--spacing-sm);
        }

        .action-btn {
            flex: 1;
            padding: var(--spacing-sm) var(--spacing-md);
            border: none;
            border-radius: var(--radius-md);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-fast);
            text-decoration: none;
            text-align: center;
            font-size: 0.875rem;
        }

        .btn-watch {
            background: var(--primary-600);
            color: white;
        }

        .btn-watch:hover {
            background: var(--primary-700);
        }

        .btn-experiment {
            background: var(--accent-600);
            color: white;
        }

        .btn-experiment:hover {
            background: var(--accent-700);
        }

        .featured-section {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            padding: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
        }

        [data-theme="dark"] .featured-section {
            background: var(--gray-700);
        }

        .featured-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-lg);
            text-align: center;
        }

        .featured-video {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-xl);
            align-items: center;
        }

        .featured-thumbnail {
            width: 100%;
            height: 250px;
            background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .featured-content h3 {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
        }

        .featured-content p {
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: var(--spacing-lg);
        }

        .featured-actions {
            display: flex;
            gap: var(--spacing-md);
        }

        .stats-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .stat-card {
            background: white;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            padding: var(--spacing-lg);
            text-align: center;
        }

        [data-theme="dark"] .stat-card {
            background: var(--gray-700);
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: var(--spacing-sm);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-600);
            margin-bottom: var(--spacing-xs);
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        @media (max-width: 768px) {
            .video-grid {
                grid-template-columns: 1fr;
            }
            
            .featured-video {
                grid-template-columns: 1fr;
            }
            
            .filter-section {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-box {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <header id="main-header"></header>

    <div class="video-header animate-fade-in">
        <div class="floating-icons">
            <div class="floating-icon">🎬</div>
            <div class="floating-icon">⚡</div>
            <div class="floating-icon">🌊</div>
            <div class="floating-icon">🔬</div>
        </div>
        <div class="header-content">
            <h1 class="video-title">🎬 Animated Video Library</h1>
            <p class="video-subtitle">
                Explore physics concepts through engaging animated videos that bring complex theories to life
            </p>
        </div>
    </div>

    <div class="main-content">
        <div class="stats-section animate-slide-up">
            <div class="stat-card">
                <div class="stat-icon">🎥</div>
                <div class="stat-value">24</div>
                <div class="stat-label">Animated Videos</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">⏱️</div>
                <div class="stat-value">6.5</div>
                <div class="stat-label">Hours of Content</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🎯</div>
                <div class="stat-value">12</div>
                <div class="stat-label">Physics Topics</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🏆</div>
                <div class="stat-value">95%</div>
                <div class="stat-label">Student Satisfaction</div>
            </div>
        </div>

        <div class="featured-section animate-slide-up animate-stagger-1">
            <h2 class="featured-title">🌟 Featured Video</h2>
            <div class="featured-video">
                <div class="featured-thumbnail">
                    <div class="thumbnail-icon">⚡</div>
                    <div class="play-button" onclick="openVideoModal('ohms-law-intro')">
                        <div class="play-icon">▶</div>
                    </div>
                    <div class="video-duration">8:45</div>
                </div>
                <div class="featured-content">
                    <h3>Understanding Ohm's Law: The Foundation of Electronics</h3>
                    <p>
                        Discover the fundamental relationship between voltage, current, and resistance through 
                        beautiful animations and real-world examples. This comprehensive introduction sets the 
                        foundation for understanding electrical circuits.
                    </p>
                    <div class="featured-actions">
                        <button class="action-btn btn-watch" onclick="openVideoModal('ohms-law-intro')">
                            Watch Video
                        </button>
                        <a href="experiments/ohms-law.html" class="action-btn btn-experiment">
                            Try Experiment
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="filter-section animate-slide-up animate-stagger-2">
            <div class="filter-group">
                <label class="filter-label">Category:</label>
                <select class="filter-select" id="categoryFilter">
                    <option value="all">All Topics</option>
                    <option value="mechanics">Mechanics</option>
                    <option value="electromagnetism">Electromagnetism</option>
                    <option value="waves">Waves & Oscillations</option>
                    <option value="thermodynamics">Thermodynamics</option>
                    <option value="modern">Modern Physics</option>
                </select>
            </div>
            <div class="filter-group">
                <label class="filter-label">Difficulty:</label>
                <select class="filter-select" id="difficultyFilter">
                    <option value="all">All Levels</option>
                    <option value="beginner">Beginner</option>
                    <option value="intermediate">Intermediate</option>
                    <option value="advanced">Advanced</option>
                </select>
            </div>
            <input type="text" class="search-box" id="searchBox" placeholder="Search videos...">
        </div>

        <div class="video-grid" id="videoGrid">
            <!-- Videos will be dynamically loaded here -->
        </div>
    </div>

    <footer id="main-footer-container"></footer>

    <script type="module" src="js/main.js"></script>
    <script src="js/video-library.js"></script>
</body>
</html>
