<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Electric Field Explorer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: #f0f0f0;
            color: #333;
            line-height: 1.6;
        }
        .container {
            width: 95%;
            max-width: 1000px; /* Max width for the whole app */
            margin-top: 10px;
            margin-bottom: 20px;
        }
        h1 {
            text-align: center;
            color: #0056b3; /* A slightly darker blue for the main title */
        }
        #controls {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 15px;
            padding: 10px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            justify-content: center;
        }
        #controls button, #controls select {
            padding: 8px 12px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background-color: #e9e9e9;
            cursor: pointer;
            font-size: 0.9em;
            transition: background-color 0.2s ease;
        }
        #controls button:hover, #controls select:hover {
            background-color: #ddd;
        }
        #controls button.active {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }
        #canvas-container {
            width: 100%;
            margin-bottom: 15px;
            display: flex; 
            justify-content: center;
        }
        canvas {
            border: 1px solid #999;
            background-color: #fff;
            display: block; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            cursor: crosshair;
        }
        #info-panel {
            display: flex;
            flex-direction: column; 
            gap: 15px;
            width: 100%;
        }
        #probe-info {
            font-size: 1em; /* Slightly larger for better readability */
            padding: 12px;
            background-color: #e6f7ff; /* Light blue background for probe info */
            border: 1px solid #b3e0ff; /* Border for probe info */
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .explanation {
            padding: 15px; /* More padding for explanations */
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .explanation h3 {
            margin-top: 0;
            margin-bottom: 10px;
            color: #0056b3;
        }
        .explanation ul {
            padding-left: 20px;
            margin-bottom: 0;
        }
        footer {
            margin-top: 20px;
            padding: 10px;
            font-size: 0.8em;
            text-align: center;
            color: #666;
            width: 100%;
        }

        /* Mobile adjustments */
        @media (max-width: 600px) {
            .container {
                width: 100%;
                margin-top: 0;
                padding-left: 5px;
                padding-right: 5px;
                box-sizing: border-box;
            }
            h1 {
                font-size: 1.5em;
            }
            #controls {
                flex-direction: column;
                align-items: stretch; 
            }
            #controls button, #controls select {
                width: 100%;
                box-sizing: border-box; 
            }
             #canvas-container {
                margin-bottom: 10px;
            }
            #info-panel {
                gap: 10px;
            }
            .explanation {
                padding: 10px;
            }
        }

    </style>
</head>
<body>
    <div class="container">
        <h1>Electric Field Explorer</h1>

        <div id="controls">
            <button id="addPositiveBtn" title="Select to add positive charges">Add Positive Charge (+)</button>
            <button id="addNegativeBtn" title="Select to add negative charges">Add Negative Charge (-)</button>
            <button id="placeProbeBtn" title="Select to place or move the test probe">Place Probe (P)</button>
            <select id="presetSelector" title="Choose a predefined charge arrangement">
                <option value="">Custom Arrangement</option>
                <option value="dipole">Dipole</option>
                <option value="quadrupole">Quadrupole (linear: + - +)</option>
                <option value="two_positive">Two Positive Charges</option>
                <option value="line_of_charge">Line of Positive Charges</option>
                <option value="empty_square">Square of Charges (+ - + -)</option>
            </select>
            <button id="clearAllBtn" title="Remove all charges and the probe">Clear All</button>
        </div>

        <div id="canvas-container">
            <canvas id="mainCanvas"></canvas>
        </div>
        
        <div id="info-panel">
            <div id="probe-info">
                Probe Potential: N/A<br>
                Probe Force Magnitude: N/A
            </div>
            <div class="explanation">
                <h3>What is an Electric Field?</h3>
                <p>An electric field is a region of space around electrically charged particles or objects in which other charged particles would feel a force. It's a vector field, meaning it has both a magnitude (strength) and a direction at every point. Field lines are used to visualize this:
                    <ul>
                        <li>They originate from positive charges and terminate on negative charges (or extend to infinity).</li>
                        <li>The direction of the field line at any point is the direction of the force on a small positive test charge.</li>
                        <li>The density of lines (how close they are) indicates the strength of the field – closer lines mean a stronger field.</li>
                    </ul>
                </p>
            </div>
            <div class="explanation">
                <h3>What is Electric Potential?</h3>
                <p>Electric potential (or voltage) at a point in an electric field is the amount of electric potential energy per unit of charge that a small positive test charge would have at that point. It's a scalar quantity (it has magnitude but no direction).
                    <ul>
                        <li>It represents the work done to move a unit positive charge from a reference point (often infinity) to that specific point against the electric field.</li>
                        <li>Positive charges create positive potential around them, and negative charges create negative potential.</li>
                        <li>Differences in electric potential drive the flow of electric current.</li>
                    </ul>
                </p>
            </div>
        </div>
    </div>

    <footer>
        Interactive Electric Field Simulator. Click on the canvas to place items.
    </footer>

    <script>
        const canvas = document.getElementById('mainCanvas');
        const ctx = canvas.getContext('2d');
        
        const probeInfoDiv = document.getElementById('probe-info');

        // --- Configuration Constants ---
        const K_COULOMB = 1; 
        const DEFAULT_CHARGE_MAGNITUDE = 20; 
        const CHARGE_RADIUS_VISUAL = 8; 
        const PROBE_RADIUS_VISUAL = 5;  
        
        const NUM_FIELD_LINES_PER_SOURCE = 16;
        const FIELD_LINE_STEP_LENGTH_PX = 7;
        const MAX_FIELD_LINE_STEPS_COUNT = 300; 
        const FIELD_LINE_ARROW_INTERVAL = 25; // Draw arrow every N steps along a line
        const MIN_E_FIELD_MAGNITUDE_FOR_LINE = 0.01; 
        const MIN_DIST_SQ_FOR_FIELD_CALC = (CHARGE_RADIUS_VISUAL * 0.5)**2; // Min distance squared

        const POSITIVE_CHARGE_COLOR = 'rgb(255, 99, 71)'; 
        const NEGATIVE_CHARGE_COLOR = 'rgb(65, 105, 225)'; 
        const PROBE_COLOR = 'rgb(50, 205, 50)'; 
        const FORCE_ARROW_COLOR = 'rgb(128, 0, 128)'; 
        const FIELD_LINE_COLOR = 'rgba(50, 50, 50, 0.6)';

        // --- State Variables ---
        let charges = []; 
        let probe = null; 
        let currentMode = 'addPositive'; 
        
        const baseCanvasWidth = 800;
        const baseCanvasHeight = 600;

        // --- Initialization and Canvas Sizing ---
        function setupCanvas() {
            const container = document.getElementById('canvas-container');
            let containerWidth = container.clientWidth;
            
            let targetWidth = Math.min(containerWidth, baseCanvasWidth); 
            targetWidth = Math.max(300, targetWidth); 

            canvas.width = targetWidth;
            canvas.height = targetWidth * (baseCanvasHeight / baseCanvasWidth); 
            
            updateButtonStyles();
            loadPreset('dipole'); // Start with a default preset
            // redrawAll(); // loadPreset calls redrawAll
        }

        function handleResize() {
            const oldCw = canvas.width;
            const oldCh = canvas.height;

            const container = document.getElementById('canvas-container');
            let containerWidth = container.clientWidth;
            let targetWidth = Math.min(containerWidth, baseCanvasWidth);
            targetWidth = Math.max(300, targetWidth);

            const newCw = targetWidth;
            const newCh = targetWidth * (baseCanvasHeight / baseCanvasWidth);

            if (oldCw > 0 && oldCh > 0 && (Math.abs(newCw - oldCw) > 1 || Math.abs(newCh - oldCh) > 1)) {
                const scaleX = newCw / oldCw;
                const scaleY = newCh / oldCh;

                charges.forEach(c => {
                    c.x = c.x * scaleX;
                    c.y = c.y * scaleY;
                });
                if (probe) {
                    probe.x = probe.x * scaleX;
                    probe.y = probe.y * scaleY;
                }
                canvas.width = newCw;
                canvas.height = newCh;
                redrawAll();
            } else if (oldCw === 0 || oldCh === 0) { 
                canvas.width = newCw;
                canvas.height = newCh;
                redrawAll();
            }
        }

        // --- Event Listeners ---
        document.getElementById('addPositiveBtn').addEventListener('click', () => setMode('addPositive'));
        document.getElementById('addNegativeBtn').addEventListener('click', () => setMode('addNegative'));
        document.getElementById('placeProbeBtn').addEventListener('click', () => setMode('placeProbe'));
        document.getElementById('clearAllBtn').addEventListener('click', clearAll);
        document.getElementById('presetSelector').addEventListener('change', (e) => loadPreset(e.target.value));

        canvas.addEventListener('click', handleCanvasClick);
        window.addEventListener('load', setupCanvas);
        window.addEventListener('resize', handleResize);

        // --- Mode Handling ---
        function setMode(mode) {
            currentMode = mode;
            updateButtonStyles();
        }

        function updateButtonStyles() {
            document.querySelectorAll('#controls button[id$="Btn"]').forEach(btn => btn.classList.remove('active'));
            if (currentMode === 'addPositive') document.getElementById('addPositiveBtn').classList.add('active');
            else if (currentMode === 'addNegative') document.getElementById('addNegativeBtn').classList.add('active');
            else if (currentMode === 'placeProbe') document.getElementById('placeProbeBtn').classList.add('active');
        }

        // --- Core Logic ---
        function handleCanvasClick(event) {
            const rect = canvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;

            if (currentMode === 'addPositive') {
                charges.push({ x, y, q: DEFAULT_CHARGE_MAGNITUDE, type: 'positive' });
            } else if (currentMode === 'addNegative') {
                charges.push({ x, y, q: -DEFAULT_CHARGE_MAGNITUDE, type: 'negative' });
            } else if (currentMode === 'placeProbe') {
                probe = { x, y };
            }
            if(currentMode === 'addPositive' || currentMode === 'addNegative'){
                 document.getElementById('presetSelector').value = ""; // Reset if custom charge added
            }
            redrawAll();
        }

        function clearAll() {
            charges = [];
            probe = null;
            document.getElementById('presetSelector').value = "";
            redrawAll();
        }

        function loadPreset(presetName) {
            charges = [];
            probe = null; 
            const cw = canvas.width;
            const ch = canvas.height;

            // Ensure preset selector reflects the choice, especially if called programmatically
            const selector = document.getElementById('presetSelector');
            if (selector.value !== presetName) {
                selector.value = presetName;
            }

            switch (presetName) {
                case 'dipole':
                    charges.push({ x: cw / 2 - 60, y: ch / 2, q: DEFAULT_CHARGE_MAGNITUDE, type: 'positive' });
                    charges.push({ x: cw / 2 + 60, y: ch / 2, q: -DEFAULT_CHARGE_MAGNITUDE, type: 'negative' });
                    break;
                case 'quadrupole': 
                    charges.push({ x: cw / 2 - 70, y: ch / 2, q: DEFAULT_CHARGE_MAGNITUDE, type: 'positive' });
                    charges.push({ x: cw / 2, y: ch / 2, q: -DEFAULT_CHARGE_MAGNITUDE, type: 'negative' }); 
                    charges.push({ x: cw / 2 + 70, y: ch / 2, q: DEFAULT_CHARGE_MAGNITUDE, type: 'positive' });
                    break;
                case 'two_positive':
                     charges.push({ x: cw / 2 - 60, y: ch / 2, q: DEFAULT_CHARGE_MAGNITUDE, type: 'positive' });
                    charges.push({ x: cw / 2 + 60, y: ch / 2, q: DEFAULT_CHARGE_MAGNITUDE, type: 'positive' });
                    break;
                case 'line_of_charge':
                    for (let i = 0; i < 5; i++) {
                        charges.push({ x: cw / 2 - 80 + i * 40, y: ch / 2, q: DEFAULT_CHARGE_MAGNITUDE, type: 'positive' });
                    }
                    break;
                case 'empty_square': 
                    const d = Math.min(cw, ch) / 6; // Scale square size with canvas
                    charges.push({ x: cw/2 - d, y: ch/2 - d, q: DEFAULT_CHARGE_MAGNITUDE, type: 'positive'});
                    charges.push({ x: cw/2 + d, y: ch/2 - d, q: -DEFAULT_CHARGE_MAGNITUDE, type: 'negative'});
                    charges.push({ x: cw/2 - d, y: ch/2 + d, q: DEFAULT_CHARGE_MAGNITUDE, type: 'positive'}); // Corrected this to positive
                    charges.push({ x: cw/2 + d, y: ch/2 + d, q: -DEFAULT_CHARGE_MAGNITUDE, type: 'negative'});
                    break;
            }
            redrawAll();
        }
        
        function calculateElectricFieldAt(px, py, excludeCharge = null) {
            let Ex = 0;
            let Ey = 0;
            charges.forEach(charge => {
                if (charge === excludeCharge) return;
                const dx = px - charge.x;
                const dy = py - charge.y;
                let rSq = dx * dx + dy * dy;
                
                if (rSq < MIN_DIST_SQ_FOR_FIELD_CALC) rSq = MIN_DIST_SQ_FOR_FIELD_CALC; 
                
                const r = Math.sqrt(rSq);
                if (r === 0) return; // Should be caught by MIN_DIST_SQ but as a safeguard
                const magnitude = K_COULOMB * charge.q / rSq; 
                
                Ex += magnitude * (dx / r);
                Ey += magnitude * (dy / r);
            });
            return { x: Ex, y: Ey };
        }

        function calculatePotentialAt(px, py) {
            let potential = 0;
            charges.forEach(charge => {
                const dx = px - charge.x;
                const dy = py - charge.y;
                let r = Math.sqrt(dx * dx + dy * dy);
                r = Math.max(r, CHARGE_RADIUS_VISUAL * 0.8); // Min distance for potential calculation
                if (r === 0) return Infinity * Math.sign(charge.q); // Technically, but avoided by Math.max
                
                potential += K_COULOMB * charge.q / r; 
            });
            return potential;
        }

        // --- Drawing Functions ---
        function redrawAll() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawFieldLines();
            drawCharges();
            if (probe) {
                drawProbe();
                drawForceOnProbe();
                displayProbeInfo();
            } else {
                probeInfoDiv.innerHTML = "Probe Potential: N/A<br>Probe Force Magnitude: N/A";
            }
        }

        function drawCharges() {
            charges.forEach(charge => {
                ctx.beginPath();
                ctx.arc(charge.x, charge.y, CHARGE_RADIUS_VISUAL, 0, 2 * Math.PI);
                ctx.fillStyle = charge.type === 'positive' ? POSITIVE_CHARGE_COLOR : NEGATIVE_CHARGE_COLOR;
                ctx.fill();
                
                ctx.fillStyle = 'white';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.font = `bold ${CHARGE_RADIUS_VISUAL * 1.5}px Arial`;
                ctx.fillText(charge.type === 'positive' ? '+' : '–', charge.x, charge.y + 1); // Using en-dash for minus
            });
        }

        function drawProbe() {
            if (!probe) return;
            ctx.beginPath();
            ctx.arc(probe.x, probe.y, PROBE_RADIUS_VISUAL, 0, 2 * Math.PI);
            ctx.fillStyle = PROBE_COLOR;
            ctx.fill();
            ctx.strokeStyle = 'darkgreen';
            ctx.lineWidth = 1.5;
            ctx.stroke();
            ctx.lineWidth = 1; // Reset
        }

        function drawFieldLines() {
            ctx.strokeStyle = FIELD_LINE_COLOR;
            ctx.lineWidth = 1;

            charges.forEach(charge => {
                if (charge.q > 0) { 
                    for (let i = 0; i < NUM_FIELD_LINES_PER_SOURCE; i++) {
                        const angle = (2 * Math.PI * i) / NUM_FIELD_LINES_PER_SOURCE;
                        let currentX = charge.x + (CHARGE_RADIUS_VISUAL + 1) * Math.cos(angle);
                        let currentY = charge.y + (CHARGE_RADIUS_VISUAL + 1) * Math.sin(angle);
                        
                        ctx.beginPath();
                        ctx.moveTo(currentX, currentY);

                        for (let step = 0; step < MAX_FIELD_LINE_STEPS_COUNT; step++) {
                            // Pass the source charge to exclude its effect if current point is extremely close,
                            // though starting outside radius mostly handles this.
                            const field = calculateElectricFieldAt(currentX, currentY, null); // Calculate field from ALL charges
                            const fieldMag = Math.sqrt(field.x * field.x + field.y * field.y);

                            if (fieldMag < MIN_E_FIELD_MAGNITUDE_FOR_LINE) break; 

                            const dx = (field.x / fieldMag) * FIELD_LINE_STEP_LENGTH_PX;
                            const dy = (field.y / fieldMag) * FIELD_LINE_STEP_LENGTH_PX;
                            
                            const prevX = currentX;
                            const prevY = currentY;
                            currentX += dx;
                            currentY += dy;
                            ctx.lineTo(currentX, currentY);

                            if (step > 0 && step % FIELD_LINE_ARROW_INTERVAL === 0) {
                                drawArrowhead(ctx, prevX, prevY, currentX, currentY, 6, FIELD_LINE_COLOR);
                            }

                            let terminated = false;
                            for (const sink of charges) {
                                if (sink.q < 0) {
                                    const distToSinkSq = (currentX - sink.x)**2 + (currentY - sink.y)**2;
                                    if (distToSinkSq < (CHARGE_RADIUS_VISUAL * 1.5)**2) { 
                                        ctx.lineTo(sink.x, sink.y);
                                        terminated = true;
                                        break;
                                    }
                                }
                            }
                            if (terminated) break;

                            if (currentX < 0 || currentX > canvas.width || currentY < 0 || currentY > canvas.height) {
                                if (step > FIELD_LINE_ARROW_INTERVAL / 2) { // Draw final arrow if line is reasonably long
                                   drawArrowhead(ctx, prevX, prevY, currentX, currentY, 6, FIELD_LINE_COLOR);
                                }
                                break;
                            }
                        }
                        ctx.stroke();
                    }
                }
            });
        }
        
        function drawArrowhead(ctx, fromX, fromY, toX, toY, headLength = 8, color) {
            const angle = Math.atan2(toY - fromY, toX - fromX);
            const originalFillStyle = ctx.fillStyle;
            
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headLength * Math.cos(angle - Math.PI / 7), toY - headLength * Math.sin(angle - Math.PI / 7));
            ctx.lineTo(toX - headLength * Math.cos(angle + Math.PI / 7), toY - headLength * Math.sin(angle + Math.PI / 7));
            ctx.closePath();
            ctx.fill();
            
            ctx.fillStyle = originalFillStyle;
        }

        function drawForceOnProbe() {
            if (!probe) return;
            const force = calculateElectricFieldAt(probe.x, probe.y); 
            const forceMag = Math.sqrt(force.x * force.x + force.y * force.y);

            if (forceMag < 0.001) return; // Don't draw if force is negligible

            let arrowDisplayLength = forceMag * 3; // Adjusted scale factor for force vector
            arrowDisplayLength = Math.min(arrowDisplayLength, Math.min(canvas.width, canvas.height) / 8); // Max length relative to canvas
            arrowDisplayLength = Math.max(arrowDisplayLength, PROBE_RADIUS_VISUAL * 2);   

            const dirX = force.x / forceMag;
            const dirY = force.y / forceMag;

            const arrowEndX = probe.x + dirX * arrowDisplayLength;
            const arrowEndY = probe.y + dirY * arrowDisplayLength;
            
            ctx.beginPath();
            ctx.moveTo(probe.x, probe.y);
            ctx.lineTo(arrowEndX, arrowEndY);
            ctx.strokeStyle = FORCE_ARROW_COLOR;
            ctx.lineWidth = 2.5; // Thicker line for force vector
            ctx.stroke();
            drawArrowhead(ctx, probe.x, probe.y, arrowEndX, arrowEndY, 10, FORCE_ARROW_COLOR);
            ctx.lineWidth = 1; 
        }

        function displayProbeInfo() {
            if (!probe) {
                probeInfoDiv.innerHTML = "Probe Potential: N/A<br>Probe Force Magnitude: N/A";
                return;
            }
            const potential = calculatePotentialAt(probe.x, probe.y);
            const force = calculateElectricFieldAt(probe.x, probe.y); 
            const forceMag = Math.sqrt(force.x * force.x + force.y * force.y);

            probeInfoDiv.innerHTML = `Probe Potential: ${potential.toFixed(2)} (arbitrary units)<br>
                                      Probe Force Magnitude: ${forceMag.toFixed(3)} (arbitrary units)`;
        }

    </script>
</body>
</html>
